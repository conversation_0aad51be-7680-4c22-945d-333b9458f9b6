# Supabase Configuration (Demo/Development)
# For production, replace these with your actual Supabase project credentials
NEXT_PUBLIC_SUPABASE_URL=https://demo.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=demo_anon_key
SUPABASE_SERVICE_ROLE_KEY=demo_service_role_key

# Database Configuration
DATABASE_URL=postgresql://demo:demo@localhost:5432/pos_pro

# Authentication
NEXTAUTH_SECRET=your_nextauth_secret_key_here
NEXTAUTH_URL=http://localhost:3000

# Payment Processing (Demo keys)
STRIPE_SECRET_KEY=sk_test_demo_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_demo_key

# Map Services (Demo keys)
NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN=demo_mapbox_token
NEXT_PUBLIC_OPENSTREETMAP_API_KEY=demo_osm_api_key

# External APIs (Demo keys)
EXCHANGE_RATE_API_KEY=demo_exchange_rate_key
SMS_API_KEY=demo_sms_key
EMAIL_API_KEY=demo_email_key

# Application Settings
NEXT_PUBLIC_APP_NAME=POS Pro
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_COMPANY_NAME=Your Company Name

# Development
NODE_ENV=development
