module.exports = {

"[project]/.next-internal/server/app/api/dashboard/stats/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/mock-data.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Mock data for development when Supabase is not configured
__turbopack_context__.s({
    "createMockApiResponse": (()=>createMockApiResponse),
    "createMockPaginatedResponse": (()=>createMockPaginatedResponse),
    "mockCustomers": (()=>mockCustomers),
    "mockDashboardStats": (()=>mockDashboardStats),
    "mockOrders": (()=>mockOrders),
    "mockProducts": (()=>mockProducts)
});
const mockProducts = [
    {
        id: 'prod-1',
        name: 'Espresso',
        description: 'Strong coffee shot',
        category_id: 'cat-1',
        sku: 'BEV-ESP-001',
        barcode: '1234567890',
        price: 2.50,
        cost: 0.75,
        unit: 'cup',
        is_manufactured: true,
        recipe_id: 'recipe-1',
        image_url: null,
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    },
    {
        id: 'prod-2',
        name: 'Cappuccino',
        description: 'Espresso with steamed milk',
        category_id: 'cat-1',
        sku: 'BEV-CAP-001',
        barcode: '1234567891',
        price: 4.50,
        cost: 1.25,
        unit: 'cup',
        is_manufactured: true,
        recipe_id: 'recipe-2',
        image_url: null,
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    },
    {
        id: 'prod-3',
        name: 'Latte',
        description: 'Espresso with steamed milk and foam',
        category_id: 'cat-1',
        sku: 'BEV-LAT-001',
        barcode: '1234567892',
        price: 5.00,
        cost: 1.50,
        unit: 'cup',
        is_manufactured: true,
        recipe_id: 'recipe-3',
        image_url: null,
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    },
    {
        id: 'prod-4',
        name: 'Caesar Salad',
        description: 'Fresh romaine with caesar dressing',
        category_id: 'cat-2',
        sku: 'APP-CS-001',
        barcode: '1234567893',
        price: 8.50,
        cost: 3.00,
        unit: 'plate',
        is_manufactured: true,
        recipe_id: 'recipe-4',
        image_url: null,
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    },
    {
        id: 'prod-5',
        name: 'Grilled Chicken',
        description: 'Seasoned grilled chicken breast',
        category_id: 'cat-3',
        sku: 'MAIN-GC-001',
        barcode: '1234567894',
        price: 15.00,
        cost: 6.00,
        unit: 'plate',
        is_manufactured: true,
        recipe_id: 'recipe-5',
        image_url: null,
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    },
    {
        id: 'prod-6',
        name: 'Chocolate Cake',
        description: 'Rich chocolate layer cake',
        category_id: 'cat-4',
        sku: 'DES-CC-001',
        barcode: '1234567895',
        price: 6.50,
        cost: 2.50,
        unit: 'slice',
        is_manufactured: true,
        recipe_id: 'recipe-6',
        image_url: null,
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    }
];
const mockCustomers = [
    {
        id: 'cust-1',
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '******-0150',
        address: '123 Customer St, Downtown',
        location: {
            latitude: 40.7127,
            longitude: -74.005
        },
        loyalty_points: 150,
        rating: 4.5,
        notes: 'Regular customer, prefers oat milk',
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    },
    {
        id: 'cust-2',
        name: 'Jane Smith',
        email: '<EMAIL>',
        phone: '******-0151',
        address: '456 Client Ave, Uptown',
        location: {
            latitude: 40.7483,
            longitude: -73.9856
        },
        loyalty_points: 75,
        rating: 4.2,
        notes: null,
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    }
];
const mockOrders = [
    {
        id: 'order-1',
        order_number: 'ORD-001-2024',
        customer_id: 'cust-1',
        branch_id: 'demo-branch-id',
        cashier_id: 'demo-cashier-id',
        sales_rep_id: null,
        table_id: null,
        order_type: 'takeaway',
        status: 'delivered',
        subtotal: 12.00,
        tax_amount: 1.20,
        discount_amount: 0,
        total_amount: 13.20,
        payment_method: 'card',
        payment_status: 'paid',
        notes: null,
        delivery_address: null,
        delivery_location: null,
        estimated_delivery_time: null,
        created_at: '2024-01-15T10:30:00Z',
        updated_at: '2024-01-15T10:45:00Z'
    },
    {
        id: 'order-2',
        order_number: 'ORD-002-2024',
        customer_id: 'cust-2',
        branch_id: 'demo-branch-id',
        cashier_id: 'demo-cashier-id',
        sales_rep_id: null,
        table_id: null,
        order_type: 'dine_in',
        status: 'preparing',
        subtotal: 23.50,
        tax_amount: 2.35,
        discount_amount: 0,
        total_amount: 25.85,
        payment_method: 'cash',
        payment_status: 'paid',
        notes: 'Extra sauce on the side',
        delivery_address: null,
        delivery_location: null,
        estimated_delivery_time: null,
        created_at: '2024-01-15T11:15:00Z',
        updated_at: '2024-01-15T11:20:00Z'
    },
    {
        id: 'order-3',
        order_number: 'ORD-003-2024',
        customer_id: null,
        branch_id: 'demo-branch-id',
        cashier_id: 'demo-cashier-id',
        sales_rep_id: null,
        table_id: null,
        order_type: 'takeaway',
        status: 'pending',
        subtotal: 7.00,
        tax_amount: 0.70,
        discount_amount: 0,
        total_amount: 7.70,
        payment_method: 'digital_wallet',
        payment_status: 'pending',
        notes: null,
        delivery_address: null,
        delivery_location: null,
        estimated_delivery_time: null,
        created_at: '2024-01-15T12:00:00Z',
        updated_at: '2024-01-15T12:00:00Z'
    }
];
const mockDashboardStats = {
    daily_sales: 1250.75,
    daily_orders: 45,
    pending_orders: 3,
    low_stock_items: 2,
    active_tables: 8,
    sales_growth: 12.5,
    sales_by_type: {
        dine_in: 650.25,
        takeaway: 400.50,
        delivery: 200.00,
        field_sales: 0
    },
    top_products: [
        {
            product_id: 'prod-2',
            name: 'Cappuccino',
            sku: 'BEV-CAP-001',
            total_quantity: 25,
            total_revenue: 112.50
        },
        {
            product_id: 'prod-3',
            name: 'Latte',
            sku: 'BEV-LAT-001',
            total_quantity: 20,
            total_revenue: 100.00
        },
        {
            product_id: 'prod-1',
            name: 'Espresso',
            sku: 'BEV-ESP-001',
            total_quantity: 18,
            total_revenue: 45.00
        }
    ],
    hourly_sales: Array.from({
        length: 24
    }, (_, hour)=>({
            hour,
            sales: Math.random() * 100,
            orders: Math.floor(Math.random() * 10)
        }))
};
const createMockApiResponse = (data, success = true, message)=>({
        success,
        data,
        message: message || (success ? 'Success' : 'Error')
    });
const createMockPaginatedResponse = (data, page = 1, limit = 20)=>({
        success: true,
        data,
        pagination: {
            page,
            limit,
            total: data.length,
            totalPages: Math.ceil(data.length / limit)
        }
    });
}}),
"[project]/src/app/api/dashboard/stats/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "OPTIONS": (()=>OPTIONS)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mock-data.ts [app-route] (ecmascript)");
;
;
;
const supabaseUrl = ("TURBOPACK compile-time value", "https://demo.supabase.co") || 'https://demo.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'demo_service_role_key';
// Check if we're in demo mode
const isDemoMode = supabaseUrl === 'https://demo.supabase.co' || supabaseServiceKey === 'demo_service_role_key';
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const branchId = searchParams.get('branch_id');
        const startDate = searchParams.get('start_date') || new Date().toISOString().split('T')[0];
        const endDate = searchParams.get('end_date') || new Date().toISOString().split('T')[0];
        // Return mock data in demo mode
        if ("TURBOPACK compile-time truthy", 1) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                data: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mockDashboardStats"]
            });
        }
        "TURBOPACK unreachable";
        const supabase = undefined;
        // Build base queries with branch filter if provided
        const buildQuery = undefined;
        // Get daily sales
        const dailySales = undefined, salesError = undefined;
        const dailySalesAmount = undefined;
        // Get daily orders count
        const dailyOrdersCount = undefined;
        // Get pending orders count
        const pendingOrdersCount = undefined;
        // Get low stock items count
        let lowStockQuery;
        const lowStockCount = undefined;
        // Get active tables count (for dine-in branches)
        const activeTablesCount = undefined;
        // Calculate sales growth (compare with previous period)
        const previousStartDate = undefined;
        const previousEndDate = undefined;
        const previousSales = undefined;
        const previousSalesAmount = undefined;
        const salesGrowth = undefined;
        // Get sales by order type
        const salesByType = undefined;
        const salesByOrderType = undefined;
        // Get top selling products
        const topProducts = undefined;
        // Get hourly sales for the day
        const hourlySales = undefined;
        const hourlyData = undefined;
        const stats = undefined;
    } catch (error) {
        console.error('Get dashboard stats error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
async function OPTIONS() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](null, {
        status: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
    });
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__295a8035._.js.map