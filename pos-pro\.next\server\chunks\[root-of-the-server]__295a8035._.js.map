{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/lib/mock-data.ts"], "sourcesContent": ["// Mock data for development when Supa<PERSON> is not configured\nimport { Product, Order, Customer, DashboardStats } from '@/types';\n\nexport const mockProducts: Product[] = [\n  {\n    id: 'prod-1',\n    name: 'Espresso',\n    description: 'Strong coffee shot',\n    category_id: 'cat-1',\n    sku: 'BEV-ESP-001',\n    barcode: '1234567890',\n    price: 2.50,\n    cost: 0.75,\n    unit: 'cup',\n    is_manufactured: true,\n    recipe_id: 'recipe-1',\n    image_url: null,\n    is_active: true,\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  },\n  {\n    id: 'prod-2',\n    name: '<PERSON><PERSON><PERSON><PERSON>',\n    description: '<PERSON><PERSON>ress<PERSON> with steamed milk',\n    category_id: 'cat-1',\n    sku: 'BEV-CAP-001',\n    barcode: '1234567891',\n    price: 4.50,\n    cost: 1.25,\n    unit: 'cup',\n    is_manufactured: true,\n    recipe_id: 'recipe-2',\n    image_url: null,\n    is_active: true,\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  },\n  {\n    id: 'prod-3',\n    name: '<PERSON><PERSON>',\n    description: 'Espresso with steamed milk and foam',\n    category_id: 'cat-1',\n    sku: 'BEV-LAT-001',\n    barcode: '1234567892',\n    price: 5.00,\n    cost: 1.50,\n    unit: 'cup',\n    is_manufactured: true,\n    recipe_id: 'recipe-3',\n    image_url: null,\n    is_active: true,\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  },\n  {\n    id: 'prod-4',\n    name: 'Caesar Salad',\n    description: 'Fresh romaine with caesar dressing',\n    category_id: 'cat-2',\n    sku: 'APP-CS-001',\n    barcode: '1234567893',\n    price: 8.50,\n    cost: 3.00,\n    unit: 'plate',\n    is_manufactured: true,\n    recipe_id: 'recipe-4',\n    image_url: null,\n    is_active: true,\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  },\n  {\n    id: 'prod-5',\n    name: 'Grilled Chicken',\n    description: 'Seasoned grilled chicken breast',\n    category_id: 'cat-3',\n    sku: 'MAIN-GC-001',\n    barcode: '1234567894',\n    price: 15.00,\n    cost: 6.00,\n    unit: 'plate',\n    is_manufactured: true,\n    recipe_id: 'recipe-5',\n    image_url: null,\n    is_active: true,\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  },\n  {\n    id: 'prod-6',\n    name: 'Chocolate Cake',\n    description: 'Rich chocolate layer cake',\n    category_id: 'cat-4',\n    sku: 'DES-CC-001',\n    barcode: '1234567895',\n    price: 6.50,\n    cost: 2.50,\n    unit: 'slice',\n    is_manufactured: true,\n    recipe_id: 'recipe-6',\n    image_url: null,\n    is_active: true,\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  },\n];\n\nexport const mockCustomers: Customer[] = [\n  {\n    id: 'cust-1',\n    name: 'John Doe',\n    email: '<EMAIL>',\n    phone: '******-0150',\n    address: '123 Customer St, Downtown',\n    location: { latitude: 40.7127, longitude: -74.005 },\n    loyalty_points: 150,\n    rating: 4.5,\n    notes: 'Regular customer, prefers oat milk',\n    is_active: true,\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  },\n  {\n    id: 'cust-2',\n    name: 'Jane Smith',\n    email: '<EMAIL>',\n    phone: '******-0151',\n    address: '456 Client Ave, Uptown',\n    location: { latitude: 40.7483, longitude: -73.9856 },\n    loyalty_points: 75,\n    rating: 4.2,\n    notes: null,\n    is_active: true,\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  },\n];\n\nexport const mockOrders: Order[] = [\n  {\n    id: 'order-1',\n    order_number: 'ORD-001-2024',\n    customer_id: 'cust-1',\n    branch_id: 'demo-branch-id',\n    cashier_id: 'demo-cashier-id',\n    sales_rep_id: null,\n    table_id: null,\n    order_type: 'takeaway',\n    status: 'delivered',\n    subtotal: 12.00,\n    tax_amount: 1.20,\n    discount_amount: 0,\n    total_amount: 13.20,\n    payment_method: 'card',\n    payment_status: 'paid',\n    notes: null,\n    delivery_address: null,\n    delivery_location: null,\n    estimated_delivery_time: null,\n    created_at: '2024-01-15T10:30:00Z',\n    updated_at: '2024-01-15T10:45:00Z',\n  },\n  {\n    id: 'order-2',\n    order_number: 'ORD-002-2024',\n    customer_id: 'cust-2',\n    branch_id: 'demo-branch-id',\n    cashier_id: 'demo-cashier-id',\n    sales_rep_id: null,\n    table_id: null,\n    order_type: 'dine_in',\n    status: 'preparing',\n    subtotal: 23.50,\n    tax_amount: 2.35,\n    discount_amount: 0,\n    total_amount: 25.85,\n    payment_method: 'cash',\n    payment_status: 'paid',\n    notes: 'Extra sauce on the side',\n    delivery_address: null,\n    delivery_location: null,\n    estimated_delivery_time: null,\n    created_at: '2024-01-15T11:15:00Z',\n    updated_at: '2024-01-15T11:20:00Z',\n  },\n  {\n    id: 'order-3',\n    order_number: 'ORD-003-2024',\n    customer_id: null,\n    branch_id: 'demo-branch-id',\n    cashier_id: 'demo-cashier-id',\n    sales_rep_id: null,\n    table_id: null,\n    order_type: 'takeaway',\n    status: 'pending',\n    subtotal: 7.00,\n    tax_amount: 0.70,\n    discount_amount: 0,\n    total_amount: 7.70,\n    payment_method: 'digital_wallet',\n    payment_status: 'pending',\n    notes: null,\n    delivery_address: null,\n    delivery_location: null,\n    estimated_delivery_time: null,\n    created_at: '2024-01-15T12:00:00Z',\n    updated_at: '2024-01-15T12:00:00Z',\n  },\n];\n\nexport const mockDashboardStats: DashboardStats = {\n  daily_sales: 1250.75,\n  daily_orders: 45,\n  pending_orders: 3,\n  low_stock_items: 2,\n  active_tables: 8,\n  sales_growth: 12.5,\n  sales_by_type: {\n    dine_in: 650.25,\n    takeaway: 400.50,\n    delivery: 200.00,\n    field_sales: 0,\n  },\n  top_products: [\n    {\n      product_id: 'prod-2',\n      name: 'Cappuccino',\n      sku: 'BEV-CAP-001',\n      total_quantity: 25,\n      total_revenue: 112.50,\n    },\n    {\n      product_id: 'prod-3',\n      name: 'Latte',\n      sku: 'BEV-LAT-001',\n      total_quantity: 20,\n      total_revenue: 100.00,\n    },\n    {\n      product_id: 'prod-1',\n      name: 'Espresso',\n      sku: 'BEV-ESP-001',\n      total_quantity: 18,\n      total_revenue: 45.00,\n    },\n  ],\n  hourly_sales: Array.from({ length: 24 }, (_, hour) => ({\n    hour,\n    sales: Math.random() * 100,\n    orders: Math.floor(Math.random() * 10),\n  })),\n};\n\n// Mock API responses\nexport const createMockApiResponse = <T>(data: T, success: boolean = true, message?: string) => ({\n  success,\n  data,\n  message: message || (success ? 'Success' : 'Error'),\n});\n\nexport const createMockPaginatedResponse = <T>(\n  data: T[],\n  page: number = 1,\n  limit: number = 20\n) => ({\n  success: true,\n  data,\n  pagination: {\n    page,\n    limit,\n    total: data.length,\n    totalPages: Math.ceil(data.length / limit),\n  },\n});\n"], "names": [], "mappings": "AAAA,4DAA4D;;;;;;;;;AAGrD,MAAM,eAA0B;IACrC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,KAAK;QACL,SAAS;QACT,OAAO;QACP,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,WAAW;QACX,WAAW;QACX,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,KAAK;QACL,SAAS;QACT,OAAO;QACP,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,WAAW;QACX,WAAW;QACX,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,KAAK;QACL,SAAS;QACT,OAAO;QACP,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,WAAW;QACX,WAAW;QACX,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,KAAK;QACL,SAAS;QACT,OAAO;QACP,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,WAAW;QACX,WAAW;QACX,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,KAAK;QACL,SAAS;QACT,OAAO;QACP,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,WAAW;QACX,WAAW;QACX,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,KAAK;QACL,SAAS;QACT,OAAO;QACP,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,WAAW;QACX,WAAW;QACX,YAAY;QACZ,YAAY;IACd;CACD;AAEM,MAAM,gBAA4B;IACvC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;YAAE,UAAU;YAAS,WAAW,CAAC;QAAO;QAClD,gBAAgB;QAChB,QAAQ;QACR,OAAO;QACP,WAAW;QACX,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;YAAE,UAAU;YAAS,WAAW,CAAC;QAAQ;QACnD,gBAAgB;QAChB,QAAQ;QACR,OAAO;QACP,WAAW;QACX,YAAY;QACZ,YAAY;IACd;CACD;AAEM,MAAM,aAAsB;IACjC;QACE,IAAI;QACJ,cAAc;QACd,aAAa;QACb,WAAW;QACX,YAAY;QACZ,cAAc;QACd,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,iBAAiB;QACjB,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB,OAAO;QACP,kBAAkB;QAClB,mBAAmB;QACnB,yBAAyB;QACzB,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,cAAc;QACd,aAAa;QACb,WAAW;QACX,YAAY;QACZ,cAAc;QACd,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,iBAAiB;QACjB,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB,OAAO;QACP,kBAAkB;QAClB,mBAAmB;QACnB,yBAAyB;QACzB,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,cAAc;QACd,aAAa;QACb,WAAW;QACX,YAAY;QACZ,cAAc;QACd,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,iBAAiB;QACjB,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB,OAAO;QACP,kBAAkB;QAClB,mBAAmB;QACnB,yBAAyB;QACzB,YAAY;QACZ,YAAY;IACd;CACD;AAEM,MAAM,qBAAqC;IAChD,aAAa;IACb,cAAc;IACd,gBAAgB;IAChB,iBAAiB;IACjB,eAAe;IACf,cAAc;IACd,eAAe;QACb,SAAS;QACT,UAAU;QACV,UAAU;QACV,aAAa;IACf;IACA,cAAc;QACZ;YACE,YAAY;YACZ,MAAM;YACN,KAAK;YACL,gBAAgB;YAChB,eAAe;QACjB;QACA;YACE,YAAY;YACZ,MAAM;YACN,KAAK;YACL,gBAAgB;YAChB,eAAe;QACjB;QACA;YACE,YAAY;YACZ,MAAM;YACN,KAAK;YACL,gBAAgB;YAChB,eAAe;QACjB;KACD;IACD,cAAc,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAG,GAAG,CAAC,GAAG,OAAS,CAAC;YACrD;YACA,OAAO,KAAK,MAAM,KAAK;YACvB,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QACrC,CAAC;AACH;AAGO,MAAM,wBAAwB,CAAI,MAAS,UAAmB,IAAI,EAAE,UAAqB,CAAC;QAC/F;QACA;QACA,SAAS,WAAW,CAAC,UAAU,YAAY,OAAO;IACpD,CAAC;AAEM,MAAM,8BAA8B,CACzC,MACA,OAAe,CAAC,EAChB,QAAgB,EAAE,GACf,CAAC;QACJ,SAAS;QACT;QACA,YAAY;YACV;YACA;YACA,OAAO,KAAK,MAAM;YAClB,YAAY,KAAK,IAAI,CAAC,KAAK,MAAM,GAAG;QACtC;IACF,CAAC", "debugId": null}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/app/api/dashboard/stats/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { createClient } from '@supabase/supabase-js';\nimport { Database } from '@/lib/supabase';\nimport { mockDashboardStats } from '@/lib/mock-data';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://demo.supabase.co';\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'demo_service_role_key';\n\n// Check if we're in demo mode\nconst isDemoMode = supabaseUrl === 'https://demo.supabase.co' || supabaseServiceKey === 'demo_service_role_key';\n\n// GET /api/dashboard/stats - Get dashboard statistics\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const branchId = searchParams.get('branch_id');\n    const startDate = searchParams.get('start_date') || new Date().toISOString().split('T')[0];\n    const endDate = searchParams.get('end_date') || new Date().toISOString().split('T')[0];\n\n    // Return mock data in demo mode\n    if (isDemoMode) {\n      return NextResponse.json({\n        success: true,\n        data: mockDashboardStats,\n      });\n    }\n\n    const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey);\n\n    // Build base queries with branch filter if provided\n    const buildQuery = (table: string) => {\n      let query = supabase.from(table);\n      if (branchId) {\n        query = query.eq('branch_id', branchId);\n      }\n      return query;\n    };\n\n    // Get daily sales\n    const { data: dailySales, error: salesError } = await buildQuery('orders')\n      .select('total_amount')\n      .eq('status', 'delivered')\n      .gte('created_at', `${startDate}T00:00:00`)\n      .lte('created_at', `${endDate}T23:59:59`);\n\n    if (salesError) {\n      return NextResponse.json(\n        { error: salesError.message },\n        { status: 400 }\n      );\n    }\n\n    const dailySalesAmount = dailySales?.reduce((sum, order) => sum + order.total_amount, 0) || 0;\n\n    // Get daily orders count\n    const { count: dailyOrdersCount } = await buildQuery('orders')\n      .select('*', { count: 'exact', head: true })\n      .gte('created_at', `${startDate}T00:00:00`)\n      .lte('created_at', `${endDate}T23:59:59`);\n\n    // Get pending orders count\n    const { count: pendingOrdersCount } = await buildQuery('orders')\n      .select('*', { count: 'exact', head: true })\n      .in('status', ['pending', 'confirmed', 'preparing']);\n\n    // Get low stock items count\n    let lowStockQuery = supabase\n      .from('low_stock_items')\n      .select('*', { count: 'exact', head: true });\n    \n    if (branchId) {\n      lowStockQuery = lowStockQuery.eq('branch_id', branchId);\n    }\n\n    const { count: lowStockCount } = await lowStockQuery;\n\n    // Get active tables count (for dine-in branches)\n    const { count: activeTablesCount } = await buildQuery('tables')\n      .select('*', { count: 'exact', head: true })\n      .eq('status', 'occupied');\n\n    // Calculate sales growth (compare with previous period)\n    const previousStartDate = new Date(startDate);\n    previousStartDate.setDate(previousStartDate.getDate() - 1);\n    const previousEndDate = new Date(endDate);\n    previousEndDate.setDate(previousEndDate.getDate() - 1);\n\n    const { data: previousSales } = await buildQuery('orders')\n      .select('total_amount')\n      .eq('status', 'delivered')\n      .gte('created_at', previousStartDate.toISOString().split('T')[0] + 'T00:00:00')\n      .lte('created_at', previousEndDate.toISOString().split('T')[0] + 'T23:59:59');\n\n    const previousSalesAmount = previousSales?.reduce((sum, order) => sum + order.total_amount, 0) || 0;\n    const salesGrowth = previousSalesAmount > 0 \n      ? ((dailySalesAmount - previousSalesAmount) / previousSalesAmount) * 100 \n      : 0;\n\n    // Get sales by order type\n    const { data: salesByType } = await buildQuery('orders')\n      .select('order_type, total_amount')\n      .eq('status', 'delivered')\n      .gte('created_at', `${startDate}T00:00:00`)\n      .lte('created_at', `${endDate}T23:59:59`);\n\n    const salesByOrderType = salesByType?.reduce((acc, order) => {\n      acc[order.order_type] = (acc[order.order_type] || 0) + order.total_amount;\n      return acc;\n    }, {} as Record<string, number>) || {};\n\n    // Get top selling products\n    const { data: topProducts } = await supabase\n      .from('order_items')\n      .select(`\n        product_id,\n        quantity,\n        total_price,\n        products (\n          name,\n          sku\n        ),\n        orders!inner (\n          status,\n          branch_id,\n          created_at\n        )\n      `)\n      .eq('orders.status', 'delivered')\n      .gte('orders.created_at', `${startDate}T00:00:00`)\n      .lte('orders.created_at', `${endDate}T23:59:59`)\n      .then(({ data, error }) => {\n        if (error) return { data: null, error };\n        \n        // Filter by branch if specified\n        const filteredData = branchId \n          ? data?.filter(item => item.orders?.branch_id === branchId)\n          : data;\n\n        // Group by product and calculate totals\n        const productStats = filteredData?.reduce((acc, item) => {\n          const productId = item.product_id;\n          if (!acc[productId]) {\n            acc[productId] = {\n              product_id: productId,\n              name: item.products?.name || 'Unknown',\n              sku: item.products?.sku || '',\n              total_quantity: 0,\n              total_revenue: 0,\n            };\n          }\n          acc[productId].total_quantity += item.quantity;\n          acc[productId].total_revenue += item.total_price;\n          return acc;\n        }, {} as Record<string, any>) || {};\n\n        // Convert to array and sort by quantity\n        return {\n          data: Object.values(productStats)\n            .sort((a: any, b: any) => b.total_quantity - a.total_quantity)\n            .slice(0, 10),\n          error: null\n        };\n      });\n\n    if (topProducts.error) {\n      return NextResponse.json(\n        { error: topProducts.error.message },\n        { status: 400 }\n      );\n    }\n\n    // Get hourly sales for the day\n    const { data: hourlySales } = await buildQuery('orders')\n      .select('created_at, total_amount')\n      .eq('status', 'delivered')\n      .gte('created_at', `${startDate}T00:00:00`)\n      .lte('created_at', `${startDate}T23:59:59`);\n\n    const hourlyData = Array.from({ length: 24 }, (_, hour) => ({\n      hour,\n      sales: 0,\n      orders: 0,\n    }));\n\n    hourlySales?.forEach(order => {\n      const hour = new Date(order.created_at).getHours();\n      hourlyData[hour].sales += order.total_amount;\n      hourlyData[hour].orders += 1;\n    });\n\n    const stats = {\n      daily_sales: dailySalesAmount,\n      daily_orders: dailyOrdersCount || 0,\n      pending_orders: pendingOrdersCount || 0,\n      low_stock_items: lowStockCount || 0,\n      active_tables: activeTablesCount || 0,\n      sales_growth: salesGrowth,\n      sales_by_type: salesByOrderType,\n      top_products: topProducts.data,\n      hourly_sales: hourlyData,\n    };\n\n    return NextResponse.json({\n      success: true,\n      data: stats,\n    });\n\n  } catch (error) {\n    console.error('Get dashboard stats error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA;AAGA;;;;AAEA,MAAM,cAAc,gEAAwC;AAC5D,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB,IAAI;AAEpE,8BAA8B;AAC9B,MAAM,aAAa,gBAAgB,8BAA8B,uBAAuB;AAGjF,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,YAAY,aAAa,GAAG,CAAC,iBAAiB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC1F,MAAM,UAAU,aAAa,GAAG,CAAC,eAAe,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAEtF,gCAAgC;QAChC,wCAAgB;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM,4HAAA,CAAA,qBAAkB;YAC1B;QACF;;QAEA,MAAM;QAEN,oDAAoD;QACpD,MAAM;QAQN,kBAAkB;QAClB,MAAc,wBAAmB;QAajC,MAAM;QAEN,yBAAyB;QACzB,MAAe;QAKf,2BAA2B;QAC3B,MAAe;QAIf,4BAA4B;QAC5B,IAAI;QAQJ,MAAe;QAEf,iDAAiD;QACjD,MAAe;QAIf,wDAAwD;QACxD,MAAM;QAEN,MAAM;QAGN,MAAc;QAMd,MAAM;QACN,MAAM;QAIN,0BAA0B;QAC1B,MAAc;QAMd,MAAM;QAKN,2BAA2B;QAC3B,MAAc;QA4Dd,+BAA+B;QAC/B,MAAc;QAMd,MAAM;QAYN,MAAM;IAiBR,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe;IACpB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,gCAAgC;YAChC,gCAAgC;QAClC;IACF;AACF", "debugId": null}}]}