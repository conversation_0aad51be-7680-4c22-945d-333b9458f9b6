{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/app/api/orders/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { createClient } from '@supabase/supabase-js';\nimport { Database } from '@/lib/supabase';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;\n\n// GET /api/orders/[id] - Get specific order\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const { id } = params;\n    const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey);\n\n    const { data: order, error } = await supabase\n      .from('orders')\n      .select(`\n        *,\n        customers (\n          id,\n          name,\n          phone,\n          email,\n          address\n        ),\n        branches (\n          id,\n          name,\n          address\n        ),\n        users!orders_cashier_id_fkey (\n          id,\n          name\n        ),\n        sales_rep:users!orders_sales_rep_id_fkey (\n          id,\n          name\n        ),\n        tables (\n          id,\n          table_number\n        ),\n        order_items (\n          id,\n          quantity,\n          unit_price,\n          total_price,\n          notes,\n          products (\n            id,\n            name,\n            sku,\n            description\n          )\n        )\n      `)\n      .eq('id', id)\n      .single();\n\n    if (error) {\n      return NextResponse.json(\n        { error: error.message },\n        { status: 404 }\n      );\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: order,\n    });\n\n  } catch (error) {\n    console.error('Get order error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\n// PATCH /api/orders/[id] - Update order\nexport async function PATCH(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const { id } = params;\n    const updateData = await request.json();\n    const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey);\n\n    // Get current order to validate the update\n    const { data: currentOrder, error: fetchError } = await supabase\n      .from('orders')\n      .select('*')\n      .eq('id', id)\n      .single();\n\n    if (fetchError) {\n      return NextResponse.json(\n        { error: 'Order not found' },\n        { status: 404 }\n      );\n    }\n\n    // Validate status transitions\n    if (updateData.status) {\n      const validTransitions: Record<string, string[]> = {\n        pending: ['confirmed', 'cancelled'],\n        confirmed: ['preparing', 'cancelled'],\n        preparing: ['ready', 'cancelled'],\n        ready: ['delivered', 'cancelled'],\n        delivered: [], // Final state\n        cancelled: [], // Final state\n      };\n\n      const allowedNextStates = validTransitions[currentOrder.status] || [];\n      if (!allowedNextStates.includes(updateData.status)) {\n        return NextResponse.json(\n          { error: `Cannot change status from ${currentOrder.status} to ${updateData.status}` },\n          { status: 400 }\n        );\n      }\n    }\n\n    // Update the order\n    const { data: updatedOrder, error: updateError } = await supabase\n      .from('orders')\n      .update({\n        ...updateData,\n        updated_at: new Date().toISOString(),\n      })\n      .eq('id', id)\n      .select(`\n        *,\n        customers (\n          id,\n          name,\n          phone,\n          email\n        ),\n        order_items (\n          id,\n          quantity,\n          unit_price,\n          total_price,\n          notes,\n          products (\n            id,\n            name,\n            sku\n          )\n        )\n      `)\n      .single();\n\n    if (updateError) {\n      return NextResponse.json(\n        { error: updateError.message },\n        { status: 400 }\n      );\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: updatedOrder,\n      message: 'Order updated successfully',\n    });\n\n  } catch (error) {\n    console.error('Update order error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\n// DELETE /api/orders/[id] - Cancel order\nexport async function DELETE(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const { id } = params;\n    const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey);\n\n    // Get current order to validate cancellation\n    const { data: currentOrder, error: fetchError } = await supabase\n      .from('orders')\n      .select('status')\n      .eq('id', id)\n      .single();\n\n    if (fetchError) {\n      return NextResponse.json(\n        { error: 'Order not found' },\n        { status: 404 }\n      );\n    }\n\n    // Only allow cancellation of non-delivered orders\n    if (currentOrder.status === 'delivered') {\n      return NextResponse.json(\n        { error: 'Cannot cancel delivered orders' },\n        { status: 400 }\n      );\n    }\n\n    // Update order status to cancelled\n    const { data: cancelledOrder, error: updateError } = await supabase\n      .from('orders')\n      .update({\n        status: 'cancelled',\n        updated_at: new Date().toISOString(),\n      })\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (updateError) {\n      return NextResponse.json(\n        { error: updateError.message },\n        { status: 400 }\n      );\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: cancelledOrder,\n      message: 'Order cancelled successfully',\n    });\n\n  } catch (error) {\n    console.error('Cancel order error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, PATCH, DELETE, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAGA,MAAM;AACN,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB;AAGzD,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG;QACf,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAY,aAAa;QAErD,MAAM,EAAE,MAAM,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,UACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAuCT,CAAC,EACA,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,MAAM,OAAO;YAAC,GACvB;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,MACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG;QACf,MAAM,aAAa,MAAM,QAAQ,IAAI;QACrC,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAY,aAAa;QAErD,2CAA2C;QAC3C,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACrD,IAAI,CAAC,UACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,YAAY;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkB,GAC3B;gBAAE,QAAQ;YAAI;QAElB;QAEA,8BAA8B;QAC9B,IAAI,WAAW,MAAM,EAAE;YACrB,MAAM,mBAA6C;gBACjD,SAAS;oBAAC;oBAAa;iBAAY;gBACnC,WAAW;oBAAC;oBAAa;iBAAY;gBACrC,WAAW;oBAAC;oBAAS;iBAAY;gBACjC,OAAO;oBAAC;oBAAa;iBAAY;gBACjC,WAAW,EAAE;gBACb,WAAW,EAAE;YACf;YAEA,MAAM,oBAAoB,gBAAgB,CAAC,aAAa,MAAM,CAAC,IAAI,EAAE;YACrE,IAAI,CAAC,kBAAkB,QAAQ,CAAC,WAAW,MAAM,GAAG;gBAClD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO,CAAC,0BAA0B,EAAE,aAAa,MAAM,CAAC,IAAI,EAAE,WAAW,MAAM,EAAE;gBAAC,GACpF;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,mBAAmB;QACnB,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SACtD,IAAI,CAAC,UACL,MAAM,CAAC;YACN,GAAG,UAAU;YACb,YAAY,IAAI,OAAO,WAAW;QACpC,GACC,EAAE,CAAC,MAAM,IACT,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;MAoBT,CAAC,EACA,MAAM;QAET,IAAI,aAAa;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,YAAY,OAAO;YAAC,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG;QACf,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAY,aAAa;QAErD,6CAA6C;QAC7C,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACrD,IAAI,CAAC,UACL,MAAM,CAAC,UACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,YAAY;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkB,GAC3B;gBAAE,QAAQ;YAAI;QAElB;QAEA,kDAAkD;QAClD,IAAI,aAAa,MAAM,KAAK,aAAa;YACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiC,GAC1C;gBAAE,QAAQ;YAAI;QAElB;QAEA,mCAAmC;QACnC,MAAM,EAAE,MAAM,cAAc,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SACxD,IAAI,CAAC,UACL,MAAM,CAAC;YACN,QAAQ;YACR,YAAY,IAAI,OAAO,WAAW;QACpC,GACC,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,aAAa;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,YAAY,OAAO;YAAC,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe;IACpB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,gCAAgC;YAChC,gCAAgC;QAClC;IACF;AACF", "debugId": null}}]}