module.exports = {

"[project]/.next-internal/server/app/api/products/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/src/lib/mock-data.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Mock data for development when Supabase is not configured
__turbopack_context__.s({
    "createMockApiResponse": (()=>createMockApiResponse),
    "createMockPaginatedResponse": (()=>createMockPaginatedResponse),
    "mockCustomers": (()=>mockCustomers),
    "mockDashboardStats": (()=>mockDashboardStats),
    "mockOrders": (()=>mockOrders),
    "mockProducts": (()=>mockProducts)
});
const mockProducts = [
    {
        id: 'prod-1',
        name: 'Espresso',
        description: 'Strong coffee shot',
        category_id: 'cat-1',
        sku: 'BEV-ESP-001',
        barcode: '1234567890',
        price: 2.50,
        cost: 0.75,
        unit: 'cup',
        is_manufactured: true,
        recipe_id: 'recipe-1',
        image_url: null,
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    },
    {
        id: 'prod-2',
        name: 'Cappuccino',
        description: 'Espresso with steamed milk',
        category_id: 'cat-1',
        sku: 'BEV-CAP-001',
        barcode: '1234567891',
        price: 4.50,
        cost: 1.25,
        unit: 'cup',
        is_manufactured: true,
        recipe_id: 'recipe-2',
        image_url: null,
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    },
    {
        id: 'prod-3',
        name: 'Latte',
        description: 'Espresso with steamed milk and foam',
        category_id: 'cat-1',
        sku: 'BEV-LAT-001',
        barcode: '1234567892',
        price: 5.00,
        cost: 1.50,
        unit: 'cup',
        is_manufactured: true,
        recipe_id: 'recipe-3',
        image_url: null,
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    },
    {
        id: 'prod-4',
        name: 'Caesar Salad',
        description: 'Fresh romaine with caesar dressing',
        category_id: 'cat-2',
        sku: 'APP-CS-001',
        barcode: '1234567893',
        price: 8.50,
        cost: 3.00,
        unit: 'plate',
        is_manufactured: true,
        recipe_id: 'recipe-4',
        image_url: null,
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    },
    {
        id: 'prod-5',
        name: 'Grilled Chicken',
        description: 'Seasoned grilled chicken breast',
        category_id: 'cat-3',
        sku: 'MAIN-GC-001',
        barcode: '1234567894',
        price: 15.00,
        cost: 6.00,
        unit: 'plate',
        is_manufactured: true,
        recipe_id: 'recipe-5',
        image_url: null,
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    },
    {
        id: 'prod-6',
        name: 'Chocolate Cake',
        description: 'Rich chocolate layer cake',
        category_id: 'cat-4',
        sku: 'DES-CC-001',
        barcode: '1234567895',
        price: 6.50,
        cost: 2.50,
        unit: 'slice',
        is_manufactured: true,
        recipe_id: 'recipe-6',
        image_url: null,
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    }
];
const mockCustomers = [
    {
        id: 'cust-1',
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '******-0150',
        address: '123 Customer St, Downtown',
        location: {
            latitude: 40.7127,
            longitude: -74.005
        },
        loyalty_points: 150,
        rating: 4.5,
        notes: 'Regular customer, prefers oat milk',
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    },
    {
        id: 'cust-2',
        name: 'Jane Smith',
        email: '<EMAIL>',
        phone: '******-0151',
        address: '456 Client Ave, Uptown',
        location: {
            latitude: 40.7483,
            longitude: -73.9856
        },
        loyalty_points: 75,
        rating: 4.2,
        notes: null,
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    }
];
const mockOrders = [
    {
        id: 'order-1',
        order_number: 'ORD-001-2024',
        customer_id: 'cust-1',
        branch_id: 'demo-branch-id',
        cashier_id: 'demo-cashier-id',
        sales_rep_id: null,
        table_id: null,
        order_type: 'takeaway',
        status: 'delivered',
        subtotal: 12.00,
        tax_amount: 1.20,
        discount_amount: 0,
        total_amount: 13.20,
        payment_method: 'card',
        payment_status: 'paid',
        notes: null,
        delivery_address: null,
        delivery_location: null,
        estimated_delivery_time: null,
        created_at: '2024-01-15T10:30:00Z',
        updated_at: '2024-01-15T10:45:00Z'
    },
    {
        id: 'order-2',
        order_number: 'ORD-002-2024',
        customer_id: 'cust-2',
        branch_id: 'demo-branch-id',
        cashier_id: 'demo-cashier-id',
        sales_rep_id: null,
        table_id: null,
        order_type: 'dine_in',
        status: 'preparing',
        subtotal: 23.50,
        tax_amount: 2.35,
        discount_amount: 0,
        total_amount: 25.85,
        payment_method: 'cash',
        payment_status: 'paid',
        notes: 'Extra sauce on the side',
        delivery_address: null,
        delivery_location: null,
        estimated_delivery_time: null,
        created_at: '2024-01-15T11:15:00Z',
        updated_at: '2024-01-15T11:20:00Z'
    },
    {
        id: 'order-3',
        order_number: 'ORD-003-2024',
        customer_id: null,
        branch_id: 'demo-branch-id',
        cashier_id: 'demo-cashier-id',
        sales_rep_id: null,
        table_id: null,
        order_type: 'takeaway',
        status: 'pending',
        subtotal: 7.00,
        tax_amount: 0.70,
        discount_amount: 0,
        total_amount: 7.70,
        payment_method: 'digital_wallet',
        payment_status: 'pending',
        notes: null,
        delivery_address: null,
        delivery_location: null,
        estimated_delivery_time: null,
        created_at: '2024-01-15T12:00:00Z',
        updated_at: '2024-01-15T12:00:00Z'
    }
];
const mockDashboardStats = {
    daily_sales: 1250.75,
    daily_orders: 45,
    pending_orders: 3,
    low_stock_items: 2,
    active_tables: 8,
    sales_growth: 12.5,
    sales_by_type: {
        dine_in: 650.25,
        takeaway: 400.50,
        delivery: 200.00,
        field_sales: 0
    },
    top_products: [
        {
            product_id: 'prod-2',
            name: 'Cappuccino',
            sku: 'BEV-CAP-001',
            total_quantity: 25,
            total_revenue: 112.50
        },
        {
            product_id: 'prod-3',
            name: 'Latte',
            sku: 'BEV-LAT-001',
            total_quantity: 20,
            total_revenue: 100.00
        },
        {
            product_id: 'prod-1',
            name: 'Espresso',
            sku: 'BEV-ESP-001',
            total_quantity: 18,
            total_revenue: 45.00
        }
    ],
    hourly_sales: Array.from({
        length: 24
    }, (_, hour)=>({
            hour,
            sales: Math.random() * 100,
            orders: Math.floor(Math.random() * 10)
        }))
};
const createMockApiResponse = (data, success = true, message)=>({
        success,
        data,
        message: message || (success ? 'Success' : 'Error')
    });
const createMockPaginatedResponse = (data, page = 1, limit = 20)=>({
        success: true,
        data,
        pagination: {
            page,
            limit,
            total: data.length,
            totalPages: Math.ceil(data.length / limit)
        }
    });
}}),
"[project]/src/app/api/products/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "OPTIONS": (()=>OPTIONS),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mock-data.ts [app-route] (ecmascript)");
;
;
;
const supabaseUrl = ("TURBOPACK compile-time value", "https://demo.supabase.co") || 'https://demo.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'demo_service_role_key';
// Check if we're in demo mode
const isDemoMode = supabaseUrl === 'https://demo.supabase.co' || supabaseServiceKey === 'demo_service_role_key';
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const page = parseInt(searchParams.get('page') || '1');
        const limit = parseInt(searchParams.get('limit') || '20');
        const categoryId = searchParams.get('category_id');
        const search = searchParams.get('search');
        const isActive = searchParams.get('is_active');
        const isManufactured = searchParams.get('is_manufactured');
        // Return mock data in demo mode
        if ("TURBOPACK compile-time truthy", 1) {
            let filteredProducts = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mockProducts"];
            // Apply filters
            if (isActive !== null) {
                filteredProducts = filteredProducts.filter((p)=>p.is_active === (isActive === 'true'));
            }
            if (isManufactured !== null) {
                filteredProducts = filteredProducts.filter((p)=>p.is_manufactured === (isManufactured === 'true'));
            }
            if (search) {
                filteredProducts = filteredProducts.filter((p)=>p.name.toLowerCase().includes(search.toLowerCase()) || p.sku.toLowerCase().includes(search.toLowerCase()));
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createMockPaginatedResponse"])(filteredProducts, page, limit));
        }
        "TURBOPACK unreachable";
        const supabase = undefined;
        // Build query
        let query;
        // Apply pagination
        const from = undefined;
        const to = undefined;
        const products = undefined, error = undefined;
        // Get total count for pagination
        let countQuery;
        const totalCount = undefined;
    } catch (error) {
        console.error('Get products error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const productData = await request.json();
        const { name, description, category_id, sku, barcode, price, cost, unit = 'piece', is_manufactured = false, image_url } = productData;
        // Validation
        if (!name || !category_id || !sku || price === undefined || cost === undefined) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Missing required fields: name, category_id, sku, price, cost'
            }, {
                status: 400
            });
        }
        const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseServiceKey);
        // Check if SKU already exists
        const { data: existingProduct } = await supabase.from('products').select('id').eq('sku', sku).single();
        if (existingProduct) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Product with this SKU already exists'
            }, {
                status: 400
            });
        }
        // Create product
        const { data: product, error: productError } = await supabase.from('products').insert({
            name,
            description: description || null,
            category_id,
            sku,
            barcode: barcode || null,
            price: parseFloat(price),
            cost: parseFloat(cost),
            unit,
            is_manufactured,
            image_url: image_url || null
        }).select(`
        *,
        categories (
          id,
          name
        )
      `).single();
        if (productError) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: productError.message
            }, {
                status: 400
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: product,
            message: 'Product created successfully'
        });
    } catch (error) {
        console.error('Create product error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
async function OPTIONS() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](null, {
        status: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
    });
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__873574dd._.js.map