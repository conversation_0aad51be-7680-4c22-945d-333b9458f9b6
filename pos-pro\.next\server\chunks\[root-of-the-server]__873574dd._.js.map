{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/lib/mock-data.ts"], "sourcesContent": ["// Mock data for development when Supa<PERSON> is not configured\nimport { Product, Order, Customer, DashboardStats } from '@/types';\n\nexport const mockProducts: Product[] = [\n  {\n    id: 'prod-1',\n    name: 'Espresso',\n    description: 'Strong coffee shot',\n    category_id: 'cat-1',\n    sku: 'BEV-ESP-001',\n    barcode: '1234567890',\n    price: 2.50,\n    cost: 0.75,\n    unit: 'cup',\n    is_manufactured: true,\n    recipe_id: 'recipe-1',\n    image_url: null,\n    is_active: true,\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  },\n  {\n    id: 'prod-2',\n    name: '<PERSON><PERSON><PERSON><PERSON>',\n    description: '<PERSON><PERSON>ress<PERSON> with steamed milk',\n    category_id: 'cat-1',\n    sku: 'BEV-CAP-001',\n    barcode: '1234567891',\n    price: 4.50,\n    cost: 1.25,\n    unit: 'cup',\n    is_manufactured: true,\n    recipe_id: 'recipe-2',\n    image_url: null,\n    is_active: true,\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  },\n  {\n    id: 'prod-3',\n    name: '<PERSON><PERSON>',\n    description: 'Espresso with steamed milk and foam',\n    category_id: 'cat-1',\n    sku: 'BEV-LAT-001',\n    barcode: '1234567892',\n    price: 5.00,\n    cost: 1.50,\n    unit: 'cup',\n    is_manufactured: true,\n    recipe_id: 'recipe-3',\n    image_url: null,\n    is_active: true,\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  },\n  {\n    id: 'prod-4',\n    name: 'Caesar Salad',\n    description: 'Fresh romaine with caesar dressing',\n    category_id: 'cat-2',\n    sku: 'APP-CS-001',\n    barcode: '1234567893',\n    price: 8.50,\n    cost: 3.00,\n    unit: 'plate',\n    is_manufactured: true,\n    recipe_id: 'recipe-4',\n    image_url: null,\n    is_active: true,\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  },\n  {\n    id: 'prod-5',\n    name: 'Grilled Chicken',\n    description: 'Seasoned grilled chicken breast',\n    category_id: 'cat-3',\n    sku: 'MAIN-GC-001',\n    barcode: '1234567894',\n    price: 15.00,\n    cost: 6.00,\n    unit: 'plate',\n    is_manufactured: true,\n    recipe_id: 'recipe-5',\n    image_url: null,\n    is_active: true,\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  },\n  {\n    id: 'prod-6',\n    name: 'Chocolate Cake',\n    description: 'Rich chocolate layer cake',\n    category_id: 'cat-4',\n    sku: 'DES-CC-001',\n    barcode: '1234567895',\n    price: 6.50,\n    cost: 2.50,\n    unit: 'slice',\n    is_manufactured: true,\n    recipe_id: 'recipe-6',\n    image_url: null,\n    is_active: true,\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  },\n];\n\nexport const mockCustomers: Customer[] = [\n  {\n    id: 'cust-1',\n    name: 'John Doe',\n    email: '<EMAIL>',\n    phone: '******-0150',\n    address: '123 Customer St, Downtown',\n    location: { latitude: 40.7127, longitude: -74.005 },\n    loyalty_points: 150,\n    rating: 4.5,\n    notes: 'Regular customer, prefers oat milk',\n    is_active: true,\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  },\n  {\n    id: 'cust-2',\n    name: 'Jane Smith',\n    email: '<EMAIL>',\n    phone: '******-0151',\n    address: '456 Client Ave, Uptown',\n    location: { latitude: 40.7483, longitude: -73.9856 },\n    loyalty_points: 75,\n    rating: 4.2,\n    notes: null,\n    is_active: true,\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  },\n];\n\nexport const mockOrders: Order[] = [\n  {\n    id: 'order-1',\n    order_number: 'ORD-001-2024',\n    customer_id: 'cust-1',\n    branch_id: 'demo-branch-id',\n    cashier_id: 'demo-cashier-id',\n    sales_rep_id: null,\n    table_id: null,\n    order_type: 'takeaway',\n    status: 'delivered',\n    subtotal: 12.00,\n    tax_amount: 1.20,\n    discount_amount: 0,\n    total_amount: 13.20,\n    payment_method: 'card',\n    payment_status: 'paid',\n    notes: null,\n    delivery_address: null,\n    delivery_location: null,\n    estimated_delivery_time: null,\n    created_at: '2024-01-15T10:30:00Z',\n    updated_at: '2024-01-15T10:45:00Z',\n  },\n  {\n    id: 'order-2',\n    order_number: 'ORD-002-2024',\n    customer_id: 'cust-2',\n    branch_id: 'demo-branch-id',\n    cashier_id: 'demo-cashier-id',\n    sales_rep_id: null,\n    table_id: null,\n    order_type: 'dine_in',\n    status: 'preparing',\n    subtotal: 23.50,\n    tax_amount: 2.35,\n    discount_amount: 0,\n    total_amount: 25.85,\n    payment_method: 'cash',\n    payment_status: 'paid',\n    notes: 'Extra sauce on the side',\n    delivery_address: null,\n    delivery_location: null,\n    estimated_delivery_time: null,\n    created_at: '2024-01-15T11:15:00Z',\n    updated_at: '2024-01-15T11:20:00Z',\n  },\n  {\n    id: 'order-3',\n    order_number: 'ORD-003-2024',\n    customer_id: null,\n    branch_id: 'demo-branch-id',\n    cashier_id: 'demo-cashier-id',\n    sales_rep_id: null,\n    table_id: null,\n    order_type: 'takeaway',\n    status: 'pending',\n    subtotal: 7.00,\n    tax_amount: 0.70,\n    discount_amount: 0,\n    total_amount: 7.70,\n    payment_method: 'digital_wallet',\n    payment_status: 'pending',\n    notes: null,\n    delivery_address: null,\n    delivery_location: null,\n    estimated_delivery_time: null,\n    created_at: '2024-01-15T12:00:00Z',\n    updated_at: '2024-01-15T12:00:00Z',\n  },\n];\n\nexport const mockDashboardStats: DashboardStats = {\n  daily_sales: 1250.75,\n  daily_orders: 45,\n  pending_orders: 3,\n  low_stock_items: 2,\n  active_tables: 8,\n  sales_growth: 12.5,\n  sales_by_type: {\n    dine_in: 650.25,\n    takeaway: 400.50,\n    delivery: 200.00,\n    field_sales: 0,\n  },\n  top_products: [\n    {\n      product_id: 'prod-2',\n      name: 'Cappuccino',\n      sku: 'BEV-CAP-001',\n      total_quantity: 25,\n      total_revenue: 112.50,\n    },\n    {\n      product_id: 'prod-3',\n      name: 'Latte',\n      sku: 'BEV-LAT-001',\n      total_quantity: 20,\n      total_revenue: 100.00,\n    },\n    {\n      product_id: 'prod-1',\n      name: 'Espresso',\n      sku: 'BEV-ESP-001',\n      total_quantity: 18,\n      total_revenue: 45.00,\n    },\n  ],\n  hourly_sales: Array.from({ length: 24 }, (_, hour) => ({\n    hour,\n    sales: Math.random() * 100,\n    orders: Math.floor(Math.random() * 10),\n  })),\n};\n\n// Mock API responses\nexport const createMockApiResponse = <T>(data: T, success: boolean = true, message?: string) => ({\n  success,\n  data,\n  message: message || (success ? 'Success' : 'Error'),\n});\n\nexport const createMockPaginatedResponse = <T>(\n  data: T[],\n  page: number = 1,\n  limit: number = 20\n) => ({\n  success: true,\n  data,\n  pagination: {\n    page,\n    limit,\n    total: data.length,\n    totalPages: Math.ceil(data.length / limit),\n  },\n});\n"], "names": [], "mappings": "AAAA,4DAA4D;;;;;;;;;AAGrD,MAAM,eAA0B;IACrC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,KAAK;QACL,SAAS;QACT,OAAO;QACP,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,WAAW;QACX,WAAW;QACX,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,KAAK;QACL,SAAS;QACT,OAAO;QACP,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,WAAW;QACX,WAAW;QACX,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,KAAK;QACL,SAAS;QACT,OAAO;QACP,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,WAAW;QACX,WAAW;QACX,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,KAAK;QACL,SAAS;QACT,OAAO;QACP,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,WAAW;QACX,WAAW;QACX,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,KAAK;QACL,SAAS;QACT,OAAO;QACP,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,WAAW;QACX,WAAW;QACX,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,KAAK;QACL,SAAS;QACT,OAAO;QACP,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,WAAW;QACX,WAAW;QACX,YAAY;QACZ,YAAY;IACd;CACD;AAEM,MAAM,gBAA4B;IACvC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;YAAE,UAAU;YAAS,WAAW,CAAC;QAAO;QAClD,gBAAgB;QAChB,QAAQ;QACR,OAAO;QACP,WAAW;QACX,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;YAAE,UAAU;YAAS,WAAW,CAAC;QAAQ;QACnD,gBAAgB;QAChB,QAAQ;QACR,OAAO;QACP,WAAW;QACX,YAAY;QACZ,YAAY;IACd;CACD;AAEM,MAAM,aAAsB;IACjC;QACE,IAAI;QACJ,cAAc;QACd,aAAa;QACb,WAAW;QACX,YAAY;QACZ,cAAc;QACd,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,iBAAiB;QACjB,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB,OAAO;QACP,kBAAkB;QAClB,mBAAmB;QACnB,yBAAyB;QACzB,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,cAAc;QACd,aAAa;QACb,WAAW;QACX,YAAY;QACZ,cAAc;QACd,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,iBAAiB;QACjB,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB,OAAO;QACP,kBAAkB;QAClB,mBAAmB;QACnB,yBAAyB;QACzB,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,cAAc;QACd,aAAa;QACb,WAAW;QACX,YAAY;QACZ,cAAc;QACd,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,iBAAiB;QACjB,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB,OAAO;QACP,kBAAkB;QAClB,mBAAmB;QACnB,yBAAyB;QACzB,YAAY;QACZ,YAAY;IACd;CACD;AAEM,MAAM,qBAAqC;IAChD,aAAa;IACb,cAAc;IACd,gBAAgB;IAChB,iBAAiB;IACjB,eAAe;IACf,cAAc;IACd,eAAe;QACb,SAAS;QACT,UAAU;QACV,UAAU;QACV,aAAa;IACf;IACA,cAAc;QACZ;YACE,YAAY;YACZ,MAAM;YACN,KAAK;YACL,gBAAgB;YAChB,eAAe;QACjB;QACA;YACE,YAAY;YACZ,MAAM;YACN,KAAK;YACL,gBAAgB;YAChB,eAAe;QACjB;QACA;YACE,YAAY;YACZ,MAAM;YACN,KAAK;YACL,gBAAgB;YAChB,eAAe;QACjB;KACD;IACD,cAAc,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAG,GAAG,CAAC,GAAG,OAAS,CAAC;YACrD;YACA,OAAO,KAAK,MAAM,KAAK;YACvB,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QACrC,CAAC;AACH;AAGO,MAAM,wBAAwB,CAAI,MAAS,UAAmB,IAAI,EAAE,UAAqB,CAAC;QAC/F;QACA;QACA,SAAS,WAAW,CAAC,UAAU,YAAY,OAAO;IACpD,CAAC;AAEM,MAAM,8BAA8B,CACzC,MACA,OAAe,CAAC,EAChB,QAAgB,EAAE,GACf,CAAC;QACJ,SAAS;QACT;QACA,YAAY;YACV;YACA;YACA,OAAO,KAAK,MAAM;YAClB,YAAY,KAAK,IAAI,CAAC,KAAK,MAAM,GAAG;QACtC;IACF,CAAC", "debugId": null}}, {"offset": {"line": 433, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/app/api/products/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { createClient } from '@supabase/supabase-js';\nimport { Database } from '@/lib/supabase';\nimport { mockProducts, createMockPaginatedResponse } from '@/lib/mock-data';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://demo.supabase.co';\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'demo_service_role_key';\n\n// Check if we're in demo mode\nconst isDemoMode = supabaseUrl === 'https://demo.supabase.co' || supabaseServiceKey === 'demo_service_role_key';\n\n// GET /api/products - Fetch products with pagination and filters\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get('page') || '1');\n    const limit = parseInt(searchParams.get('limit') || '20');\n    const categoryId = searchParams.get('category_id');\n    const search = searchParams.get('search');\n    const isActive = searchParams.get('is_active');\n    const isManufactured = searchParams.get('is_manufactured');\n\n    // Return mock data in demo mode\n    if (isDemoMode) {\n      let filteredProducts = mockProducts;\n\n      // Apply filters\n      if (isActive !== null) {\n        filteredProducts = filteredProducts.filter(p => p.is_active === (isActive === 'true'));\n      }\n      if (isManufactured !== null) {\n        filteredProducts = filteredProducts.filter(p => p.is_manufactured === (isManufactured === 'true'));\n      }\n      if (search) {\n        filteredProducts = filteredProducts.filter(p =>\n          p.name.toLowerCase().includes(search.toLowerCase()) ||\n          p.sku.toLowerCase().includes(search.toLowerCase())\n        );\n      }\n\n      return NextResponse.json(createMockPaginatedResponse(filteredProducts, page, limit));\n    }\n\n    const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey);\n\n    // Build query\n    let query = supabase\n      .from('products')\n      .select(`\n        *,\n        categories (\n          id,\n          name\n        ),\n        recipes (\n          id,\n          name,\n          preparation_time,\n          cost_per_unit\n        )\n      `)\n      .order('name', { ascending: true });\n\n    // Apply filters\n    if (categoryId) {\n      query = query.eq('category_id', categoryId);\n    }\n    if (isActive !== null) {\n      query = query.eq('is_active', isActive === 'true');\n    }\n    if (isManufactured !== null) {\n      query = query.eq('is_manufactured', isManufactured === 'true');\n    }\n    if (search) {\n      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%,sku.ilike.%${search}%`);\n    }\n\n    // Apply pagination\n    const from = (page - 1) * limit;\n    const to = from + limit - 1;\n    query = query.range(from, to);\n\n    const { data: products, error } = await query;\n\n    if (error) {\n      return NextResponse.json(\n        { error: error.message },\n        { status: 400 }\n      );\n    }\n\n    // Get total count for pagination\n    let countQuery = supabase\n      .from('products')\n      .select('*', { count: 'exact', head: true });\n\n    // Apply same filters for count\n    if (categoryId) {\n      countQuery = countQuery.eq('category_id', categoryId);\n    }\n    if (isActive !== null) {\n      countQuery = countQuery.eq('is_active', isActive === 'true');\n    }\n    if (isManufactured !== null) {\n      countQuery = countQuery.eq('is_manufactured', isManufactured === 'true');\n    }\n    if (search) {\n      countQuery = countQuery.or(`name.ilike.%${search}%,description.ilike.%${search}%,sku.ilike.%${search}%`);\n    }\n\n    const { count: totalCount } = await countQuery;\n\n    return NextResponse.json({\n      success: true,\n      data: products,\n      pagination: {\n        page,\n        limit,\n        total: totalCount || 0,\n        totalPages: Math.ceil((totalCount || 0) / limit),\n      },\n    });\n\n  } catch (error) {\n    console.error('Get products error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\n// POST /api/products - Create new product\nexport async function POST(request: NextRequest) {\n  try {\n    const productData = await request.json();\n    const {\n      name,\n      description,\n      category_id,\n      sku,\n      barcode,\n      price,\n      cost,\n      unit = 'piece',\n      is_manufactured = false,\n      image_url,\n    } = productData;\n\n    // Validation\n    if (!name || !category_id || !sku || price === undefined || cost === undefined) {\n      return NextResponse.json(\n        { error: 'Missing required fields: name, category_id, sku, price, cost' },\n        { status: 400 }\n      );\n    }\n\n    const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey);\n\n    // Check if SKU already exists\n    const { data: existingProduct } = await supabase\n      .from('products')\n      .select('id')\n      .eq('sku', sku)\n      .single();\n\n    if (existingProduct) {\n      return NextResponse.json(\n        { error: 'Product with this SKU already exists' },\n        { status: 400 }\n      );\n    }\n\n    // Create product\n    const { data: product, error: productError } = await supabase\n      .from('products')\n      .insert({\n        name,\n        description: description || null,\n        category_id,\n        sku,\n        barcode: barcode || null,\n        price: parseFloat(price),\n        cost: parseFloat(cost),\n        unit,\n        is_manufactured,\n        image_url: image_url || null,\n      })\n      .select(`\n        *,\n        categories (\n          id,\n          name\n        )\n      `)\n      .single();\n\n    if (productError) {\n      return NextResponse.json(\n        { error: productError.message },\n        { status: 400 }\n      );\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: product,\n      message: 'Product created successfully',\n    });\n\n  } catch (error) {\n    console.error('Create product error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAEA;;;;AAEA,MAAM,cAAc,gEAAwC;AAC5D,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB,IAAI;AAEpE,8BAA8B;AAC9B,MAAM,aAAa,gBAAgB,8BAA8B,uBAAuB;AAGjF,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,aAAa,aAAa,GAAG,CAAC;QACpC,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,iBAAiB,aAAa,GAAG,CAAC;QAExC,gCAAgC;QAChC,wCAAgB;YACd,IAAI,mBAAmB,4HAAA,CAAA,eAAY;YAEnC,gBAAgB;YAChB,IAAI,aAAa,MAAM;gBACrB,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK,CAAC,aAAa,MAAM;YACtF;YACA,IAAI,mBAAmB,MAAM;gBAC3B,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,eAAe,KAAK,CAAC,mBAAmB,MAAM;YAClG;YACA,IAAI,QAAQ;gBACV,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,IACzC,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,OAAO,WAAW,OAChD,EAAE,GAAG,CAAC,WAAW,GAAG,QAAQ,CAAC,OAAO,WAAW;YAEnD;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,CAAA,GAAA,4HAAA,CAAA,8BAA2B,AAAD,EAAE,kBAAkB,MAAM;QAC/E;;QAEA,MAAM;QAEN,cAAc;QACd,IAAI;QA+BJ,mBAAmB;QACnB,MAAM;QACN,MAAM;QAGN,MAAc,sBAAU;QASxB,iCAAiC;QACjC,IAAI;QAkBJ,MAAe;IAajB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,cAAc,MAAM,QAAQ,IAAI;QACtC,MAAM,EACJ,IAAI,EACJ,WAAW,EACX,WAAW,EACX,GAAG,EACH,OAAO,EACP,KAAK,EACL,IAAI,EACJ,OAAO,OAAO,EACd,kBAAkB,KAAK,EACvB,SAAS,EACV,GAAG;QAEJ,aAAa;QACb,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,OAAO,UAAU,aAAa,SAAS,WAAW;YAC9E,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA+D,GACxE;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAY,aAAa;QAErD,8BAA8B;QAC9B,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,MAAM,SACrC,IAAI,CAAC,YACL,MAAM,CAAC,MACP,EAAE,CAAC,OAAO,KACV,MAAM;QAET,IAAI,iBAAiB;YACnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuC,GAChD;gBAAE,QAAQ;YAAI;QAElB;QAEA,iBAAiB;QACjB,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAClD,IAAI,CAAC,YACL,MAAM,CAAC;YACN;YACA,aAAa,eAAe;YAC5B;YACA;YACA,SAAS,WAAW;YACpB,OAAO,WAAW;YAClB,MAAM,WAAW;YACjB;YACA;YACA,WAAW,aAAa;QAC1B,GACC,MAAM,CAAC,CAAC;;;;;;MAMT,CAAC,EACA,MAAM;QAET,IAAI,cAAc;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,aAAa,OAAO;YAAC,GAC9B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe;IACpB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,gCAAgC;YAChC,gCAAgC;QAClC;IACF;AACF", "debugId": null}}]}