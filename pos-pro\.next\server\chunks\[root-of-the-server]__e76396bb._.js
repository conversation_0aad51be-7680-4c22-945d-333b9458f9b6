module.exports = {

"[project]/.next-internal/server/app/api/orders/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/utils/index.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "calculateDistance": (()=>calculateDistance),
    "calculateTax": (()=>calculateTax),
    "calculateTotal": (()=>calculateTotal),
    "capitalize": (()=>capitalize),
    "cn": (()=>cn),
    "debounce": (()=>debounce),
    "formatCurrency": (()=>formatCurrency),
    "formatDate": (()=>formatDate),
    "formatDateTime": (()=>formatDateTime),
    "formatNumber": (()=>formatNumber),
    "formatPercentage": (()=>formatPercentage),
    "formatTime": (()=>formatTime),
    "generateId": (()=>generateId),
    "generateOrderNumber": (()=>generateOrderNumber),
    "generatePONumber": (()=>generatePONumber),
    "getFromStorage": (()=>getFromStorage),
    "groupBy": (()=>groupBy),
    "handleError": (()=>handleError),
    "isPointInPolygon": (()=>isPointInPolygon),
    "isPointInRadius": (()=>isPointInRadius),
    "isValidEmail": (()=>isValidEmail),
    "isValidPhone": (()=>isValidPhone),
    "removeFromStorage": (()=>removeFromStorage),
    "setToStorage": (()=>setToStorage),
    "slugify": (()=>slugify),
    "sortBy": (()=>sortBy),
    "throttle": (()=>throttle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/date-fns/format.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$parseISO$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/parseISO.js [app-route] (ecmascript)");
;
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
}
function formatDate(date, formatString = 'PPP') {
    const dateObj = typeof date === 'string' ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$parseISO$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parseISO"])(date) : date;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(dateObj, formatString);
}
function formatDateTime(date) {
    return formatDate(date, 'PPP p');
}
function formatTime(date) {
    return formatDate(date, 'p');
}
function formatNumber(num, decimals = 2) {
    return num.toFixed(decimals);
}
function formatPercentage(num, decimals = 1) {
    return `${(num * 100).toFixed(decimals)}%`;
}
function capitalize(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
}
function slugify(str) {
    return str.toLowerCase().replace(/[^\w\s-]/g, '').replace(/[\s_-]+/g, '-').replace(/^-+|-+$/g, '');
}
function generateId() {
    return Math.random().toString(36).substr(2, 9);
}
function generateOrderNumber() {
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substr(2, 4).toUpperCase();
    return `ORD-${timestamp}-${random}`;
}
function generatePONumber() {
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substr(2, 4).toUpperCase();
    return `PO-${timestamp}-${random}`;
}
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
function isValidPhone(phone) {
    const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
    return phoneRegex.test(phone) && phone.replace(/\D/g, '').length >= 10;
}
function calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = deg2rad(lat2 - lat1);
    const dLon = deg2rad(lon2 - lon1);
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const d = R * c; // Distance in kilometers
    return d;
}
function deg2rad(deg) {
    return deg * (Math.PI / 180);
}
function isPointInRadius(centerLat, centerLon, pointLat, pointLon, radiusKm) {
    const distance = calculateDistance(centerLat, centerLon, pointLat, pointLon);
    return distance <= radiusKm;
}
function isPointInPolygon(point, polygon) {
    const x = point.longitude;
    const y = point.latitude;
    let inside = false;
    for(let i = 0, j = polygon.length - 1; i < polygon.length; j = i++){
        const xi = polygon[i].longitude;
        const yi = polygon[i].latitude;
        const xj = polygon[j].longitude;
        const yj = polygon[j].latitude;
        if (yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi) {
            inside = !inside;
        }
    }
    return inside;
}
function groupBy(array, key) {
    return array.reduce((groups, item)=>{
        const group = String(item[key]);
        groups[group] = groups[group] || [];
        groups[group].push(item);
        return groups;
    }, {});
}
function sortBy(array, key, direction = 'asc') {
    return [
        ...array
    ].sort((a, b)=>{
        const aVal = a[key];
        const bVal = b[key];
        if (aVal < bVal) return direction === 'asc' ? -1 : 1;
        if (aVal > bVal) return direction === 'asc' ? 1 : -1;
        return 0;
    });
}
function getFromStorage(key, defaultValue) {
    if ("TURBOPACK compile-time truthy", 1) return defaultValue;
    "TURBOPACK unreachable";
}
function setToStorage(key, value) {
    if ("TURBOPACK compile-time truthy", 1) return;
    "TURBOPACK unreachable";
}
function removeFromStorage(key) {
    if ("TURBOPACK compile-time truthy", 1) return;
    "TURBOPACK unreachable";
}
function debounce(func, wait) {
    let timeout;
    return (...args)=>{
        clearTimeout(timeout);
        timeout = setTimeout(()=>func(...args), wait);
    };
}
function throttle(func, limit) {
    let inThrottle;
    return (...args)=>{
        if (!inThrottle) {
            func(...args);
            inThrottle = true;
            setTimeout(()=>inThrottle = false, limit);
        }
    };
}
function handleError(error) {
    if (error instanceof Error) {
        return error.message;
    }
    if (typeof error === 'string') {
        return error;
    }
    return 'An unknown error occurred';
}
function calculateTax(amount, taxRate) {
    return amount * (taxRate / 100);
}
function calculateTotal(subtotal, taxRate, discount = 0) {
    const discountAmount = subtotal * (discount / 100);
    const taxableAmount = subtotal - discountAmount;
    const taxAmount = calculateTax(taxableAmount, taxRate);
    const total = taxableAmount + taxAmount;
    return {
        subtotal,
        taxAmount,
        discountAmount,
        total
    };
}
}}),
"[project]/src/lib/mock-data.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Mock data for development when Supabase is not configured
__turbopack_context__.s({
    "createMockApiResponse": (()=>createMockApiResponse),
    "createMockPaginatedResponse": (()=>createMockPaginatedResponse),
    "mockCustomers": (()=>mockCustomers),
    "mockDashboardStats": (()=>mockDashboardStats),
    "mockOrders": (()=>mockOrders),
    "mockProducts": (()=>mockProducts)
});
const mockProducts = [
    {
        id: 'prod-1',
        name: 'Espresso',
        description: 'Strong coffee shot',
        category_id: 'cat-1',
        sku: 'BEV-ESP-001',
        barcode: '1234567890',
        price: 2.50,
        cost: 0.75,
        unit: 'cup',
        is_manufactured: true,
        recipe_id: 'recipe-1',
        image_url: null,
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    },
    {
        id: 'prod-2',
        name: 'Cappuccino',
        description: 'Espresso with steamed milk',
        category_id: 'cat-1',
        sku: 'BEV-CAP-001',
        barcode: '1234567891',
        price: 4.50,
        cost: 1.25,
        unit: 'cup',
        is_manufactured: true,
        recipe_id: 'recipe-2',
        image_url: null,
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    },
    {
        id: 'prod-3',
        name: 'Latte',
        description: 'Espresso with steamed milk and foam',
        category_id: 'cat-1',
        sku: 'BEV-LAT-001',
        barcode: '1234567892',
        price: 5.00,
        cost: 1.50,
        unit: 'cup',
        is_manufactured: true,
        recipe_id: 'recipe-3',
        image_url: null,
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    },
    {
        id: 'prod-4',
        name: 'Caesar Salad',
        description: 'Fresh romaine with caesar dressing',
        category_id: 'cat-2',
        sku: 'APP-CS-001',
        barcode: '1234567893',
        price: 8.50,
        cost: 3.00,
        unit: 'plate',
        is_manufactured: true,
        recipe_id: 'recipe-4',
        image_url: null,
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    },
    {
        id: 'prod-5',
        name: 'Grilled Chicken',
        description: 'Seasoned grilled chicken breast',
        category_id: 'cat-3',
        sku: 'MAIN-GC-001',
        barcode: '1234567894',
        price: 15.00,
        cost: 6.00,
        unit: 'plate',
        is_manufactured: true,
        recipe_id: 'recipe-5',
        image_url: null,
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    },
    {
        id: 'prod-6',
        name: 'Chocolate Cake',
        description: 'Rich chocolate layer cake',
        category_id: 'cat-4',
        sku: 'DES-CC-001',
        barcode: '1234567895',
        price: 6.50,
        cost: 2.50,
        unit: 'slice',
        is_manufactured: true,
        recipe_id: 'recipe-6',
        image_url: null,
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    }
];
const mockCustomers = [
    {
        id: 'cust-1',
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '******-0150',
        address: '123 Customer St, Downtown',
        location: {
            latitude: 40.7127,
            longitude: -74.005
        },
        loyalty_points: 150,
        rating: 4.5,
        notes: 'Regular customer, prefers oat milk',
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    },
    {
        id: 'cust-2',
        name: 'Jane Smith',
        email: '<EMAIL>',
        phone: '******-0151',
        address: '456 Client Ave, Uptown',
        location: {
            latitude: 40.7483,
            longitude: -73.9856
        },
        loyalty_points: 75,
        rating: 4.2,
        notes: null,
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    }
];
const mockOrders = [
    {
        id: 'order-1',
        order_number: 'ORD-001-2024',
        customer_id: 'cust-1',
        branch_id: 'demo-branch-id',
        cashier_id: 'demo-cashier-id',
        sales_rep_id: null,
        table_id: null,
        order_type: 'takeaway',
        status: 'delivered',
        subtotal: 12.00,
        tax_amount: 1.20,
        discount_amount: 0,
        total_amount: 13.20,
        payment_method: 'card',
        payment_status: 'paid',
        notes: null,
        delivery_address: null,
        delivery_location: null,
        estimated_delivery_time: null,
        created_at: '2024-01-15T10:30:00Z',
        updated_at: '2024-01-15T10:45:00Z'
    },
    {
        id: 'order-2',
        order_number: 'ORD-002-2024',
        customer_id: 'cust-2',
        branch_id: 'demo-branch-id',
        cashier_id: 'demo-cashier-id',
        sales_rep_id: null,
        table_id: null,
        order_type: 'dine_in',
        status: 'preparing',
        subtotal: 23.50,
        tax_amount: 2.35,
        discount_amount: 0,
        total_amount: 25.85,
        payment_method: 'cash',
        payment_status: 'paid',
        notes: 'Extra sauce on the side',
        delivery_address: null,
        delivery_location: null,
        estimated_delivery_time: null,
        created_at: '2024-01-15T11:15:00Z',
        updated_at: '2024-01-15T11:20:00Z'
    },
    {
        id: 'order-3',
        order_number: 'ORD-003-2024',
        customer_id: null,
        branch_id: 'demo-branch-id',
        cashier_id: 'demo-cashier-id',
        sales_rep_id: null,
        table_id: null,
        order_type: 'takeaway',
        status: 'pending',
        subtotal: 7.00,
        tax_amount: 0.70,
        discount_amount: 0,
        total_amount: 7.70,
        payment_method: 'digital_wallet',
        payment_status: 'pending',
        notes: null,
        delivery_address: null,
        delivery_location: null,
        estimated_delivery_time: null,
        created_at: '2024-01-15T12:00:00Z',
        updated_at: '2024-01-15T12:00:00Z'
    }
];
const mockDashboardStats = {
    daily_sales: 1250.75,
    daily_orders: 45,
    pending_orders: 3,
    low_stock_items: 2,
    active_tables: 8,
    sales_growth: 12.5,
    sales_by_type: {
        dine_in: 650.25,
        takeaway: 400.50,
        delivery: 200.00,
        field_sales: 0
    },
    top_products: [
        {
            product_id: 'prod-2',
            name: 'Cappuccino',
            sku: 'BEV-CAP-001',
            total_quantity: 25,
            total_revenue: 112.50
        },
        {
            product_id: 'prod-3',
            name: 'Latte',
            sku: 'BEV-LAT-001',
            total_quantity: 20,
            total_revenue: 100.00
        },
        {
            product_id: 'prod-1',
            name: 'Espresso',
            sku: 'BEV-ESP-001',
            total_quantity: 18,
            total_revenue: 45.00
        }
    ],
    hourly_sales: Array.from({
        length: 24
    }, (_, hour)=>({
            hour,
            sales: Math.random() * 100,
            orders: Math.floor(Math.random() * 10)
        }))
};
const createMockApiResponse = (data, success = true, message)=>({
        success,
        data,
        message: message || (success ? 'Success' : 'Error')
    });
const createMockPaginatedResponse = (data, page = 1, limit = 20)=>({
        success: true,
        data,
        pagination: {
            page,
            limit,
            total: data.length,
            totalPages: Math.ceil(data.length / limit)
        }
    });
}}),
"[project]/src/app/api/orders/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "OPTIONS": (()=>OPTIONS),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/index.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mock-data.ts [app-route] (ecmascript)");
;
;
;
;
const supabaseUrl = ("TURBOPACK compile-time value", "https://demo.supabase.co") || 'https://demo.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'demo_service_role_key';
// Check if we're in demo mode
const isDemoMode = supabaseUrl === 'https://demo.supabase.co' || supabaseServiceKey === 'demo_service_role_key';
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const page = parseInt(searchParams.get('page') || '1');
        const limit = parseInt(searchParams.get('limit') || '10');
        const status = searchParams.get('status');
        const branchId = searchParams.get('branch_id');
        const orderType = searchParams.get('order_type');
        const startDate = searchParams.get('start_date');
        const endDate = searchParams.get('end_date');
        // Return mock data in demo mode
        if ("TURBOPACK compile-time truthy", 1) {
            let filteredOrders = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mockOrders"];
            // Apply filters
            if (status) {
                filteredOrders = filteredOrders.filter((o)=>o.status === status);
            }
            if (orderType) {
                filteredOrders = filteredOrders.filter((o)=>o.order_type === orderType);
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createMockPaginatedResponse"])(filteredOrders, page, limit));
        }
        "TURBOPACK unreachable";
        const supabase = undefined;
        // Build query
        let query;
        // Apply pagination
        const from = undefined;
        const to = undefined;
        const orders = undefined, error = undefined, count = undefined;
        // Get total count for pagination
        const totalCount = undefined;
    } catch (error) {
        console.error('Get orders error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const orderData = await request.json();
        const { customer_id, branch_id, cashier_id, sales_rep_id, table_id, order_type, items, notes, delivery_address, delivery_location, payment_method = 'cash' } = orderData;
        // Validation
        if (!branch_id || !cashier_id || !order_type || !items || items.length === 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Missing required fields'
            }, {
                status: 400
            });
        }
        // Return mock response in demo mode
        if ("TURBOPACK compile-time truthy", 1) {
            const orderNumber = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateOrderNumber"])();
            const subtotal = items.reduce((sum, item)=>sum + item.quantity * 5.00, 0); // Mock price
            const taxAmount = subtotal * 0.10;
            const totalAmount = subtotal + taxAmount;
            const mockOrder = {
                id: `order-${Date.now()}`,
                order_number: orderNumber,
                customer_id: customer_id || null,
                branch_id,
                cashier_id,
                sales_rep_id: sales_rep_id || null,
                table_id: table_id || null,
                order_type,
                status: 'pending',
                subtotal,
                tax_amount: taxAmount,
                discount_amount: 0,
                total_amount: totalAmount,
                payment_method,
                payment_status: 'pending',
                notes: notes || null,
                delivery_address: delivery_address || null,
                delivery_location: delivery_location || null,
                estimated_delivery_time: null,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                order_items: items.map((item, index)=>({
                        id: `item-${Date.now()}-${index}`,
                        product_id: item.product_id,
                        quantity: item.quantity,
                        unit_price: 5.00,
                        total_price: item.quantity * 5.00,
                        notes: item.notes || null
                    }))
            };
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createMockApiResponse"])(mockOrder, true, 'Order created successfully'));
        }
        "TURBOPACK unreachable";
        const supabase = undefined;
        // Generate order number
        const orderNumber = undefined;
        // Calculate totals
        let subtotal;
        const processedItems = undefined;
        const item = undefined;
        // Calculate tax (10% for example)
        const taxRate = undefined;
        const taxAmount = undefined;
        const totalAmount = undefined;
        // Create order
        const order = undefined, orderError = undefined;
        // Create order items
        const orderItemsWithOrderId = undefined;
        const itemsError = undefined;
        // Fetch complete order with relations
        const completeOrder = undefined;
    } catch (error) {
        console.error('Create order error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
async function OPTIONS() {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](null, {
        status: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
    });
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__e76396bb._.js.map