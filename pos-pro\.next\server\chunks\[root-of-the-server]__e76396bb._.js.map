{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/utils/index.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\nimport { format, parseISO } from 'date-fns';\n\n// Utility function for combining Tailwind classes\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// Currency formatting\nexport function formatCurrency(amount: number, currency: string = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: currency,\n  }).format(amount);\n}\n\n// Date formatting utilities\nexport function formatDate(date: string | Date, formatString: string = 'PPP'): string {\n  const dateObj = typeof date === 'string' ? parseISO(date) : date;\n  return format(dateObj, formatString);\n}\n\nexport function formatDateTime(date: string | Date): string {\n  return formatDate(date, 'PPP p');\n}\n\nexport function formatTime(date: string | Date): string {\n  return formatDate(date, 'p');\n}\n\n// Number formatting\nexport function formatNumber(num: number, decimals: number = 2): string {\n  return num.toFixed(decimals);\n}\n\nexport function formatPercentage(num: number, decimals: number = 1): string {\n  return `${(num * 100).toFixed(decimals)}%`;\n}\n\n// String utilities\nexport function capitalize(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\n\nexport function slugify(str: string): string {\n  return str\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '');\n}\n\n// Generate unique IDs\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function generateOrderNumber(): string {\n  const timestamp = Date.now().toString().slice(-6);\n  const random = Math.random().toString(36).substr(2, 4).toUpperCase();\n  return `ORD-${timestamp}-${random}`;\n}\n\nexport function generatePONumber(): string {\n  const timestamp = Date.now().toString().slice(-6);\n  const random = Math.random().toString(36).substr(2, 4).toUpperCase();\n  return `PO-${timestamp}-${random}`;\n}\n\n// Validation utilities\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function isValidPhone(phone: string): boolean {\n  const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]+$/;\n  return phoneRegex.test(phone) && phone.replace(/\\D/g, '').length >= 10;\n}\n\n// Geographic utilities\nexport function calculateDistance(\n  lat1: number,\n  lon1: number,\n  lat2: number,\n  lon2: number\n): number {\n  const R = 6371; // Radius of the Earth in kilometers\n  const dLat = deg2rad(lat2 - lat1);\n  const dLon = deg2rad(lon2 - lon1);\n  const a =\n    Math.sin(dLat / 2) * Math.sin(dLat / 2) +\n    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *\n    Math.sin(dLon / 2) * Math.sin(dLon / 2);\n  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n  const d = R * c; // Distance in kilometers\n  return d;\n}\n\nfunction deg2rad(deg: number): number {\n  return deg * (Math.PI / 180);\n}\n\nexport function isPointInRadius(\n  centerLat: number,\n  centerLon: number,\n  pointLat: number,\n  pointLon: number,\n  radiusKm: number\n): boolean {\n  const distance = calculateDistance(centerLat, centerLon, pointLat, pointLon);\n  return distance <= radiusKm;\n}\n\n// Check if point is inside polygon (for territory boundaries)\nexport function isPointInPolygon(\n  point: { latitude: number; longitude: number },\n  polygon: { latitude: number; longitude: number }[]\n): boolean {\n  const x = point.longitude;\n  const y = point.latitude;\n  let inside = false;\n\n  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\n    const xi = polygon[i].longitude;\n    const yi = polygon[i].latitude;\n    const xj = polygon[j].longitude;\n    const yj = polygon[j].latitude;\n\n    if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {\n      inside = !inside;\n    }\n  }\n\n  return inside;\n}\n\n// Array utilities\nexport function groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {\n  return array.reduce((groups, item) => {\n    const group = String(item[key]);\n    groups[group] = groups[group] || [];\n    groups[group].push(item);\n    return groups;\n  }, {} as Record<string, T[]>);\n}\n\nexport function sortBy<T>(array: T[], key: keyof T, direction: 'asc' | 'desc' = 'asc'): T[] {\n  return [...array].sort((a, b) => {\n    const aVal = a[key];\n    const bVal = b[key];\n    \n    if (aVal < bVal) return direction === 'asc' ? -1 : 1;\n    if (aVal > bVal) return direction === 'asc' ? 1 : -1;\n    return 0;\n  });\n}\n\n// Local storage utilities\nexport function getFromStorage<T>(key: string, defaultValue: T): T {\n  if (typeof window === 'undefined') return defaultValue;\n  \n  try {\n    const item = window.localStorage.getItem(key);\n    return item ? JSON.parse(item) : defaultValue;\n  } catch (error) {\n    console.error(`Error reading from localStorage:`, error);\n    return defaultValue;\n  }\n}\n\nexport function setToStorage<T>(key: string, value: T): void {\n  if (typeof window === 'undefined') return;\n  \n  try {\n    window.localStorage.setItem(key, JSON.stringify(value));\n  } catch (error) {\n    console.error(`Error writing to localStorage:`, error);\n  }\n}\n\nexport function removeFromStorage(key: string): void {\n  if (typeof window === 'undefined') return;\n  \n  try {\n    window.localStorage.removeItem(key);\n  } catch (error) {\n    console.error(`Error removing from localStorage:`, error);\n  }\n}\n\n// Debounce utility\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  \n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// Throttle utility\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  \n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n}\n\n// Error handling\nexport function handleError(error: unknown): string {\n  if (error instanceof Error) {\n    return error.message;\n  }\n  if (typeof error === 'string') {\n    return error;\n  }\n  return 'An unknown error occurred';\n}\n\n// Tax calculation\nexport function calculateTax(amount: number, taxRate: number): number {\n  return amount * (taxRate / 100);\n}\n\nexport function calculateTotal(subtotal: number, taxRate: number, discount: number = 0): {\n  subtotal: number;\n  taxAmount: number;\n  discountAmount: number;\n  total: number;\n} {\n  const discountAmount = subtotal * (discount / 100);\n  const taxableAmount = subtotal - discountAmount;\n  const taxAmount = calculateTax(taxableAmount, taxRate);\n  const total = taxableAmount + taxAmount;\n\n  return {\n    subtotal,\n    taxAmount,\n    discountAmount,\n    total,\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,uIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,eAAe,MAAc,EAAE,WAAmB,KAAK;IACrE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,WAAW,IAAmB,EAAE,eAAuB,KAAK;IAC1E,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,yIAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;IAC5D,OAAO,CAAA,GAAA,uJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;AACzB;AAEO,SAAS,eAAe,IAAmB;IAChD,OAAO,WAAW,MAAM;AAC1B;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,WAAW,MAAM;AAC1B;AAGO,SAAS,aAAa,GAAW,EAAE,WAAmB,CAAC;IAC5D,OAAO,IAAI,OAAO,CAAC;AACrB;AAEO,SAAS,iBAAiB,GAAW,EAAE,WAAmB,CAAC;IAChE,OAAO,GAAG,CAAC,MAAM,GAAG,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;AAC5C;AAGO,SAAS,WAAW,GAAW;IACpC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD;AAEO,SAAS,QAAQ,GAAW;IACjC,OAAO,IACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAGO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS;IACd,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;IAC/C,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW;IAClE,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,QAAQ;AACrC;AAEO,SAAS;IACd,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;IAC/C,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW;IAClE,OAAO,CAAC,GAAG,EAAE,UAAU,CAAC,EAAE,QAAQ;AACpC;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,UAAU,MAAM,OAAO,CAAC,OAAO,IAAI,MAAM,IAAI;AACtE;AAGO,SAAS,kBACd,IAAY,EACZ,IAAY,EACZ,IAAY,EACZ,IAAY;IAEZ,MAAM,IAAI,MAAM,oCAAoC;IACpD,MAAM,OAAO,QAAQ,OAAO;IAC5B,MAAM,OAAO,QAAQ,OAAO;IAC5B,MAAM,IACJ,KAAK,GAAG,CAAC,OAAO,KAAK,KAAK,GAAG,CAAC,OAAO,KACrC,KAAK,GAAG,CAAC,QAAQ,SAAS,KAAK,GAAG,CAAC,QAAQ,SAC3C,KAAK,GAAG,CAAC,OAAO,KAAK,KAAK,GAAG,CAAC,OAAO;IACvC,MAAM,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI;IACrD,MAAM,IAAI,IAAI,GAAG,yBAAyB;IAC1C,OAAO;AACT;AAEA,SAAS,QAAQ,GAAW;IAC1B,OAAO,MAAM,CAAC,KAAK,EAAE,GAAG,GAAG;AAC7B;AAEO,SAAS,gBACd,SAAiB,EACjB,SAAiB,EACjB,QAAgB,EAChB,QAAgB,EAChB,QAAgB;IAEhB,MAAM,WAAW,kBAAkB,WAAW,WAAW,UAAU;IACnE,OAAO,YAAY;AACrB;AAGO,SAAS,iBACd,KAA8C,EAC9C,OAAkD;IAElD,MAAM,IAAI,MAAM,SAAS;IACzB,MAAM,IAAI,MAAM,QAAQ;IACxB,IAAI,SAAS;IAEb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,GAAG,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAI,IAAK;QACnE,MAAM,KAAK,OAAO,CAAC,EAAE,CAAC,SAAS;QAC/B,MAAM,KAAK,OAAO,CAAC,EAAE,CAAC,QAAQ;QAC9B,MAAM,KAAK,OAAO,CAAC,EAAE,CAAC,SAAS;QAC/B,MAAM,KAAK,OAAO,CAAC,EAAE,CAAC,QAAQ;QAE9B,IAAI,AAAE,KAAK,MAAQ,KAAK,KAAQ,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,IAAK;YAC1E,SAAS,CAAC;QACZ;IACF;IAEA,OAAO;AACT;AAGO,SAAS,QAAW,KAAU,EAAE,GAAY;IACjD,OAAO,MAAM,MAAM,CAAC,CAAC,QAAQ;QAC3B,MAAM,QAAQ,OAAO,IAAI,CAAC,IAAI;QAC9B,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,EAAE;QACnC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;QACnB,OAAO;IACT,GAAG,CAAC;AACN;AAEO,SAAS,OAAU,KAAU,EAAE,GAAY,EAAE,YAA4B,KAAK;IACnF,OAAO;WAAI;KAAM,CAAC,IAAI,CAAC,CAAC,GAAG;QACzB,MAAM,OAAO,CAAC,CAAC,IAAI;QACnB,MAAM,OAAO,CAAC,CAAC,IAAI;QAEnB,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,CAAC,IAAI;QACnD,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,IAAI,CAAC;QACnD,OAAO;IACT;AACF;AAGO,SAAS,eAAkB,GAAW,EAAE,YAAe;IAC5D,wCAAmC,OAAO;;AAS5C;AAEO,SAAS,aAAgB,GAAW,EAAE,KAAQ;IACnD,wCAAmC;;AAOrC;AAEO,SAAS,kBAAkB,GAAW;IAC3C,wCAAmC;;AAOrC;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAM,aAAa,OAAO;QACvC;IACF;AACF;AAGO,SAAS,YAAY,KAAc;IACxC,IAAI,iBAAiB,OAAO;QAC1B,OAAO,MAAM,OAAO;IACtB;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IACA,OAAO;AACT;AAGO,SAAS,aAAa,MAAc,EAAE,OAAe;IAC1D,OAAO,SAAS,CAAC,UAAU,GAAG;AAChC;AAEO,SAAS,eAAe,QAAgB,EAAE,OAAe,EAAE,WAAmB,CAAC;IAMpF,MAAM,iBAAiB,WAAW,CAAC,WAAW,GAAG;IACjD,MAAM,gBAAgB,WAAW;IACjC,MAAM,YAAY,aAAa,eAAe;IAC9C,MAAM,QAAQ,gBAAgB;IAE9B,OAAO;QACL;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 257, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/lib/mock-data.ts"], "sourcesContent": ["// Mock data for development when Supa<PERSON> is not configured\nimport { Product, Order, Customer, DashboardStats } from '@/types';\n\nexport const mockProducts: Product[] = [\n  {\n    id: 'prod-1',\n    name: 'Espresso',\n    description: 'Strong coffee shot',\n    category_id: 'cat-1',\n    sku: 'BEV-ESP-001',\n    barcode: '1234567890',\n    price: 2.50,\n    cost: 0.75,\n    unit: 'cup',\n    is_manufactured: true,\n    recipe_id: 'recipe-1',\n    image_url: null,\n    is_active: true,\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  },\n  {\n    id: 'prod-2',\n    name: '<PERSON><PERSON><PERSON><PERSON>',\n    description: '<PERSON><PERSON>ress<PERSON> with steamed milk',\n    category_id: 'cat-1',\n    sku: 'BEV-CAP-001',\n    barcode: '1234567891',\n    price: 4.50,\n    cost: 1.25,\n    unit: 'cup',\n    is_manufactured: true,\n    recipe_id: 'recipe-2',\n    image_url: null,\n    is_active: true,\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  },\n  {\n    id: 'prod-3',\n    name: '<PERSON><PERSON>',\n    description: 'Espresso with steamed milk and foam',\n    category_id: 'cat-1',\n    sku: 'BEV-LAT-001',\n    barcode: '1234567892',\n    price: 5.00,\n    cost: 1.50,\n    unit: 'cup',\n    is_manufactured: true,\n    recipe_id: 'recipe-3',\n    image_url: null,\n    is_active: true,\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  },\n  {\n    id: 'prod-4',\n    name: 'Caesar Salad',\n    description: 'Fresh romaine with caesar dressing',\n    category_id: 'cat-2',\n    sku: 'APP-CS-001',\n    barcode: '1234567893',\n    price: 8.50,\n    cost: 3.00,\n    unit: 'plate',\n    is_manufactured: true,\n    recipe_id: 'recipe-4',\n    image_url: null,\n    is_active: true,\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  },\n  {\n    id: 'prod-5',\n    name: 'Grilled Chicken',\n    description: 'Seasoned grilled chicken breast',\n    category_id: 'cat-3',\n    sku: 'MAIN-GC-001',\n    barcode: '1234567894',\n    price: 15.00,\n    cost: 6.00,\n    unit: 'plate',\n    is_manufactured: true,\n    recipe_id: 'recipe-5',\n    image_url: null,\n    is_active: true,\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  },\n  {\n    id: 'prod-6',\n    name: 'Chocolate Cake',\n    description: 'Rich chocolate layer cake',\n    category_id: 'cat-4',\n    sku: 'DES-CC-001',\n    barcode: '1234567895',\n    price: 6.50,\n    cost: 2.50,\n    unit: 'slice',\n    is_manufactured: true,\n    recipe_id: 'recipe-6',\n    image_url: null,\n    is_active: true,\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  },\n];\n\nexport const mockCustomers: Customer[] = [\n  {\n    id: 'cust-1',\n    name: 'John Doe',\n    email: '<EMAIL>',\n    phone: '******-0150',\n    address: '123 Customer St, Downtown',\n    location: { latitude: 40.7127, longitude: -74.005 },\n    loyalty_points: 150,\n    rating: 4.5,\n    notes: 'Regular customer, prefers oat milk',\n    is_active: true,\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  },\n  {\n    id: 'cust-2',\n    name: 'Jane Smith',\n    email: '<EMAIL>',\n    phone: '******-0151',\n    address: '456 Client Ave, Uptown',\n    location: { latitude: 40.7483, longitude: -73.9856 },\n    loyalty_points: 75,\n    rating: 4.2,\n    notes: null,\n    is_active: true,\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  },\n];\n\nexport const mockOrders: Order[] = [\n  {\n    id: 'order-1',\n    order_number: 'ORD-001-2024',\n    customer_id: 'cust-1',\n    branch_id: 'demo-branch-id',\n    cashier_id: 'demo-cashier-id',\n    sales_rep_id: null,\n    table_id: null,\n    order_type: 'takeaway',\n    status: 'delivered',\n    subtotal: 12.00,\n    tax_amount: 1.20,\n    discount_amount: 0,\n    total_amount: 13.20,\n    payment_method: 'card',\n    payment_status: 'paid',\n    notes: null,\n    delivery_address: null,\n    delivery_location: null,\n    estimated_delivery_time: null,\n    created_at: '2024-01-15T10:30:00Z',\n    updated_at: '2024-01-15T10:45:00Z',\n  },\n  {\n    id: 'order-2',\n    order_number: 'ORD-002-2024',\n    customer_id: 'cust-2',\n    branch_id: 'demo-branch-id',\n    cashier_id: 'demo-cashier-id',\n    sales_rep_id: null,\n    table_id: null,\n    order_type: 'dine_in',\n    status: 'preparing',\n    subtotal: 23.50,\n    tax_amount: 2.35,\n    discount_amount: 0,\n    total_amount: 25.85,\n    payment_method: 'cash',\n    payment_status: 'paid',\n    notes: 'Extra sauce on the side',\n    delivery_address: null,\n    delivery_location: null,\n    estimated_delivery_time: null,\n    created_at: '2024-01-15T11:15:00Z',\n    updated_at: '2024-01-15T11:20:00Z',\n  },\n  {\n    id: 'order-3',\n    order_number: 'ORD-003-2024',\n    customer_id: null,\n    branch_id: 'demo-branch-id',\n    cashier_id: 'demo-cashier-id',\n    sales_rep_id: null,\n    table_id: null,\n    order_type: 'takeaway',\n    status: 'pending',\n    subtotal: 7.00,\n    tax_amount: 0.70,\n    discount_amount: 0,\n    total_amount: 7.70,\n    payment_method: 'digital_wallet',\n    payment_status: 'pending',\n    notes: null,\n    delivery_address: null,\n    delivery_location: null,\n    estimated_delivery_time: null,\n    created_at: '2024-01-15T12:00:00Z',\n    updated_at: '2024-01-15T12:00:00Z',\n  },\n];\n\nexport const mockDashboardStats: DashboardStats = {\n  daily_sales: 1250.75,\n  daily_orders: 45,\n  pending_orders: 3,\n  low_stock_items: 2,\n  active_tables: 8,\n  sales_growth: 12.5,\n  sales_by_type: {\n    dine_in: 650.25,\n    takeaway: 400.50,\n    delivery: 200.00,\n    field_sales: 0,\n  },\n  top_products: [\n    {\n      product_id: 'prod-2',\n      name: 'Cappuccino',\n      sku: 'BEV-CAP-001',\n      total_quantity: 25,\n      total_revenue: 112.50,\n    },\n    {\n      product_id: 'prod-3',\n      name: 'Latte',\n      sku: 'BEV-LAT-001',\n      total_quantity: 20,\n      total_revenue: 100.00,\n    },\n    {\n      product_id: 'prod-1',\n      name: 'Espresso',\n      sku: 'BEV-ESP-001',\n      total_quantity: 18,\n      total_revenue: 45.00,\n    },\n  ],\n  hourly_sales: Array.from({ length: 24 }, (_, hour) => ({\n    hour,\n    sales: Math.random() * 100,\n    orders: Math.floor(Math.random() * 10),\n  })),\n};\n\n// Mock API responses\nexport const createMockApiResponse = <T>(data: T, success: boolean = true, message?: string) => ({\n  success,\n  data,\n  message: message || (success ? 'Success' : 'Error'),\n});\n\nexport const createMockPaginatedResponse = <T>(\n  data: T[],\n  page: number = 1,\n  limit: number = 20\n) => ({\n  success: true,\n  data,\n  pagination: {\n    page,\n    limit,\n    total: data.length,\n    totalPages: Math.ceil(data.length / limit),\n  },\n});\n"], "names": [], "mappings": "AAAA,4DAA4D;;;;;;;;;AAGrD,MAAM,eAA0B;IACrC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,KAAK;QACL,SAAS;QACT,OAAO;QACP,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,WAAW;QACX,WAAW;QACX,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,KAAK;QACL,SAAS;QACT,OAAO;QACP,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,WAAW;QACX,WAAW;QACX,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,KAAK;QACL,SAAS;QACT,OAAO;QACP,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,WAAW;QACX,WAAW;QACX,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,KAAK;QACL,SAAS;QACT,OAAO;QACP,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,WAAW;QACX,WAAW;QACX,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,KAAK;QACL,SAAS;QACT,OAAO;QACP,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,WAAW;QACX,WAAW;QACX,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,KAAK;QACL,SAAS;QACT,OAAO;QACP,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,WAAW;QACX,WAAW;QACX,YAAY;QACZ,YAAY;IACd;CACD;AAEM,MAAM,gBAA4B;IACvC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;YAAE,UAAU;YAAS,WAAW,CAAC;QAAO;QAClD,gBAAgB;QAChB,QAAQ;QACR,OAAO;QACP,WAAW;QACX,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;YAAE,UAAU;YAAS,WAAW,CAAC;QAAQ;QACnD,gBAAgB;QAChB,QAAQ;QACR,OAAO;QACP,WAAW;QACX,YAAY;QACZ,YAAY;IACd;CACD;AAEM,MAAM,aAAsB;IACjC;QACE,IAAI;QACJ,cAAc;QACd,aAAa;QACb,WAAW;QACX,YAAY;QACZ,cAAc;QACd,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,iBAAiB;QACjB,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB,OAAO;QACP,kBAAkB;QAClB,mBAAmB;QACnB,yBAAyB;QACzB,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,cAAc;QACd,aAAa;QACb,WAAW;QACX,YAAY;QACZ,cAAc;QACd,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,iBAAiB;QACjB,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB,OAAO;QACP,kBAAkB;QAClB,mBAAmB;QACnB,yBAAyB;QACzB,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,cAAc;QACd,aAAa;QACb,WAAW;QACX,YAAY;QACZ,cAAc;QACd,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,iBAAiB;QACjB,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB,OAAO;QACP,kBAAkB;QAClB,mBAAmB;QACnB,yBAAyB;QACzB,YAAY;QACZ,YAAY;IACd;CACD;AAEM,MAAM,qBAAqC;IAChD,aAAa;IACb,cAAc;IACd,gBAAgB;IAChB,iBAAiB;IACjB,eAAe;IACf,cAAc;IACd,eAAe;QACb,SAAS;QACT,UAAU;QACV,UAAU;QACV,aAAa;IACf;IACA,cAAc;QACZ;YACE,YAAY;YACZ,MAAM;YACN,KAAK;YACL,gBAAgB;YAChB,eAAe;QACjB;QACA;YACE,YAAY;YACZ,MAAM;YACN,KAAK;YACL,gBAAgB;YAChB,eAAe;QACjB;QACA;YACE,YAAY;YACZ,MAAM;YACN,KAAK;YACL,gBAAgB;YAChB,eAAe;QACjB;KACD;IACD,cAAc,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAG,GAAG,CAAC,GAAG,OAAS,CAAC;YACrD;YACA,OAAO,KAAK,MAAM,KAAK;YACvB,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QACrC,CAAC;AACH;AAGO,MAAM,wBAAwB,CAAI,MAAS,UAAmB,IAAI,EAAE,UAAqB,CAAC;QAC/F;QACA;QACA,SAAS,WAAW,CAAC,UAAU,YAAY,OAAO;IACpD,CAAC;AAEM,MAAM,8BAA8B,CACzC,MACA,OAAe,CAAC,EAChB,QAAgB,EAAE,GACf,CAAC;QACJ,SAAS;QACT;QACA,YAAY;YACV;YACA;YACA,OAAO,KAAK,MAAM;YAClB,YAAY,KAAK,IAAI,CAAC,KAAK,MAAM,GAAG;QACtC;IACF,CAAC", "debugId": null}}, {"offset": {"line": 542, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/app/api/orders/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { createClient } from '@supabase/supabase-js';\nimport { Database } from '@/lib/supabase';\nimport { generateOrderNumber } from '@/utils';\nimport { mockOrders, createMockPaginatedResponse, createMockApiResponse } from '@/lib/mock-data';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://demo.supabase.co';\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'demo_service_role_key';\n\n// Check if we're in demo mode\nconst isDemoMode = supabaseUrl === 'https://demo.supabase.co' || supabaseServiceKey === 'demo_service_role_key';\n\n// GET /api/orders - Fetch orders with pagination and filters\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get('page') || '1');\n    const limit = parseInt(searchParams.get('limit') || '10');\n    const status = searchParams.get('status');\n    const branchId = searchParams.get('branch_id');\n    const orderType = searchParams.get('order_type');\n    const startDate = searchParams.get('start_date');\n    const endDate = searchParams.get('end_date');\n\n    // Return mock data in demo mode\n    if (isDemoMode) {\n      let filteredOrders = mockOrders;\n\n      // Apply filters\n      if (status) {\n        filteredOrders = filteredOrders.filter(o => o.status === status);\n      }\n      if (orderType) {\n        filteredOrders = filteredOrders.filter(o => o.order_type === orderType);\n      }\n\n      return NextResponse.json(createMockPaginatedResponse(filteredOrders, page, limit));\n    }\n\n    const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey);\n\n    // Build query\n    let query = supabase\n      .from('orders')\n      .select(`\n        *,\n        customers (\n          id,\n          name,\n          phone,\n          email\n        ),\n        branches (\n          id,\n          name\n        ),\n        users!orders_cashier_id_fkey (\n          id,\n          name\n        ),\n        sales_rep:users!orders_sales_rep_id_fkey (\n          id,\n          name\n        ),\n        tables (\n          id,\n          table_number\n        ),\n        order_items (\n          id,\n          quantity,\n          unit_price,\n          total_price,\n          notes,\n          products (\n            id,\n            name,\n            sku\n          )\n        )\n      `)\n      .order('created_at', { ascending: false });\n\n    // Apply filters\n    if (status) {\n      query = query.eq('status', status);\n    }\n    if (branchId) {\n      query = query.eq('branch_id', branchId);\n    }\n    if (orderType) {\n      query = query.eq('order_type', orderType);\n    }\n    if (startDate) {\n      query = query.gte('created_at', startDate);\n    }\n    if (endDate) {\n      query = query.lte('created_at', endDate);\n    }\n\n    // Apply pagination\n    const from = (page - 1) * limit;\n    const to = from + limit - 1;\n    query = query.range(from, to);\n\n    const { data: orders, error, count } = await query;\n\n    if (error) {\n      return NextResponse.json(\n        { error: error.message },\n        { status: 400 }\n      );\n    }\n\n    // Get total count for pagination\n    const { count: totalCount } = await supabase\n      .from('orders')\n      .select('*', { count: 'exact', head: true });\n\n    return NextResponse.json({\n      success: true,\n      data: orders,\n      pagination: {\n        page,\n        limit,\n        total: totalCount || 0,\n        totalPages: Math.ceil((totalCount || 0) / limit),\n      },\n    });\n\n  } catch (error) {\n    console.error('Get orders error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\n// POST /api/orders - Create new order\nexport async function POST(request: NextRequest) {\n  try {\n    const orderData = await request.json();\n    const {\n      customer_id,\n      branch_id,\n      cashier_id,\n      sales_rep_id,\n      table_id,\n      order_type,\n      items,\n      notes,\n      delivery_address,\n      delivery_location,\n      payment_method = 'cash',\n    } = orderData;\n\n    // Validation\n    if (!branch_id || !cashier_id || !order_type || !items || items.length === 0) {\n      return NextResponse.json(\n        { error: 'Missing required fields' },\n        { status: 400 }\n      );\n    }\n\n    // Return mock response in demo mode\n    if (isDemoMode) {\n      const orderNumber = generateOrderNumber();\n      const subtotal = items.reduce((sum: number, item: any) => sum + (item.quantity * 5.00), 0); // Mock price\n      const taxAmount = subtotal * 0.10;\n      const totalAmount = subtotal + taxAmount;\n\n      const mockOrder = {\n        id: `order-${Date.now()}`,\n        order_number: orderNumber,\n        customer_id: customer_id || null,\n        branch_id,\n        cashier_id,\n        sales_rep_id: sales_rep_id || null,\n        table_id: table_id || null,\n        order_type,\n        status: 'pending',\n        subtotal,\n        tax_amount: taxAmount,\n        discount_amount: 0,\n        total_amount: totalAmount,\n        payment_method,\n        payment_status: 'pending',\n        notes: notes || null,\n        delivery_address: delivery_address || null,\n        delivery_location: delivery_location || null,\n        estimated_delivery_time: null,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n        order_items: items.map((item: any, index: number) => ({\n          id: `item-${Date.now()}-${index}`,\n          product_id: item.product_id,\n          quantity: item.quantity,\n          unit_price: 5.00, // Mock price\n          total_price: item.quantity * 5.00,\n          notes: item.notes || null,\n        })),\n      };\n\n      return NextResponse.json(createMockApiResponse(mockOrder, true, 'Order created successfully'));\n    }\n\n    const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey);\n\n    // Generate order number\n    const orderNumber = generateOrderNumber();\n\n    // Calculate totals\n    let subtotal = 0;\n    const processedItems = [];\n\n    for (const item of items) {\n      // Get product details\n      const { data: product, error: productError } = await supabase\n        .from('products')\n        .select('id, name, price')\n        .eq('id', item.product_id)\n        .single();\n\n      if (productError || !product) {\n        return NextResponse.json(\n          { error: `Product not found: ${item.product_id}` },\n          { status: 400 }\n        );\n      }\n\n      const itemTotal = product.price * item.quantity;\n      subtotal += itemTotal;\n\n      processedItems.push({\n        product_id: item.product_id,\n        quantity: item.quantity,\n        unit_price: product.price,\n        total_price: itemTotal,\n        notes: item.notes || null,\n      });\n    }\n\n    // Calculate tax (10% for example)\n    const taxRate = 0.10;\n    const taxAmount = subtotal * taxRate;\n    const totalAmount = subtotal + taxAmount;\n\n    // Create order\n    const { data: order, error: orderError } = await supabase\n      .from('orders')\n      .insert({\n        order_number: orderNumber,\n        customer_id: customer_id || null,\n        branch_id,\n        cashier_id,\n        sales_rep_id: sales_rep_id || null,\n        table_id: table_id || null,\n        order_type,\n        subtotal,\n        tax_amount: taxAmount,\n        total_amount: totalAmount,\n        payment_method,\n        notes: notes || null,\n        delivery_address: delivery_address || null,\n        delivery_latitude: delivery_location?.latitude || null,\n        delivery_longitude: delivery_location?.longitude || null,\n      })\n      .select()\n      .single();\n\n    if (orderError) {\n      return NextResponse.json(\n        { error: orderError.message },\n        { status: 400 }\n      );\n    }\n\n    // Create order items\n    const orderItemsWithOrderId = processedItems.map(item => ({\n      ...item,\n      order_id: order.id,\n    }));\n\n    const { error: itemsError } = await supabase\n      .from('order_items')\n      .insert(orderItemsWithOrderId);\n\n    if (itemsError) {\n      // Rollback order if items creation fails\n      await supabase.from('orders').delete().eq('id', order.id);\n      return NextResponse.json(\n        { error: itemsError.message },\n        { status: 400 }\n      );\n    }\n\n    // Fetch complete order with relations\n    const { data: completeOrder } = await supabase\n      .from('orders')\n      .select(`\n        *,\n        customers (\n          id,\n          name,\n          phone\n        ),\n        order_items (\n          id,\n          quantity,\n          unit_price,\n          total_price,\n          notes,\n          products (\n            id,\n            name,\n            sku\n          )\n        )\n      `)\n      .eq('id', order.id)\n      .single();\n\n    return NextResponse.json({\n      success: true,\n      data: completeOrder,\n      message: 'Order created successfully',\n    });\n\n  } catch (error) {\n    console.error('Create order error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAGA;AACA;;;;;AAEA,MAAM,cAAc,gEAAwC;AAC5D,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB,IAAI;AAEpE,8BAA8B;AAC9B,MAAM,aAAa,gBAAgB,8BAA8B,uBAAuB;AAGjF,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,UAAU,aAAa,GAAG,CAAC;QAEjC,gCAAgC;QAChC,wCAAgB;YACd,IAAI,iBAAiB,4HAAA,CAAA,aAAU;YAE/B,gBAAgB;YAChB,IAAI,QAAQ;gBACV,iBAAiB,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;YAC3D;YACA,IAAI,WAAW;gBACb,iBAAiB,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK;YAC/D;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,CAAA,GAAA,4HAAA,CAAA,8BAA2B,AAAD,EAAE,gBAAgB,MAAM;QAC7E;;QAEA,MAAM;QAEN,cAAc;QACd,IAAI;QA0DJ,mBAAmB;QACnB,MAAM;QACN,MAAM;QAGN,MAAc,oBAAQ,mBAAO;QAS7B,iCAAiC;QACjC,MAAe;IAejB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,YAAY,MAAM,QAAQ,IAAI;QACpC,MAAM,EACJ,WAAW,EACX,SAAS,EACT,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,UAAU,EACV,KAAK,EACL,KAAK,EACL,gBAAgB,EAChB,iBAAiB,EACjB,iBAAiB,MAAM,EACxB,GAAG;QAEJ,aAAa;QACb,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,cAAc,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;YAC5E,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,oCAAoC;QACpC,wCAAgB;YACd,MAAM,cAAc,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD;YACtC,MAAM,WAAW,MAAM,MAAM,CAAC,CAAC,KAAa,OAAc,MAAO,KAAK,QAAQ,GAAG,MAAO,IAAI,aAAa;YACzG,MAAM,YAAY,WAAW;YAC7B,MAAM,cAAc,WAAW;YAE/B,MAAM,YAAY;gBAChB,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI;gBACzB,cAAc;gBACd,aAAa,eAAe;gBAC5B;gBACA;gBACA,cAAc,gBAAgB;gBAC9B,UAAU,YAAY;gBACtB;gBACA,QAAQ;gBACR;gBACA,YAAY;gBACZ,iBAAiB;gBACjB,cAAc;gBACd;gBACA,gBAAgB;gBAChB,OAAO,SAAS;gBAChB,kBAAkB,oBAAoB;gBACtC,mBAAmB,qBAAqB;gBACxC,yBAAyB;gBACzB,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;gBAClC,aAAa,MAAM,GAAG,CAAC,CAAC,MAAW,QAAkB,CAAC;wBACpD,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,OAAO;wBACjC,YAAY,KAAK,UAAU;wBAC3B,UAAU,KAAK,QAAQ;wBACvB,YAAY;wBACZ,aAAa,KAAK,QAAQ,GAAG;wBAC7B,OAAO,KAAK,KAAK,IAAI;oBACvB,CAAC;YACH;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,CAAA,GAAA,4HAAA,CAAA,wBAAqB,AAAD,EAAE,WAAW,MAAM;QAClE;;QAEA,MAAM;QAEN,wBAAwB;QACxB,MAAM;QAEN,mBAAmB;QACnB,IAAI;QACJ,MAAM;QAED,MAAM;QA2BX,kCAAkC;QAClC,MAAM;QACN,MAAM;QACN,MAAM;QAEN,eAAe;QACf,MAAc,mBAAc;QA6B5B,qBAAqB;QACrB,MAAM;QAKN,MAAe;QAaf,sCAAsC;QACtC,MAAc;IA+BhB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe;IACpB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,gCAAgC;YAChC,gCAAgC;QAClC;IACF;AACF", "debugId": null}}]}