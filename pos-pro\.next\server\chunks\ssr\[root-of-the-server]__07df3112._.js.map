{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/utils/index.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\nimport { format, parseISO } from 'date-fns';\n\n// Utility function for combining Tailwind classes\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// Currency formatting\nexport function formatCurrency(amount: number, currency: string = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: currency,\n  }).format(amount);\n}\n\n// Date formatting utilities\nexport function formatDate(date: string | Date, formatString: string = 'PPP'): string {\n  const dateObj = typeof date === 'string' ? parseISO(date) : date;\n  return format(dateObj, formatString);\n}\n\nexport function formatDateTime(date: string | Date): string {\n  return formatDate(date, 'PPP p');\n}\n\nexport function formatTime(date: string | Date): string {\n  return formatDate(date, 'p');\n}\n\n// Number formatting\nexport function formatNumber(num: number, decimals: number = 2): string {\n  return num.toFixed(decimals);\n}\n\nexport function formatPercentage(num: number, decimals: number = 1): string {\n  return `${(num * 100).toFixed(decimals)}%`;\n}\n\n// String utilities\nexport function capitalize(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\n\nexport function slugify(str: string): string {\n  return str\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '');\n}\n\n// Generate unique IDs\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function generateOrderNumber(): string {\n  const timestamp = Date.now().toString().slice(-6);\n  const random = Math.random().toString(36).substr(2, 4).toUpperCase();\n  return `ORD-${timestamp}-${random}`;\n}\n\nexport function generatePONumber(): string {\n  const timestamp = Date.now().toString().slice(-6);\n  const random = Math.random().toString(36).substr(2, 4).toUpperCase();\n  return `PO-${timestamp}-${random}`;\n}\n\n// Validation utilities\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function isValidPhone(phone: string): boolean {\n  const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]+$/;\n  return phoneRegex.test(phone) && phone.replace(/\\D/g, '').length >= 10;\n}\n\n// Geographic utilities\nexport function calculateDistance(\n  lat1: number,\n  lon1: number,\n  lat2: number,\n  lon2: number\n): number {\n  const R = 6371; // Radius of the Earth in kilometers\n  const dLat = deg2rad(lat2 - lat1);\n  const dLon = deg2rad(lon2 - lon1);\n  const a =\n    Math.sin(dLat / 2) * Math.sin(dLat / 2) +\n    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *\n    Math.sin(dLon / 2) * Math.sin(dLon / 2);\n  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n  const d = R * c; // Distance in kilometers\n  return d;\n}\n\nfunction deg2rad(deg: number): number {\n  return deg * (Math.PI / 180);\n}\n\nexport function isPointInRadius(\n  centerLat: number,\n  centerLon: number,\n  pointLat: number,\n  pointLon: number,\n  radiusKm: number\n): boolean {\n  const distance = calculateDistance(centerLat, centerLon, pointLat, pointLon);\n  return distance <= radiusKm;\n}\n\n// Check if point is inside polygon (for territory boundaries)\nexport function isPointInPolygon(\n  point: { latitude: number; longitude: number },\n  polygon: { latitude: number; longitude: number }[]\n): boolean {\n  const x = point.longitude;\n  const y = point.latitude;\n  let inside = false;\n\n  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\n    const xi = polygon[i].longitude;\n    const yi = polygon[i].latitude;\n    const xj = polygon[j].longitude;\n    const yj = polygon[j].latitude;\n\n    if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {\n      inside = !inside;\n    }\n  }\n\n  return inside;\n}\n\n// Array utilities\nexport function groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {\n  return array.reduce((groups, item) => {\n    const group = String(item[key]);\n    groups[group] = groups[group] || [];\n    groups[group].push(item);\n    return groups;\n  }, {} as Record<string, T[]>);\n}\n\nexport function sortBy<T>(array: T[], key: keyof T, direction: 'asc' | 'desc' = 'asc'): T[] {\n  return [...array].sort((a, b) => {\n    const aVal = a[key];\n    const bVal = b[key];\n    \n    if (aVal < bVal) return direction === 'asc' ? -1 : 1;\n    if (aVal > bVal) return direction === 'asc' ? 1 : -1;\n    return 0;\n  });\n}\n\n// Local storage utilities\nexport function getFromStorage<T>(key: string, defaultValue: T): T {\n  if (typeof window === 'undefined') return defaultValue;\n  \n  try {\n    const item = window.localStorage.getItem(key);\n    return item ? JSON.parse(item) : defaultValue;\n  } catch (error) {\n    console.error(`Error reading from localStorage:`, error);\n    return defaultValue;\n  }\n}\n\nexport function setToStorage<T>(key: string, value: T): void {\n  if (typeof window === 'undefined') return;\n  \n  try {\n    window.localStorage.setItem(key, JSON.stringify(value));\n  } catch (error) {\n    console.error(`Error writing to localStorage:`, error);\n  }\n}\n\nexport function removeFromStorage(key: string): void {\n  if (typeof window === 'undefined') return;\n  \n  try {\n    window.localStorage.removeItem(key);\n  } catch (error) {\n    console.error(`Error removing from localStorage:`, error);\n  }\n}\n\n// Debounce utility\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  \n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// Throttle utility\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  \n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n}\n\n// Error handling\nexport function handleError(error: unknown): string {\n  if (error instanceof Error) {\n    return error.message;\n  }\n  if (typeof error === 'string') {\n    return error;\n  }\n  return 'An unknown error occurred';\n}\n\n// Tax calculation\nexport function calculateTax(amount: number, taxRate: number): number {\n  return amount * (taxRate / 100);\n}\n\nexport function calculateTotal(subtotal: number, taxRate: number, discount: number = 0): {\n  subtotal: number;\n  taxAmount: number;\n  discountAmount: number;\n  total: number;\n} {\n  const discountAmount = subtotal * (discount / 100);\n  const taxableAmount = subtotal - discountAmount;\n  const taxAmount = calculateTax(taxableAmount, taxRate);\n  const total = taxableAmount + taxAmount;\n\n  return {\n    subtotal,\n    taxAmount,\n    discountAmount,\n    total,\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,eAAe,MAAc,EAAE,WAAmB,KAAK;IACrE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,WAAW,IAAmB,EAAE,eAAuB,KAAK;IAC1E,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;IAC5D,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;AACzB;AAEO,SAAS,eAAe,IAAmB;IAChD,OAAO,WAAW,MAAM;AAC1B;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,WAAW,MAAM;AAC1B;AAGO,SAAS,aAAa,GAAW,EAAE,WAAmB,CAAC;IAC5D,OAAO,IAAI,OAAO,CAAC;AACrB;AAEO,SAAS,iBAAiB,GAAW,EAAE,WAAmB,CAAC;IAChE,OAAO,GAAG,CAAC,MAAM,GAAG,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;AAC5C;AAGO,SAAS,WAAW,GAAW;IACpC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD;AAEO,SAAS,QAAQ,GAAW;IACjC,OAAO,IACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAGO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS;IACd,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;IAC/C,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW;IAClE,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,QAAQ;AACrC;AAEO,SAAS;IACd,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;IAC/C,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW;IAClE,OAAO,CAAC,GAAG,EAAE,UAAU,CAAC,EAAE,QAAQ;AACpC;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,UAAU,MAAM,OAAO,CAAC,OAAO,IAAI,MAAM,IAAI;AACtE;AAGO,SAAS,kBACd,IAAY,EACZ,IAAY,EACZ,IAAY,EACZ,IAAY;IAEZ,MAAM,IAAI,MAAM,oCAAoC;IACpD,MAAM,OAAO,QAAQ,OAAO;IAC5B,MAAM,OAAO,QAAQ,OAAO;IAC5B,MAAM,IACJ,KAAK,GAAG,CAAC,OAAO,KAAK,KAAK,GAAG,CAAC,OAAO,KACrC,KAAK,GAAG,CAAC,QAAQ,SAAS,KAAK,GAAG,CAAC,QAAQ,SAC3C,KAAK,GAAG,CAAC,OAAO,KAAK,KAAK,GAAG,CAAC,OAAO;IACvC,MAAM,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI;IACrD,MAAM,IAAI,IAAI,GAAG,yBAAyB;IAC1C,OAAO;AACT;AAEA,SAAS,QAAQ,GAAW;IAC1B,OAAO,MAAM,CAAC,KAAK,EAAE,GAAG,GAAG;AAC7B;AAEO,SAAS,gBACd,SAAiB,EACjB,SAAiB,EACjB,QAAgB,EAChB,QAAgB,EAChB,QAAgB;IAEhB,MAAM,WAAW,kBAAkB,WAAW,WAAW,UAAU;IACnE,OAAO,YAAY;AACrB;AAGO,SAAS,iBACd,KAA8C,EAC9C,OAAkD;IAElD,MAAM,IAAI,MAAM,SAAS;IACzB,MAAM,IAAI,MAAM,QAAQ;IACxB,IAAI,SAAS;IAEb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,GAAG,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAI,IAAK;QACnE,MAAM,KAAK,OAAO,CAAC,EAAE,CAAC,SAAS;QAC/B,MAAM,KAAK,OAAO,CAAC,EAAE,CAAC,QAAQ;QAC9B,MAAM,KAAK,OAAO,CAAC,EAAE,CAAC,SAAS;QAC/B,MAAM,KAAK,OAAO,CAAC,EAAE,CAAC,QAAQ;QAE9B,IAAI,AAAE,KAAK,MAAQ,KAAK,KAAQ,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,IAAK;YAC1E,SAAS,CAAC;QACZ;IACF;IAEA,OAAO;AACT;AAGO,SAAS,QAAW,KAAU,EAAE,GAAY;IACjD,OAAO,MAAM,MAAM,CAAC,CAAC,QAAQ;QAC3B,MAAM,QAAQ,OAAO,IAAI,CAAC,IAAI;QAC9B,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,EAAE;QACnC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;QACnB,OAAO;IACT,GAAG,CAAC;AACN;AAEO,SAAS,OAAU,KAAU,EAAE,GAAY,EAAE,YAA4B,KAAK;IACnF,OAAO;WAAI;KAAM,CAAC,IAAI,CAAC,CAAC,GAAG;QACzB,MAAM,OAAO,CAAC,CAAC,IAAI;QACnB,MAAM,OAAO,CAAC,CAAC,IAAI;QAEnB,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,CAAC,IAAI;QACnD,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,IAAI,CAAC;QACnD,OAAO;IACT;AACF;AAGO,SAAS,eAAkB,GAAW,EAAE,YAAe;IAC5D,wCAAmC,OAAO;;AAS5C;AAEO,SAAS,aAAgB,GAAW,EAAE,KAAQ;IACnD,wCAAmC;;AAOrC;AAEO,SAAS,kBAAkB,GAAW;IAC3C,wCAAmC;;AAOrC;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAM,aAAa,OAAO;QACvC;IACF;AACF;AAGO,SAAS,YAAY,KAAc;IACxC,IAAI,iBAAiB,OAAO;QAC1B,OAAO,MAAM,OAAO;IACtB;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IACA,OAAO;AACT;AAGO,SAAS,aAAa,MAAc,EAAE,OAAe;IAC1D,OAAO,SAAS,CAAC,UAAU,GAAG;AAChC;AAEO,SAAS,eAAe,QAAgB,EAAE,OAAe,EAAE,WAAmB,CAAC;IAMpF,MAAM,iBAAiB,WAAW,CAAC,WAAW,GAAG;IACjD,MAAM,gBAAgB,WAAW;IACjC,MAAM,YAAY,aAAa,eAAe;IAC9C,MAAM,QAAQ,gBAAgB;IAE9B,OAAO;QACL;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/utils';\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n  {\n    variants: {\n      variant: {\n        default: 'bg-primary text-primary-foreground hover:bg-primary/90',\n        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',\n        outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',\n        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',\n        ghost: 'hover:bg-accent hover:text-accent-foreground',\n        link: 'text-primary underline-offset-4 hover:underline',\n        success: 'bg-green-600 text-white hover:bg-green-700',\n        warning: 'bg-yellow-600 text-white hover:bg-yellow-700',\n      },\n      size: {\n        default: 'h-10 px-4 py-2',\n        sm: 'h-9 rounded-md px-3',\n        lg: 'h-11 rounded-md px-8',\n        xl: 'h-12 rounded-md px-10 text-base',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n  loading?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, loading = false, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;QACX;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC7F,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 325, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/utils';\n\nexport interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  leftIcon?: React.ReactNode;\n  rightIcon?: React.ReactNode;\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, label, error, helperText, leftIcon, rightIcon, ...props }, ref) => {\n    const inputId = React.useId();\n\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label\n            htmlFor={inputId}\n            className=\"block text-sm font-medium text-gray-700 mb-1\"\n          >\n            {label}\n          </label>\n        )}\n        <div className=\"relative\">\n          {leftIcon && (\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <div className=\"h-5 w-5 text-gray-400\">{leftIcon}</div>\n            </div>\n          )}\n          <input\n            id={inputId}\n            type={type}\n            className={cn(\n              'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n              leftIcon && 'pl-10',\n              rightIcon && 'pr-10',\n              error && 'border-red-500 focus-visible:ring-red-500',\n              className\n            )}\n            ref={ref}\n            {...props}\n          />\n          {rightIcon && (\n            <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center\">\n              <div className=\"h-5 w-5 text-gray-400\">{rightIcon}</div>\n            </div>\n          )}\n        </div>\n        {error && (\n          <p className=\"mt-1 text-sm text-red-600\">{error}</p>\n        )}\n        {helperText && !error && (\n          <p className=\"mt-1 text-sm text-gray-500\">{helperText}</p>\n        )}\n      </div>\n    );\n  }\n);\n\nInput.displayName = 'Input';\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAUA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC7E,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAE1B,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBACC,SAAS;gBACT,WAAU;0BAET;;;;;;0BAGL,8OAAC;gBAAI,WAAU;;oBACZ,0BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCAAyB;;;;;;;;;;;kCAG5C,8OAAC;wBACC,IAAI;wBACJ,MAAM;wBACN,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,gWACA,YAAY,SACZ,aAAa,SACb,SAAS,6CACT;wBAEF,KAAK;wBACJ,GAAG,KAAK;;;;;;oBAEV,2BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCAAyB;;;;;;;;;;;;;;;;;YAI7C,uBACC,8OAAC;gBAAE,WAAU;0BAA6B;;;;;;YAE3C,cAAc,CAAC,uBACd,8OAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;AAInD;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 429, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/utils';\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'rounded-lg border bg-card text-card-foreground shadow-sm',\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\n    {...props}\n  />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight',\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n));\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 510, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/app/%28auth%29/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/lib/auth-context';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { ShoppingCart, Eye, EyeOff } from 'lucide-react';\n\nexport default function LoginPage() {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [error, setError] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  \n  const { signIn } = useAuth();\n  const router = useRouter();\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n    setIsLoading(true);\n\n    try {\n      const result = await signIn(email, password);\n      \n      if (result.success) {\n        router.push('/dashboard');\n      } else {\n        setError(result.error || 'Login failed');\n      }\n    } catch (error) {\n      setError('An unexpected error occurred');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Demo accounts for testing\n  const demoAccounts = [\n    { email: '<EMAIL>', password: 'admin123', role: 'Admin' },\n    { email: '<EMAIL>', password: 'manager123', role: 'Manager' },\n    { email: '<EMAIL>', password: 'cashier123', role: 'Cashier' },\n    { email: '<EMAIL>', password: 'kitchen123', role: 'Kitchen Staff' },\n    { email: '<EMAIL>', password: 'sales123', role: 'Sales Rep' },\n  ];\n\n  const fillDemoAccount = (demoEmail: string, demoPassword: string) => {\n    setEmail(demoEmail);\n    setPassword(demoPassword);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\">\n      <div className=\"w-full max-w-md space-y-6\">\n        {/* Logo and Title */}\n        <div className=\"text-center\">\n          <div className=\"flex justify-center items-center mb-4\">\n            <ShoppingCart className=\"h-12 w-12 text-blue-600 mr-3\" />\n            <h1 className=\"text-3xl font-bold text-gray-900\">POS Pro</h1>\n          </div>\n          <p className=\"text-gray-600\">Sign in to your restaurant management system</p>\n        </div>\n\n        {/* Login Form */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Welcome Back</CardTitle>\n            <CardDescription>\n              Enter your credentials to access your account\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <form onSubmit={handleSubmit} className=\"space-y-4\">\n              <Input\n                label=\"Email Address\"\n                type=\"email\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                placeholder=\"Enter your email\"\n                required\n                disabled={isLoading}\n              />\n              \n              <Input\n                label=\"Password\"\n                type={showPassword ? 'text' : 'password'}\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n                placeholder=\"Enter your password\"\n                required\n                disabled={isLoading}\n                rightIcon={\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowPassword(!showPassword)}\n                    className=\"text-gray-400 hover:text-gray-600\"\n                  >\n                    {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}\n                  </button>\n                }\n              />\n\n              {error && (\n                <div className=\"text-red-600 text-sm bg-red-50 p-3 rounded-md\">\n                  {error}\n                </div>\n              )}\n\n              <Button\n                type=\"submit\"\n                className=\"w-full\"\n                loading={isLoading}\n                disabled={!email || !password}\n              >\n                Sign In\n              </Button>\n            </form>\n          </CardContent>\n        </Card>\n\n        {/* Demo Accounts */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-sm\">Demo Accounts</CardTitle>\n            <CardDescription className=\"text-xs\">\n              Click to auto-fill credentials for testing\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 gap-2\">\n              {demoAccounts.map((account, index) => (\n                <button\n                  key={index}\n                  onClick={() => fillDemoAccount(account.email, account.password)}\n                  className=\"text-left p-2 rounded-md hover:bg-gray-50 transition-colors text-sm\"\n                  disabled={isLoading}\n                >\n                  <div className=\"font-medium text-gray-900\">{account.role}</div>\n                  <div className=\"text-gray-500 text-xs\">{account.email}</div>\n                </button>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Footer */}\n        <div className=\"text-center text-sm text-gray-500\">\n          <p>© 2024 POS Pro. All rights reserved.</p>\n          <p className=\"mt-1\">\n            Need help? Contact{' '}\n            <a href=\"mailto:<EMAIL>\" className=\"text-blue-600 hover:underline\">\n              <EMAIL>\n            </a>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AARA;;;;;;;;;AAUe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IACzB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QACT,aAAa;QAEb,IAAI;YACF,MAAM,SAAS,MAAM,OAAO,OAAO;YAEnC,IAAI,OAAO,OAAO,EAAE;gBAClB,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,SAAS,OAAO,KAAK,IAAI;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,4BAA4B;IAC5B,MAAM,eAAe;QACnB;YAAE,OAAO;YAAoB,UAAU;YAAY,MAAM;QAAQ;QACjE;YAAE,OAAO;YAAuB,UAAU;YAAc,MAAM;QAAU;QACxE;YAAE,OAAO;YAAuB,UAAU;YAAc,MAAM;QAAU;QACxE;YAAE,OAAO;YAAuB,UAAU;YAAc,MAAM;QAAgB;QAC9E;YAAE,OAAO;YAAqB,UAAU;YAAY,MAAM;QAAY;KACvE;IAED,MAAM,kBAAkB,CAAC,WAAmB;QAC1C,SAAS;QACT,YAAY;IACd;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;;sCAEnD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAI/B,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,8OAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,8OAAC,iIAAA,CAAA,QAAK;wCACJ,OAAM;wCACN,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,aAAY;wCACZ,QAAQ;wCACR,UAAU;;;;;;kDAGZ,8OAAC,iIAAA,CAAA,QAAK;wCACJ,OAAM;wCACN,MAAM,eAAe,SAAS;wCAC9B,OAAO;wCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC3C,aAAY;wCACZ,QAAQ;wCACR,UAAU;wCACV,yBACE,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,gBAAgB,CAAC;4CAChC,WAAU;sDAET,6BAAe,8OAAC,0MAAA,CAAA,SAAM;gDAAC,MAAM;;;;;uEAAS,8OAAC,gMAAA,CAAA,MAAG;gDAAC,MAAM;;;;;;;;;;;;;;;;oCAKvD,uBACC,8OAAC;wCAAI,WAAU;kDACZ;;;;;;kDAIL,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,WAAU;wCACV,SAAS;wCACT,UAAU,CAAC,SAAS,CAAC;kDACtB;;;;;;;;;;;;;;;;;;;;;;;8BAQP,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAU;;;;;;8CAC/B,8OAAC,gIAAA,CAAA,kBAAe;oCAAC,WAAU;8CAAU;;;;;;;;;;;;sCAIvC,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,SAAS,sBAC1B,8OAAC;wCAEC,SAAS,IAAM,gBAAgB,QAAQ,KAAK,EAAE,QAAQ,QAAQ;wCAC9D,WAAU;wCACV,UAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DAA6B,QAAQ,IAAI;;;;;;0DACxD,8OAAC;gDAAI,WAAU;0DAAyB,QAAQ,KAAK;;;;;;;uCANhD;;;;;;;;;;;;;;;;;;;;;8BAcf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCAAE;;;;;;sCACH,8OAAC;4BAAE,WAAU;;gCAAO;gCACC;8CACnB,8OAAC;oCAAE,MAAK;oCAA4B,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1F", "debugId": null}}]}