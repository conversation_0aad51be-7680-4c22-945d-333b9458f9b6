{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/utils';\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'rounded-lg border bg-card text-card-foreground shadow-sm',\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\n    {...props}\n  />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight',\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n));\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/app/%28dashboard%29/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useAuth } from '@/lib/auth-context';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { \n  DollarSign, \n  ShoppingCart, \n  Clock, \n  Package, \n  Users, \n  TrendingUp,\n  AlertTriangle,\n  CheckCircle\n} from 'lucide-react';\nimport { formatCurrency, formatNumber } from '@/utils';\n\ninterface DashboardStats {\n  daily_sales: number;\n  daily_orders: number;\n  pending_orders: number;\n  low_stock_items: number;\n  active_tables: number;\n  sales_growth: number;\n  sales_by_type: Record<string, number>;\n  top_products: Array<{\n    product_id: string;\n    name: string;\n    sku: string;\n    total_quantity: number;\n    total_revenue: number;\n  }>;\n  hourly_sales: Array<{\n    hour: number;\n    sales: number;\n    orders: number;\n  }>;\n}\n\nexport default function DashboardPage() {\n  const { user } = useAuth();\n  const [stats, setStats] = useState<DashboardStats | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    fetchDashboardStats();\n  }, [user]);\n\n  const fetchDashboardStats = async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams();\n      \n      if (user?.branch_id) {\n        params.append('branch_id', user.branch_id);\n      }\n      \n      const today = new Date().toISOString().split('T')[0];\n      params.append('start_date', today);\n      params.append('end_date', today);\n\n      const response = await fetch(`/api/dashboard/stats?${params}`);\n      const data = await response.json();\n\n      if (data.success) {\n        setStats(data.data);\n      } else {\n        setError(data.error || 'Failed to fetch dashboard stats');\n      }\n    } catch (error) {\n      console.error('Error fetching dashboard stats:', error);\n      setError('Failed to fetch dashboard stats');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          {[...Array(4)].map((_, i) => (\n            <Card key={i} className=\"animate-pulse\">\n              <CardHeader className=\"pb-2\">\n                <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n              </CardHeader>\n              <CardContent>\n                <div className=\"h-8 bg-gray-200 rounded w-1/2 mb-2\"></div>\n                <div className=\"h-3 bg-gray-200 rounded w-full\"></div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"text-center py-12\">\n        <AlertTriangle className=\"h-12 w-12 text-red-500 mx-auto mb-4\" />\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Error Loading Dashboard</h3>\n        <p className=\"text-gray-600 mb-4\">{error}</p>\n        <Button onClick={fetchDashboardStats}>Try Again</Button>\n      </div>\n    );\n  }\n\n  const statCards = [\n    {\n      title: 'Daily Sales',\n      value: formatCurrency(stats?.daily_sales || 0),\n      change: stats?.sales_growth || 0,\n      icon: DollarSign,\n      color: 'text-green-600',\n      bgColor: 'bg-green-100',\n    },\n    {\n      title: 'Daily Orders',\n      value: formatNumber(stats?.daily_orders || 0, 0),\n      icon: ShoppingCart,\n      color: 'text-blue-600',\n      bgColor: 'bg-blue-100',\n    },\n    {\n      title: 'Pending Orders',\n      value: formatNumber(stats?.pending_orders || 0, 0),\n      icon: Clock,\n      color: 'text-yellow-600',\n      bgColor: 'bg-yellow-100',\n    },\n    {\n      title: 'Low Stock Items',\n      value: formatNumber(stats?.low_stock_items || 0, 0),\n      icon: Package,\n      color: 'text-red-600',\n      bgColor: 'bg-red-100',\n    },\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Welcome Header */}\n      <div className=\"mb-8\">\n        <h1 className=\"text-2xl font-bold text-gray-900\">\n          Welcome back, {user?.name}!\n        </h1>\n        <p className=\"text-gray-600\">\n          Here's what's happening at your restaurant today.\n        </p>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {statCards.map((stat, index) => (\n          <Card key={index}>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium text-gray-600\">\n                {stat.title}\n              </CardTitle>\n              <div className={`p-2 rounded-full ${stat.bgColor}`}>\n                <stat.icon className={`h-4 w-4 ${stat.color}`} />\n              </div>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-gray-900\">{stat.value}</div>\n              {stat.change !== undefined && (\n                <div className=\"flex items-center text-sm\">\n                  <TrendingUp className={`h-4 w-4 mr-1 ${\n                    stat.change >= 0 ? 'text-green-500' : 'text-red-500'\n                  }`} />\n                  <span className={stat.change >= 0 ? 'text-green-600' : 'text-red-600'}>\n                    {stat.change >= 0 ? '+' : ''}{stat.change.toFixed(1)}%\n                  </span>\n                  <span className=\"text-gray-500 ml-1\">from yesterday</span>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n\n      {/* Sales by Order Type */}\n      {stats?.sales_by_type && Object.keys(stats.sales_by_type).length > 0 && (\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Sales by Order Type</CardTitle>\n              <CardDescription>Today's revenue breakdown</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                {Object.entries(stats.sales_by_type).map(([type, amount]) => (\n                  <div key={type} className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center\">\n                      <div className=\"w-3 h-3 rounded-full bg-blue-500 mr-3\"></div>\n                      <span className=\"text-sm font-medium capitalize\">\n                        {type.replace('_', ' ')}\n                      </span>\n                    </div>\n                    <span className=\"text-sm font-semibold\">\n                      {formatCurrency(amount)}\n                    </span>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Top Products */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Top Selling Products</CardTitle>\n              <CardDescription>Most popular items today</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-3\">\n                {stats?.top_products?.slice(0, 5).map((product, index) => (\n                  <div key={product.product_id} className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center\">\n                      <div className=\"w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center mr-3\">\n                        <span className=\"text-xs font-medium text-gray-600\">\n                          {index + 1}\n                        </span>\n                      </div>\n                      <div>\n                        <div className=\"text-sm font-medium\">{product.name}</div>\n                        <div className=\"text-xs text-gray-500\">{product.sku}</div>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-sm font-semibold\">\n                        {product.total_quantity} sold\n                      </div>\n                      <div className=\"text-xs text-gray-500\">\n                        {formatCurrency(product.total_revenue)}\n                      </div>\n                    </div>\n                  </div>\n                )) || (\n                  <div className=\"text-center py-4 text-gray-500\">\n                    No sales data available\n                  </div>\n                )}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n\n      {/* Quick Actions */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Quick Actions</CardTitle>\n          <CardDescription>Common tasks and shortcuts</CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n            <Button variant=\"outline\" className=\"h-20 flex-col\" asChild>\n              <a href=\"/dashboard/pos\">\n                <ShoppingCart className=\"h-6 w-6 mb-2\" />\n                New Order\n              </a>\n            </Button>\n            <Button variant=\"outline\" className=\"h-20 flex-col\" asChild>\n              <a href=\"/dashboard/products\">\n                <Package className=\"h-6 w-6 mb-2\" />\n                Manage Products\n              </a>\n            </Button>\n            <Button variant=\"outline\" className=\"h-20 flex-col\" asChild>\n              <a href=\"/dashboard/inventory\">\n                <AlertTriangle className=\"h-6 w-6 mb-2\" />\n                Check Inventory\n              </a>\n            </Button>\n            <Button variant=\"outline\" className=\"h-20 flex-col\" asChild>\n              <a href=\"/dashboard/orders\">\n                <CheckCircle className=\"h-6 w-6 mb-2\" />\n                View Orders\n              </a>\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAhBA;;;;;;;;AAwCe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAK;IAET,MAAM,sBAAsB;QAC1B,IAAI;YACF,WAAW;YACX,MAAM,SAAS,IAAI;YAEnB,IAAI,MAAM,WAAW;gBACnB,OAAO,MAAM,CAAC,aAAa,KAAK,SAAS;YAC3C;YAEA,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACpD,OAAO,MAAM,CAAC,cAAc;YAC5B,OAAO,MAAM,CAAC,YAAY;YAE1B,MAAM,WAAW,MAAM,MAAM,CAAC,qBAAqB,EAAE,QAAQ;YAC7D,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,SAAS,KAAK,IAAI;YACpB,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,gIAAA,CAAA,OAAI;wBAAS,WAAU;;0CACtB,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC;oCAAI,WAAU;;;;;;;;;;;0CAEjB,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;uBANR;;;;;;;;;;;;;;;IAarB;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;8BACzB,8OAAC;oBAAG,WAAU;8BAAyC;;;;;;8BACvD,8OAAC;oBAAE,WAAU;8BAAsB;;;;;;8BACnC,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAS;8BAAqB;;;;;;;;;;;;IAG5C;IAEA,MAAM,YAAY;QAChB;YACE,OAAO;YACP,OAAO,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,eAAe;YAC5C,QAAQ,OAAO,gBAAgB;YAC/B,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD,EAAE,OAAO,gBAAgB,GAAG;YAC9C,MAAM,sNAAA,CAAA,eAAY;YAClB,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD,EAAE,OAAO,kBAAkB,GAAG;YAChD,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD,EAAE,OAAO,mBAAmB,GAAG;YACjD,MAAM,wMAAA,CAAA,UAAO;YACb,OAAO;YACP,SAAS;QACX;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAAmC;4BAChC,MAAM;4BAAK;;;;;;;kCAE5B,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAM/B,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAClB,KAAK,KAAK;;;;;;kDAEb,8OAAC;wCAAI,WAAW,CAAC,iBAAiB,EAAE,KAAK,OAAO,EAAE;kDAChD,cAAA,8OAAC,KAAK,IAAI;4CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,EAAE;;;;;;;;;;;;;;;;;0CAGjD,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;kDAAoC,KAAK,KAAK;;;;;;oCAC5D,KAAK,MAAM,KAAK,2BACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAW,CAAC,aAAa,EACnC,KAAK,MAAM,IAAI,IAAI,mBAAmB,gBACtC;;;;;;0DACF,8OAAC;gDAAK,WAAW,KAAK,MAAM,IAAI,IAAI,mBAAmB;;oDACpD,KAAK,MAAM,IAAI,IAAI,MAAM;oDAAI,KAAK,MAAM,CAAC,OAAO,CAAC;oDAAG;;;;;;;0DAEvD,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;;;;;;;;;;;;;;uBAnBlC;;;;;;;;;;YA4Bd,OAAO,iBAAiB,OAAO,IAAI,CAAC,MAAM,aAAa,EAAE,MAAM,GAAG,mBACjE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACZ,OAAO,OAAO,CAAC,MAAM,aAAa,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,OAAO,iBACtD,8OAAC;4CAAe,WAAU;;8DACxB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEACb,KAAK,OAAO,CAAC,KAAK;;;;;;;;;;;;8DAGvB,8OAAC;oDAAK,WAAU;8DACb,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;2CARV;;;;;;;;;;;;;;;;;;;;;kCAiBlB,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACZ,OAAO,cAAc,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,sBAC9C,8OAAC;4CAA6B,WAAU;;8DACtC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EACb,QAAQ;;;;;;;;;;;sEAGb,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAAuB,QAAQ,IAAI;;;;;;8EAClD,8OAAC;oEAAI,WAAU;8EAAyB,QAAQ,GAAG;;;;;;;;;;;;;;;;;;8DAGvD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;gEACZ,QAAQ,cAAc;gEAAC;;;;;;;sEAE1B,8OAAC;4DAAI,WAAU;sEACZ,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,aAAa;;;;;;;;;;;;;2CAjBjC,QAAQ,UAAU;;;;mEAsB5B,8OAAC;wCAAI,WAAU;kDAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW5D,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAEnB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;oCAAgB,OAAO;8CACzD,cAAA,8OAAC;wCAAE,MAAK;;0DACN,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;8CAI7C,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;oCAAgB,OAAO;8CACzD,cAAA,8OAAC;wCAAE,MAAK;;0DACN,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;8CAIxC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;oCAAgB,OAAO;8CACzD,cAAA,8OAAC;wCAAE,MAAK;;0DACN,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;8CAI9C,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;oCAAgB,OAAO;8CACzD,cAAA,8OAAC;wCAAE,MAAK;;0DACN,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxD", "debugId": null}}, {"offset": {"line": 802, "column": 0}, "map": {"version": 3, "file": "dollar-sign.js", "sources": ["file:///D:/nova/Pos%20pro/pos-pro/node_modules/lucide-react/src/icons/dollar-sign.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['line', { x1: '12', x2: '12', y1: '2', y2: '22', key: '7eqyqh' }],\n  ['path', { d: 'M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6', key: '1b0p4s' }],\n];\n\n/**\n * @component @name DollarSign\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjIiIHkyPSIyMiIgLz4KICA8cGF0aCBkPSJNMTcgNUg5LjVhMy41IDMuNSAwIDAgMCAwIDdoNWEzLjUgMy41IDAgMCAxIDAgN0g2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/dollar-sign\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst DollarSign = createLucideIcon('dollar-sign', __iconNode);\n\nexport default DollarSign;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjE;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqD,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACpF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 851, "column": 0}, "map": {"version": 3, "file": "clock.js", "sources": ["file:///D:/nova/Pos%20pro/pos-pro/node_modules/lucide-react/src/icons/clock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 6v6l4 2', key: 'mmk7yg' }],\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n];\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgNnY2bDQgMiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('clock', __iconNode);\n\nexport default Clock;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5C;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC3D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 899, "column": 0}, "map": {"version": 3, "file": "triangle-alert.js", "sources": ["file:///D:/nova/Pos%20pro/pos-pro/node_modules/lucide-react/src/icons/triangle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'm21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3',\n      key: 'wmoenq',\n    },\n  ],\n  ['path', { d: 'M12 9v4', key: 'juzpu7' }],\n  ['path', { d: 'M12 17h.01', key: 'p32p05' }],\n];\n\n/**\n * @component @name TriangleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjEuNzMgMTgtOC0xNGEyIDIgMCAwIDAtMy40OCAwbC04IDE0QTIgMiAwIDAgMCA0IDIxaDE2YTIgMiAwIDAgMCAxLjczLTMiIC8+CiAgPHBhdGggZD0iTTEyIDl2NCIgLz4KICA8cGF0aCBkPSJNMTIgMTdoLjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/triangle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TriangleAlert = createLucideIcon('triangle-alert', __iconNode);\n\nexport default TriangleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC7C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAgB,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAA,AAAjB,CAAA,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAkB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 952, "column": 0}, "map": {"version": 3, "file": "circle-check-big.js", "sources": ["file:///D:/nova/Pos%20pro/pos-pro/node_modules/lucide-react/src/icons/circle-check-big.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21.801 10A10 10 0 1 1 17 3.335', key: 'yps3ct' }],\n  ['path', { d: 'm9 11 3 3L22 4', key: '1pflzl' }],\n];\n\n/**\n * @component @name CircleCheckBig\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEuODAxIDEwQTEwIDEwIDAgMSAxIDE3IDMuMzM1IiAvPgogIDxwYXRoIGQ9Im05IDExIDMgM0wyMiA0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/circle-check-big\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleCheckBig = createLucideIcon('circle-check-big', __iconNode);\n\nexport default CircleCheckBig;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAmC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAChE;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACjD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAiB,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAoB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}