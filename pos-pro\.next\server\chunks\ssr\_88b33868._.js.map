{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/utils/index.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\nimport { format, parseISO } from 'date-fns';\n\n// Utility function for combining Tailwind classes\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// Currency formatting\nexport function formatCurrency(amount: number, currency: string = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: currency,\n  }).format(amount);\n}\n\n// Date formatting utilities\nexport function formatDate(date: string | Date, formatString: string = 'PPP'): string {\n  const dateObj = typeof date === 'string' ? parseISO(date) : date;\n  return format(dateObj, formatString);\n}\n\nexport function formatDateTime(date: string | Date): string {\n  return formatDate(date, 'PPP p');\n}\n\nexport function formatTime(date: string | Date): string {\n  return formatDate(date, 'p');\n}\n\n// Number formatting\nexport function formatNumber(num: number, decimals: number = 2): string {\n  return num.toFixed(decimals);\n}\n\nexport function formatPercentage(num: number, decimals: number = 1): string {\n  return `${(num * 100).toFixed(decimals)}%`;\n}\n\n// String utilities\nexport function capitalize(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\n\nexport function slugify(str: string): string {\n  return str\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '');\n}\n\n// Generate unique IDs\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function generateOrderNumber(): string {\n  const timestamp = Date.now().toString().slice(-6);\n  const random = Math.random().toString(36).substr(2, 4).toUpperCase();\n  return `ORD-${timestamp}-${random}`;\n}\n\nexport function generatePONumber(): string {\n  const timestamp = Date.now().toString().slice(-6);\n  const random = Math.random().toString(36).substr(2, 4).toUpperCase();\n  return `PO-${timestamp}-${random}`;\n}\n\n// Validation utilities\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function isValidPhone(phone: string): boolean {\n  const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]+$/;\n  return phoneRegex.test(phone) && phone.replace(/\\D/g, '').length >= 10;\n}\n\n// Geographic utilities\nexport function calculateDistance(\n  lat1: number,\n  lon1: number,\n  lat2: number,\n  lon2: number\n): number {\n  const R = 6371; // Radius of the Earth in kilometers\n  const dLat = deg2rad(lat2 - lat1);\n  const dLon = deg2rad(lon2 - lon1);\n  const a =\n    Math.sin(dLat / 2) * Math.sin(dLat / 2) +\n    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *\n    Math.sin(dLon / 2) * Math.sin(dLon / 2);\n  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n  const d = R * c; // Distance in kilometers\n  return d;\n}\n\nfunction deg2rad(deg: number): number {\n  return deg * (Math.PI / 180);\n}\n\nexport function isPointInRadius(\n  centerLat: number,\n  centerLon: number,\n  pointLat: number,\n  pointLon: number,\n  radiusKm: number\n): boolean {\n  const distance = calculateDistance(centerLat, centerLon, pointLat, pointLon);\n  return distance <= radiusKm;\n}\n\n// Check if point is inside polygon (for territory boundaries)\nexport function isPointInPolygon(\n  point: { latitude: number; longitude: number },\n  polygon: { latitude: number; longitude: number }[]\n): boolean {\n  const x = point.longitude;\n  const y = point.latitude;\n  let inside = false;\n\n  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\n    const xi = polygon[i].longitude;\n    const yi = polygon[i].latitude;\n    const xj = polygon[j].longitude;\n    const yj = polygon[j].latitude;\n\n    if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {\n      inside = !inside;\n    }\n  }\n\n  return inside;\n}\n\n// Array utilities\nexport function groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {\n  return array.reduce((groups, item) => {\n    const group = String(item[key]);\n    groups[group] = groups[group] || [];\n    groups[group].push(item);\n    return groups;\n  }, {} as Record<string, T[]>);\n}\n\nexport function sortBy<T>(array: T[], key: keyof T, direction: 'asc' | 'desc' = 'asc'): T[] {\n  return [...array].sort((a, b) => {\n    const aVal = a[key];\n    const bVal = b[key];\n    \n    if (aVal < bVal) return direction === 'asc' ? -1 : 1;\n    if (aVal > bVal) return direction === 'asc' ? 1 : -1;\n    return 0;\n  });\n}\n\n// Local storage utilities\nexport function getFromStorage<T>(key: string, defaultValue: T): T {\n  if (typeof window === 'undefined') return defaultValue;\n  \n  try {\n    const item = window.localStorage.getItem(key);\n    return item ? JSON.parse(item) : defaultValue;\n  } catch (error) {\n    console.error(`Error reading from localStorage:`, error);\n    return defaultValue;\n  }\n}\n\nexport function setToStorage<T>(key: string, value: T): void {\n  if (typeof window === 'undefined') return;\n  \n  try {\n    window.localStorage.setItem(key, JSON.stringify(value));\n  } catch (error) {\n    console.error(`Error writing to localStorage:`, error);\n  }\n}\n\nexport function removeFromStorage(key: string): void {\n  if (typeof window === 'undefined') return;\n  \n  try {\n    window.localStorage.removeItem(key);\n  } catch (error) {\n    console.error(`Error removing from localStorage:`, error);\n  }\n}\n\n// Debounce utility\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  \n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// Throttle utility\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  \n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n}\n\n// Error handling\nexport function handleError(error: unknown): string {\n  if (error instanceof Error) {\n    return error.message;\n  }\n  if (typeof error === 'string') {\n    return error;\n  }\n  return 'An unknown error occurred';\n}\n\n// Tax calculation\nexport function calculateTax(amount: number, taxRate: number): number {\n  return amount * (taxRate / 100);\n}\n\nexport function calculateTotal(subtotal: number, taxRate: number, discount: number = 0): {\n  subtotal: number;\n  taxAmount: number;\n  discountAmount: number;\n  total: number;\n} {\n  const discountAmount = subtotal * (discount / 100);\n  const taxableAmount = subtotal - discountAmount;\n  const taxAmount = calculateTax(taxableAmount, taxRate);\n  const total = taxableAmount + taxAmount;\n\n  return {\n    subtotal,\n    taxAmount,\n    discountAmount,\n    total,\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,eAAe,MAAc,EAAE,WAAmB,KAAK;IACrE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,WAAW,IAAmB,EAAE,eAAuB,KAAK;IAC1E,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,uIAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;IAC5D,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;AACzB;AAEO,SAAS,eAAe,IAAmB;IAChD,OAAO,WAAW,MAAM;AAC1B;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,WAAW,MAAM;AAC1B;AAGO,SAAS,aAAa,GAAW,EAAE,WAAmB,CAAC;IAC5D,OAAO,IAAI,OAAO,CAAC;AACrB;AAEO,SAAS,iBAAiB,GAAW,EAAE,WAAmB,CAAC;IAChE,OAAO,GAAG,CAAC,MAAM,GAAG,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;AAC5C;AAGO,SAAS,WAAW,GAAW;IACpC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD;AAEO,SAAS,QAAQ,GAAW;IACjC,OAAO,IACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAGO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS;IACd,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;IAC/C,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW;IAClE,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,QAAQ;AACrC;AAEO,SAAS;IACd,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;IAC/C,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW;IAClE,OAAO,CAAC,GAAG,EAAE,UAAU,CAAC,EAAE,QAAQ;AACpC;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,UAAU,MAAM,OAAO,CAAC,OAAO,IAAI,MAAM,IAAI;AACtE;AAGO,SAAS,kBACd,IAAY,EACZ,IAAY,EACZ,IAAY,EACZ,IAAY;IAEZ,MAAM,IAAI,MAAM,oCAAoC;IACpD,MAAM,OAAO,QAAQ,OAAO;IAC5B,MAAM,OAAO,QAAQ,OAAO;IAC5B,MAAM,IACJ,KAAK,GAAG,CAAC,OAAO,KAAK,KAAK,GAAG,CAAC,OAAO,KACrC,KAAK,GAAG,CAAC,QAAQ,SAAS,KAAK,GAAG,CAAC,QAAQ,SAC3C,KAAK,GAAG,CAAC,OAAO,KAAK,KAAK,GAAG,CAAC,OAAO;IACvC,MAAM,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI;IACrD,MAAM,IAAI,IAAI,GAAG,yBAAyB;IAC1C,OAAO;AACT;AAEA,SAAS,QAAQ,GAAW;IAC1B,OAAO,MAAM,CAAC,KAAK,EAAE,GAAG,GAAG;AAC7B;AAEO,SAAS,gBACd,SAAiB,EACjB,SAAiB,EACjB,QAAgB,EAChB,QAAgB,EAChB,QAAgB;IAEhB,MAAM,WAAW,kBAAkB,WAAW,WAAW,UAAU;IACnE,OAAO,YAAY;AACrB;AAGO,SAAS,iBACd,KAA8C,EAC9C,OAAkD;IAElD,MAAM,IAAI,MAAM,SAAS;IACzB,MAAM,IAAI,MAAM,QAAQ;IACxB,IAAI,SAAS;IAEb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,GAAG,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAI,IAAK;QACnE,MAAM,KAAK,OAAO,CAAC,EAAE,CAAC,SAAS;QAC/B,MAAM,KAAK,OAAO,CAAC,EAAE,CAAC,QAAQ;QAC9B,MAAM,KAAK,OAAO,CAAC,EAAE,CAAC,SAAS;QAC/B,MAAM,KAAK,OAAO,CAAC,EAAE,CAAC,QAAQ;QAE9B,IAAI,AAAE,KAAK,MAAQ,KAAK,KAAQ,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,IAAK;YAC1E,SAAS,CAAC;QACZ;IACF;IAEA,OAAO;AACT;AAGO,SAAS,QAAW,KAAU,EAAE,GAAY;IACjD,OAAO,MAAM,MAAM,CAAC,CAAC,QAAQ;QAC3B,MAAM,QAAQ,OAAO,IAAI,CAAC,IAAI;QAC9B,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,EAAE;QACnC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;QACnB,OAAO;IACT,GAAG,CAAC;AACN;AAEO,SAAS,OAAU,KAAU,EAAE,GAAY,EAAE,YAA4B,KAAK;IACnF,OAAO;WAAI;KAAM,CAAC,IAAI,CAAC,CAAC,GAAG;QACzB,MAAM,OAAO,CAAC,CAAC,IAAI;QACnB,MAAM,OAAO,CAAC,CAAC,IAAI;QAEnB,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,CAAC,IAAI;QACnD,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,IAAI,CAAC;QACnD,OAAO;IACT;AACF;AAGO,SAAS,eAAkB,GAAW,EAAE,YAAe;IAC5D,wCAAmC,OAAO;;AAS5C;AAEO,SAAS,aAAgB,GAAW,EAAE,KAAQ;IACnD,wCAAmC;;AAOrC;AAEO,SAAS,kBAAkB,GAAW;IAC3C,wCAAmC;;AAOrC;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAM,aAAa,OAAO;QACvC;IACF;AACF;AAGO,SAAS,YAAY,KAAc;IACxC,IAAI,iBAAiB,OAAO;QAC1B,OAAO,MAAM,OAAO;IACtB;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IACA,OAAO;AACT;AAGO,SAAS,aAAa,MAAc,EAAE,OAAe;IAC1D,OAAO,SAAS,CAAC,UAAU,GAAG;AAChC;AAEO,SAAS,eAAe,QAAgB,EAAE,OAAe,EAAE,WAAmB,CAAC;IAMpF,MAAM,iBAAiB,WAAW,CAAC,WAAW,GAAG;IACjD,MAAM,gBAAgB,WAAW;IACjC,MAAM,YAAY,aAAa,eAAe;IAC9C,MAAM,QAAQ,gBAAgB;IAE9B,OAAO;QACL;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/utils';\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'rounded-lg border bg-card text-card-foreground shadow-sm',\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\n    {...props}\n  />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight',\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n));\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/utils';\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n  {\n    variants: {\n      variant: {\n        default: 'bg-primary text-primary-foreground hover:bg-primary/90',\n        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',\n        outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',\n        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',\n        ghost: 'hover:bg-accent hover:text-accent-foreground',\n        link: 'text-primary underline-offset-4 hover:underline',\n        success: 'bg-green-600 text-white hover:bg-green-700',\n        warning: 'bg-yellow-600 text-white hover:bg-yellow-700',\n      },\n      size: {\n        default: 'h-10 px-4 py-2',\n        sm: 'h-9 rounded-md px-3',\n        lg: 'h-11 rounded-md px-8',\n        xl: 'h-12 rounded-md px-10 text-base',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n  loading?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, loading = false, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;QACX;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC7F,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 399, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/app/page.tsx"], "sourcesContent": ["import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { But<PERSON> } from '@/components/ui/button';\nimport {\n  ShoppingCart,\n  Package,\n  Users,\n  TrendingUp,\n  MapPin,\n  CreditCard,\n  ChefHat,\n  BarChart3\n} from 'lucide-react';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <ShoppingCart className=\"h-8 w-8 text-blue-600 mr-3\" />\n              <h1 className=\"text-2xl font-bold text-gray-900\">POS Pro</h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <Button variant=\"outline\">Login</Button>\n              <Button>Get Started</Button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-r from-blue-600 to-blue-800 text-white py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-4xl md:text-6xl font-bold mb-6\">\n            Complete POS Solution for Restaurants\n          </h2>\n          <p className=\"text-xl md:text-2xl mb-8 text-blue-100\">\n            Manage sales, inventory, delivery, and analytics all in one powerful platform\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button size=\"lg\" variant=\"secondary\">\n              Start Free Trial\n            </Button>\n            <Button size=\"lg\" variant=\"outline\" className=\"text-white border-white hover:bg-white hover:text-blue-600\">\n              Watch Demo\n            </Button>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Grid */}\n      <section className=\"py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h3 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              Everything You Need to Run Your Restaurant\n            </h3>\n            <p className=\"text-xl text-gray-600\">\n              Comprehensive modules designed for modern food service businesses\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            <Card className=\"hover:shadow-lg transition-shadow\">\n              <CardHeader>\n                <ShoppingCart className=\"h-12 w-12 text-blue-600 mb-4\" />\n                <CardTitle>Sales & Ordering</CardTitle>\n                <CardDescription>\n                  Multi-mode ordering system with dine-in, takeaway, delivery, and field sales support\n                </CardDescription>\n              </CardHeader>\n            </Card>\n\n            <Card className=\"hover:shadow-lg transition-shadow\">\n              <CardHeader>\n                <Package className=\"h-12 w-12 text-green-600 mb-4\" />\n                <CardTitle>Inventory Management</CardTitle>\n                <CardDescription>\n                  Real-time stock tracking, automated reorders, and multi-location inventory control\n                </CardDescription>\n              </CardHeader>\n            </Card>\n\n            <Card className=\"hover:shadow-lg transition-shadow\">\n              <CardHeader>\n                <MapPin className=\"h-12 w-12 text-red-600 mb-4\" />\n                <CardTitle>Delivery Tracking</CardTitle>\n                <CardDescription>\n                  OpenStreetMap integration with geofencing, route optimization, and real-time tracking\n                </CardDescription>\n              </CardHeader>\n            </Card>\n\n            <Card className=\"hover:shadow-lg transition-shadow\">\n              <CardHeader>\n                <CreditCard className=\"h-12 w-12 text-purple-600 mb-4\" />\n                <CardTitle>Financial Management</CardTitle>\n                <CardDescription>\n                  Electronic invoicing, multi-currency support, and automated financial reporting\n                </CardDescription>\n              </CardHeader>\n            </Card>\n\n            <Card className=\"hover:shadow-lg transition-shadow\">\n              <CardHeader>\n                <Users className=\"h-12 w-12 text-orange-600 mb-4\" />\n                <CardTitle>Customer Management</CardTitle>\n                <CardDescription>\n                  Customer profiles, loyalty programs, and comprehensive rating systems\n                </CardDescription>\n              </CardHeader>\n            </Card>\n\n            <Card className=\"hover:shadow-lg transition-shadow\">\n              <CardHeader>\n                <ChefHat className=\"h-12 w-12 text-yellow-600 mb-4\" />\n                <CardTitle>Kitchen Display System</CardTitle>\n                <CardDescription>\n                  Real-time order management, preparation tracking, and kitchen workflow optimization\n                </CardDescription>\n              </CardHeader>\n            </Card>\n\n            <Card className=\"hover:shadow-lg transition-shadow\">\n              <CardHeader>\n                <TrendingUp className=\"h-12 w-12 text-indigo-600 mb-4\" />\n                <CardTitle>Manufacturing & Recipes</CardTitle>\n                <CardDescription>\n                  BOM management, automated production orders, and cost tracking for prepared items\n                </CardDescription>\n              </CardHeader>\n            </Card>\n\n            <Card className=\"hover:shadow-lg transition-shadow\">\n              <CardHeader>\n                <BarChart3 className=\"h-12 w-12 text-pink-600 mb-4\" />\n                <CardTitle>Analytics & Reporting</CardTitle>\n                <CardDescription>\n                  Comprehensive dashboards with sales analytics, performance metrics, and insights\n                </CardDescription>\n              </CardHeader>\n            </Card>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-gray-900 text-white py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h3 className=\"text-3xl font-bold mb-4\">\n            Ready to Transform Your Restaurant Operations?\n          </h3>\n          <p className=\"text-xl text-gray-300 mb-8\">\n            Join thousands of restaurants already using POS Pro to streamline their operations\n          </p>\n          <Button size=\"lg\" className=\"bg-blue-600 hover:bg-blue-700\">\n            Start Your Free Trial Today\n          </Button>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t py-12\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <div className=\"flex items-center mb-4 md:mb-0\">\n              <ShoppingCart className=\"h-6 w-6 text-blue-600 mr-2\" />\n              <span className=\"text-lg font-semibold text-gray-900\">POS Pro</span>\n            </div>\n            <p className=\"text-gray-600\">\n              © 2024 POS Pro. All rights reserved. Built for modern restaurants.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;AAWe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;;;;;;;0CAEnD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;kDAAU;;;;;;kDAC1B,8OAAC,kIAAA,CAAA,SAAM;kDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOhB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,8OAAC;4BAAE,WAAU;sCAAyC;;;;;;sCAGtD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAQ;8CAAY;;;;;;8CAGtC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAQ;oCAAU,WAAU;8CAA6D;;;;;;;;;;;;;;;;;;;;;;;0BAQjH,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;0DACxB,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;;;;;;8CAMrB,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;;;;;;8CAMrB,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;;;;;;8CAMrB,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;;;;;;8CAMrB,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;;;;;;8CAMrB,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,4MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;;;;;;8CAMrB,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;;;;;;8CAMrB,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,kNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU3B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCAGxC,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC,kIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAK,WAAU;sCAAgC;;;;;;;;;;;;;;;;;0BAOhE,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,8OAAC;wCAAK,WAAU;kDAAsC;;;;;;;;;;;;0CAExD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzC", "debugId": null}}]}