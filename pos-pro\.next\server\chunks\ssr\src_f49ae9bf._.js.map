{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/utils';\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'rounded-lg border bg-card text-card-foreground shadow-sm',\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\n    {...props}\n  />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight',\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n));\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/utils';\n\nexport interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  leftIcon?: React.ReactNode;\n  rightIcon?: React.ReactNode;\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, label, error, helperText, leftIcon, rightIcon, ...props }, ref) => {\n    const inputId = React.useId();\n\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label\n            htmlFor={inputId}\n            className=\"block text-sm font-medium text-gray-700 mb-1\"\n          >\n            {label}\n          </label>\n        )}\n        <div className=\"relative\">\n          {leftIcon && (\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <div className=\"h-5 w-5 text-gray-400\">{leftIcon}</div>\n            </div>\n          )}\n          <input\n            id={inputId}\n            type={type}\n            className={cn(\n              'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n              leftIcon && 'pl-10',\n              rightIcon && 'pr-10',\n              error && 'border-red-500 focus-visible:ring-red-500',\n              className\n            )}\n            ref={ref}\n            {...props}\n          />\n          {rightIcon && (\n            <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center\">\n              <div className=\"h-5 w-5 text-gray-400\">{rightIcon}</div>\n            </div>\n          )}\n        </div>\n        {error && (\n          <p className=\"mt-1 text-sm text-red-600\">{error}</p>\n        )}\n        {helperText && !error && (\n          <p className=\"mt-1 text-sm text-gray-500\">{helperText}</p>\n        )}\n      </div>\n    );\n  }\n);\n\nInput.displayName = 'Input';\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAUA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC7E,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAE1B,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBACC,SAAS;gBACT,WAAU;0BAET;;;;;;0BAGL,8OAAC;gBAAI,WAAU;;oBACZ,0BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCAAyB;;;;;;;;;;;kCAG5C,8OAAC;wBACC,IAAI;wBACJ,MAAM;wBACN,WAAW,CAAA,GAAA,qHAAA,CAAA,KAAE,AAAD,EACV,gWACA,YAAY,SACZ,aAAa,SACb,SAAS,6CACT;wBAEF,KAAK;wBACJ,GAAG,KAAK;;;;;;oBAEV,2BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCAAyB;;;;;;;;;;;;;;;;;YAI7C,uBACC,8OAAC;gBAAE,WAAU;0BAA6B;;;;;;YAE3C,cAAc,CAAC,uBACd,8OAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;AAInD;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/app/%28dashboard%29/dashboard/products/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useAuth, usePermissions } from '@/lib/auth-context';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { \n  Package, \n  Plus, \n  Edit, \n  Trash2,\n  Search,\n  Filter,\n  Eye,\n  Copy,\n  BarChart3,\n  ChefHat\n} from 'lucide-react';\nimport { formatCurrency } from '@/utils';\nimport { Product } from '@/types';\n\ninterface ProductWithCategory extends Product {\n  category_name?: string;\n  inventory_quantity?: number;\n  sales_count?: number;\n}\n\nexport default function ProductsPage() {\n  const { user } = useAuth();\n  const permissions = usePermissions();\n  const [products, setProducts] = useState<ProductWithCategory[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [categoryFilter, setCategoryFilter] = useState('');\n  const [statusFilter, setStatusFilter] = useState('');\n  const [showCreateModal, setShowCreateModal] = useState(false);\n\n  // Mock data for demonstration\n  const mockProducts: ProductWithCategory[] = [\n    {\n      id: 'prod-1',\n      name: 'Espresso',\n      description: 'Strong coffee shot made from premium arabica beans',\n      category_id: 'cat-1',\n      category_name: 'Hot Beverages',\n      sku: 'BEV-ESP-001',\n      barcode: '1234567890123',\n      price: 2.50,\n      cost: 0.75,\n      unit: 'cup',\n      is_manufactured: true,\n      recipe_id: 'recipe-1',\n      image_url: null,\n      is_active: true,\n      inventory_quantity: 0, // Manufactured items don't have direct inventory\n      sales_count: 145,\n      created_at: '2024-01-01T00:00:00Z',\n      updated_at: '2024-01-15T10:30:00Z',\n    },\n    {\n      id: 'prod-2',\n      name: 'Cappuccino',\n      description: 'Espresso with steamed milk and foam',\n      category_id: 'cat-1',\n      category_name: 'Hot Beverages',\n      sku: 'BEV-CAP-001',\n      barcode: '1234567890124',\n      price: 4.50,\n      cost: 1.25,\n      unit: 'cup',\n      is_manufactured: true,\n      recipe_id: 'recipe-2',\n      image_url: null,\n      is_active: true,\n      inventory_quantity: 0,\n      sales_count: 98,\n      created_at: '2024-01-01T00:00:00Z',\n      updated_at: '2024-01-15T10:30:00Z',\n    },\n    {\n      id: 'prod-3',\n      name: 'Coffee Beans',\n      description: 'Premium arabica coffee beans from Colombia',\n      category_id: 'cat-2',\n      category_name: 'Ingredients',\n      sku: 'ING-CB-001',\n      barcode: '1234567890125',\n      price: 12.00,\n      cost: 8.00,\n      unit: 'lb',\n      is_manufactured: false,\n      recipe_id: null,\n      image_url: null,\n      is_active: true,\n      inventory_quantity: 45,\n      sales_count: 0, // Ingredients are not sold directly\n      created_at: '2024-01-01T00:00:00Z',\n      updated_at: '2024-01-15T10:30:00Z',\n    },\n    {\n      id: 'prod-4',\n      name: 'Caesar Salad',\n      description: 'Fresh romaine lettuce with caesar dressing and croutons',\n      category_id: 'cat-3',\n      category_name: 'Appetizers',\n      sku: 'APP-CS-001',\n      barcode: '1234567890126',\n      price: 8.50,\n      cost: 3.00,\n      unit: 'plate',\n      is_manufactured: true,\n      recipe_id: 'recipe-3',\n      image_url: null,\n      is_active: true,\n      inventory_quantity: 0,\n      sales_count: 67,\n      created_at: '2024-01-01T00:00:00Z',\n      updated_at: '2024-01-15T10:30:00Z',\n    },\n    {\n      id: 'prod-5',\n      name: 'Grilled Chicken',\n      description: 'Seasoned grilled chicken breast with herbs',\n      category_id: 'cat-4',\n      category_name: 'Main Courses',\n      sku: 'MAIN-GC-001',\n      barcode: '1234567890127',\n      price: 15.00,\n      cost: 6.00,\n      unit: 'plate',\n      is_manufactured: true,\n      recipe_id: 'recipe-4',\n      image_url: null,\n      is_active: false, // Temporarily disabled\n      inventory_quantity: 0,\n      sales_count: 23,\n      created_at: '2024-01-01T00:00:00Z',\n      updated_at: '2024-01-15T10:30:00Z',\n    },\n  ];\n\n  const categories = [\n    { id: 'cat-1', name: 'Hot Beverages' },\n    { id: 'cat-2', name: 'Ingredients' },\n    { id: 'cat-3', name: 'Appetizers' },\n    { id: 'cat-4', name: 'Main Courses' },\n    { id: 'cat-5', name: 'Desserts' },\n  ];\n\n  useEffect(() => {\n    // In a real app, this would fetch from the API\n    setProducts(mockProducts);\n    setLoading(false);\n  }, []);\n\n  const filteredProducts = products.filter(product => {\n    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         product.description?.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = !categoryFilter || product.category_id === categoryFilter;\n    const matchesStatus = !statusFilter || \n                         (statusFilter === 'active' && product.is_active) ||\n                         (statusFilter === 'inactive' && !product.is_active) ||\n                         (statusFilter === 'manufactured' && product.is_manufactured) ||\n                         (statusFilter === 'ingredient' && !product.is_manufactured);\n    return matchesSearch && matchesCategory && matchesStatus;\n  });\n\n  const toggleProductStatus = (productId: string) => {\n    setProducts(prev => prev.map(product => \n      product.id === productId \n        ? { ...product, is_active: !product.is_active }\n        : product\n    ));\n  };\n\n  const duplicateProduct = (product: ProductWithCategory) => {\n    const newProduct = {\n      ...product,\n      id: `prod-${Date.now()}`,\n      name: `${product.name} (Copy)`,\n      sku: `${product.sku}-COPY`,\n      barcode: undefined,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString(),\n    };\n    setProducts(prev => [newProduct, ...prev]);\n  };\n\n  const productStats = {\n    total: products.length,\n    active: products.filter(p => p.is_active).length,\n    manufactured: products.filter(p => p.is_manufactured).length,\n    ingredients: products.filter(p => !p.is_manufactured).length,\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-4\">\n        <h1 className=\"text-2xl font-bold\">Products</h1>\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          {[...Array(4)].map((_, i) => (\n            <Card key={i} className=\"animate-pulse\">\n              <CardContent className=\"p-6\">\n                <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n                <div className=\"h-8 bg-gray-200 rounded w-1/2\"></div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <h1 className=\"text-2xl font-bold text-gray-900\">Products</h1>\n        <div className=\"flex space-x-2\">\n          <Button variant=\"outline\" size=\"sm\">\n            <BarChart3 className=\"h-4 w-4 mr-2\" />\n            Analytics\n          </Button>\n          {permissions.canManageProducts() && (\n            <Button size=\"sm\" onClick={() => setShowCreateModal(true)}>\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Add Product\n            </Button>\n          )}\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Total Products</p>\n                <p className=\"text-2xl font-bold\">{productStats.total}</p>\n              </div>\n              <Package className=\"h-8 w-8 text-blue-600\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Active Products</p>\n                <p className=\"text-2xl font-bold text-green-600\">{productStats.active}</p>\n              </div>\n              <Eye className=\"h-8 w-8 text-green-600\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Manufactured</p>\n                <p className=\"text-2xl font-bold text-purple-600\">{productStats.manufactured}</p>\n              </div>\n              <ChefHat className=\"h-8 w-8 text-purple-600\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Ingredients</p>\n                <p className=\"text-2xl font-bold text-orange-600\">{productStats.ingredients}</p>\n              </div>\n              <Package className=\"h-8 w-8 text-orange-600\" />\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Filters */}\n      <Card>\n        <CardContent className=\"p-4\">\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <div className=\"flex-1\">\n              <Input\n                placeholder=\"Search products...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                leftIcon={<Search size={20} />}\n              />\n            </div>\n            <select\n              value={categoryFilter}\n              onChange={(e) => setCategoryFilter(e.target.value)}\n              className=\"px-3 py-2 border border-gray-300 rounded-md\"\n            >\n              <option value=\"\">All Categories</option>\n              {categories.map(category => (\n                <option key={category.id} value={category.id}>{category.name}</option>\n              ))}\n            </select>\n            <select\n              value={statusFilter}\n              onChange={(e) => setStatusFilter(e.target.value)}\n              className=\"px-3 py-2 border border-gray-300 rounded-md\"\n            >\n              <option value=\"\">All Types</option>\n              <option value=\"active\">Active Only</option>\n              <option value=\"inactive\">Inactive Only</option>\n              <option value=\"manufactured\">Manufactured</option>\n              <option value=\"ingredient\">Ingredients</option>\n            </select>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Products Table */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Product List</CardTitle>\n          <CardDescription>\n            {filteredProducts.length} products found\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full\">\n              <thead>\n                <tr className=\"border-b\">\n                  <th className=\"text-left py-3 px-4\">Product</th>\n                  <th className=\"text-left py-3 px-4\">SKU</th>\n                  <th className=\"text-left py-3 px-4\">Category</th>\n                  <th className=\"text-left py-3 px-4\">Price</th>\n                  <th className=\"text-left py-3 px-4\">Cost</th>\n                  <th className=\"text-left py-3 px-4\">Margin</th>\n                  <th className=\"text-left py-3 px-4\">Type</th>\n                  <th className=\"text-left py-3 px-4\">Status</th>\n                  <th className=\"text-left py-3 px-4\">Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                {filteredProducts.map((product) => {\n                  const margin = ((product.price - product.cost) / product.price * 100);\n                  \n                  return (\n                    <tr key={product.id} className=\"border-b hover:bg-gray-50\">\n                      <td className=\"py-3 px-4\">\n                        <div>\n                          <div className=\"font-medium\">{product.name}</div>\n                          <div className=\"text-sm text-gray-500 max-w-xs truncate\">\n                            {product.description}\n                          </div>\n                          {product.sales_count !== undefined && product.sales_count > 0 && (\n                            <div className=\"text-xs text-blue-600\">\n                              {product.sales_count} sold this month\n                            </div>\n                          )}\n                        </div>\n                      </td>\n                      <td className=\"py-3 px-4 text-sm font-mono\">{product.sku}</td>\n                      <td className=\"py-3 px-4 text-sm\">{product.category_name}</td>\n                      <td className=\"py-3 px-4 text-sm font-medium\">\n                        {formatCurrency(product.price)}\n                      </td>\n                      <td className=\"py-3 px-4 text-sm\">\n                        {formatCurrency(product.cost)}\n                      </td>\n                      <td className=\"py-3 px-4 text-sm\">\n                        <span className={`font-medium ${margin > 50 ? 'text-green-600' : margin > 30 ? 'text-yellow-600' : 'text-red-600'}`}>\n                          {margin.toFixed(1)}%\n                        </span>\n                      </td>\n                      <td className=\"py-3 px-4\">\n                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                          product.is_manufactured \n                            ? 'bg-purple-100 text-purple-800' \n                            : 'bg-orange-100 text-orange-800'\n                        }`}>\n                          {product.is_manufactured ? 'Manufactured' : 'Ingredient'}\n                        </span>\n                      </td>\n                      <td className=\"py-3 px-4\">\n                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                          product.is_active \n                            ? 'bg-green-100 text-green-800' \n                            : 'bg-red-100 text-red-800'\n                        }`}>\n                          {product.is_active ? 'Active' : 'Inactive'}\n                        </span>\n                      </td>\n                      <td className=\"py-3 px-4\">\n                        <div className=\"flex space-x-1\">\n                          <Button variant=\"outline\" size=\"sm\">\n                            <Eye className=\"h-3 w-3\" />\n                          </Button>\n                          {permissions.canManageProducts() && (\n                            <>\n                              <Button variant=\"outline\" size=\"sm\">\n                                <Edit className=\"h-3 w-3\" />\n                              </Button>\n                              <Button \n                                variant=\"outline\" \n                                size=\"sm\"\n                                onClick={() => duplicateProduct(product)}\n                              >\n                                <Copy className=\"h-3 w-3\" />\n                              </Button>\n                              <Button \n                                variant=\"outline\" \n                                size=\"sm\"\n                                onClick={() => toggleProductStatus(product.id)}\n                              >\n                                {product.is_active ? (\n                                  <Trash2 className=\"h-3 w-3 text-red-600\" />\n                                ) : (\n                                  <Eye className=\"h-3 w-3 text-green-600\" />\n                                )}\n                              </Button>\n                            </>\n                          )}\n                        </div>\n                      </td>\n                    </tr>\n                  );\n                })}\n              </tbody>\n            </table>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Create Product Modal Placeholder */}\n      {showCreateModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <Card className=\"w-full max-w-2xl mx-4\">\n            <CardHeader>\n              <CardTitle>Add New Product</CardTitle>\n              <CardDescription>Create a new product for your menu</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                <Input label=\"Product Name\" placeholder=\"Enter product name\" />\n                <Input label=\"SKU\" placeholder=\"Enter SKU\" />\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <Input label=\"Price\" type=\"number\" placeholder=\"0.00\" />\n                  <Input label=\"Cost\" type=\"number\" placeholder=\"0.00\" />\n                </div>\n                <select className=\"w-full px-3 py-2 border border-gray-300 rounded-md\">\n                  <option value=\"\">Select Category</option>\n                  {categories.map(category => (\n                    <option key={category.id} value={category.id}>{category.name}</option>\n                  ))}\n                </select>\n                <div className=\"flex items-center space-x-2\">\n                  <input type=\"checkbox\" id=\"is_manufactured\" />\n                  <label htmlFor=\"is_manufactured\" className=\"text-sm\">This is a manufactured product</label>\n                </div>\n              </div>\n              <div className=\"flex justify-end space-x-2 mt-6\">\n                <Button variant=\"outline\" onClick={() => setShowCreateModal(false)}>\n                  Cancel\n                </Button>\n                <Button onClick={() => setShowCreateModal(false)}>\n                  Create Product\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAnBA;;;;;;;;;AA4Be,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,cAAc,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,8BAA8B;IAC9B,MAAM,eAAsC;QAC1C;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,aAAa;YACb,eAAe;YACf,KAAK;YACL,SAAS;YACT,OAAO;YACP,MAAM;YACN,MAAM;YACN,iBAAiB;YACjB,WAAW;YACX,WAAW;YACX,WAAW;YACX,oBAAoB;YACpB,aAAa;YACb,YAAY;YACZ,YAAY;QACd;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,aAAa;YACb,eAAe;YACf,KAAK;YACL,SAAS;YACT,OAAO;YACP,MAAM;YACN,MAAM;YACN,iBAAiB;YACjB,WAAW;YACX,WAAW;YACX,WAAW;YACX,oBAAoB;YACpB,aAAa;YACb,YAAY;YACZ,YAAY;QACd;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,aAAa;YACb,eAAe;YACf,KAAK;YACL,SAAS;YACT,OAAO;YACP,MAAM;YACN,MAAM;YACN,iBAAiB;YACjB,WAAW;YACX,WAAW;YACX,WAAW;YACX,oBAAoB;YACpB,aAAa;YACb,YAAY;YACZ,YAAY;QACd;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,aAAa;YACb,eAAe;YACf,KAAK;YACL,SAAS;YACT,OAAO;YACP,MAAM;YACN,MAAM;YACN,iBAAiB;YACjB,WAAW;YACX,WAAW;YACX,WAAW;YACX,oBAAoB;YACpB,aAAa;YACb,YAAY;YACZ,YAAY;QACd;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,aAAa;YACb,eAAe;YACf,KAAK;YACL,SAAS;YACT,OAAO;YACP,MAAM;YACN,MAAM;YACN,iBAAiB;YACjB,WAAW;YACX,WAAW;YACX,WAAW;YACX,oBAAoB;YACpB,aAAa;YACb,YAAY;YACZ,YAAY;QACd;KACD;IAED,MAAM,aAAa;QACjB;YAAE,IAAI;YAAS,MAAM;QAAgB;QACrC;YAAE,IAAI;YAAS,MAAM;QAAc;QACnC;YAAE,IAAI;YAAS,MAAM;QAAa;QAClC;YAAE,IAAI;YAAS,MAAM;QAAe;QACpC;YAAE,IAAI;YAAS,MAAM;QAAW;KACjC;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,+CAA+C;QAC/C,YAAY;QACZ,WAAW;IACb,GAAG,EAAE;IAEL,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA;QACvC,MAAM,gBAAgB,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3D,QAAQ,GAAG,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,QAAQ,WAAW,EAAE,cAAc,SAAS,WAAW,WAAW;QACvF,MAAM,kBAAkB,CAAC,kBAAkB,QAAQ,WAAW,KAAK;QACnE,MAAM,gBAAgB,CAAC,gBACD,iBAAiB,YAAY,QAAQ,SAAS,IAC9C,iBAAiB,cAAc,CAAC,QAAQ,SAAS,IACjD,iBAAiB,kBAAkB,QAAQ,eAAe,IAC1D,iBAAiB,gBAAgB,CAAC,QAAQ,eAAe;QAC/E,OAAO,iBAAiB,mBAAmB;IAC7C;IAEA,MAAM,sBAAsB,CAAC;QAC3B,YAAY,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,UAC3B,QAAQ,EAAE,KAAK,YACX;oBAAE,GAAG,OAAO;oBAAE,WAAW,CAAC,QAAQ,SAAS;gBAAC,IAC5C;IAER;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,aAAa;YACjB,GAAG,OAAO;YACV,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;YACxB,MAAM,GAAG,QAAQ,IAAI,CAAC,OAAO,CAAC;YAC9B,KAAK,GAAG,QAAQ,GAAG,CAAC,KAAK,CAAC;YAC1B,SAAS;YACT,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;QACA,YAAY,CAAA,OAAQ;gBAAC;mBAAe;aAAK;IAC3C;IAEA,MAAM,eAAe;QACnB,OAAO,SAAS,MAAM;QACtB,QAAQ,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;QAChD,cAAc,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,eAAe,EAAE,MAAM;QAC5D,aAAa,SAAS,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,eAAe,EAAE,MAAM;IAC9D;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAqB;;;;;;8BACnC,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,gIAAA,CAAA,OAAI;4BAAS,WAAU;sCACtB,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;2BAHR;;;;;;;;;;;;;;;;IAUrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;kDAC7B,8OAAC,kNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;4BAGvC,YAAY,iBAAiB,oBAC5B,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,SAAS,IAAM,mBAAmB;;kDAClD,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAQzC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAsB,aAAa,KAAK;;;;;;;;;;;;kDAEvD,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAKzB,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAqC,aAAa,MAAM;;;;;;;;;;;;kDAEvE,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAKrB,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAsC,aAAa,YAAY;;;;;;;;;;;;kDAE9E,8OAAC,4MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAKzB,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAsC,aAAa,WAAW;;;;;;;;;;;;kDAE7E,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO3B,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,wBAAU,8OAAC,sMAAA,CAAA,SAAM;wCAAC,MAAM;;;;;;;;;;;;;;;;0CAG5B,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gCACjD,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC;4CAAyB,OAAO,SAAS,EAAE;sDAAG,SAAS,IAAI;2CAA/C,SAAS,EAAE;;;;;;;;;;;0CAG5B,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAC/C,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAG;;;;;;kDACjB,8OAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,8OAAC;wCAAO,OAAM;kDAAW;;;;;;kDACzB,8OAAC;wCAAO,OAAM;kDAAe;;;;;;kDAC7B,8OAAC;wCAAO,OAAM;kDAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOnC,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;;oCACb,iBAAiB,MAAM;oCAAC;;;;;;;;;;;;;kCAG7B,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;kDACC,cAAA,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAG,WAAU;8DAAsB;;;;;;8DACpC,8OAAC;oDAAG,WAAU;8DAAsB;;;;;;8DACpC,8OAAC;oDAAG,WAAU;8DAAsB;;;;;;8DACpC,8OAAC;oDAAG,WAAU;8DAAsB;;;;;;8DACpC,8OAAC;oDAAG,WAAU;8DAAsB;;;;;;8DACpC,8OAAC;oDAAG,WAAU;8DAAsB;;;;;;8DACpC,8OAAC;oDAAG,WAAU;8DAAsB;;;;;;8DACpC,8OAAC;oDAAG,WAAU;8DAAsB;;;;;;8DACpC,8OAAC;oDAAG,WAAU;8DAAsB;;;;;;;;;;;;;;;;;kDAGxC,8OAAC;kDACE,iBAAiB,GAAG,CAAC,CAAC;4CACrB,MAAM,SAAU,CAAC,QAAQ,KAAK,GAAG,QAAQ,IAAI,IAAI,QAAQ,KAAK,GAAG;4CAEjE,qBACE,8OAAC;gDAAoB,WAAU;;kEAC7B,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAAe,QAAQ,IAAI;;;;;;8EAC1C,8OAAC;oEAAI,WAAU;8EACZ,QAAQ,WAAW;;;;;;gEAErB,QAAQ,WAAW,KAAK,aAAa,QAAQ,WAAW,GAAG,mBAC1D,8OAAC;oEAAI,WAAU;;wEACZ,QAAQ,WAAW;wEAAC;;;;;;;;;;;;;;;;;;kEAK7B,8OAAC;wDAAG,WAAU;kEAA+B,QAAQ,GAAG;;;;;;kEACxD,8OAAC;wDAAG,WAAU;kEAAqB,QAAQ,aAAa;;;;;;kEACxD,8OAAC;wDAAG,WAAU;kEACX,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,KAAK;;;;;;kEAE/B,8OAAC;wDAAG,WAAU;kEACX,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,IAAI;;;;;;kEAE9B,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAK,WAAW,CAAC,YAAY,EAAE,SAAS,KAAK,mBAAmB,SAAS,KAAK,oBAAoB,gBAAgB;;gEAChH,OAAO,OAAO,CAAC;gEAAG;;;;;;;;;;;;kEAGvB,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAK,WAAW,CAAC,2CAA2C,EAC3D,QAAQ,eAAe,GACnB,kCACA,iCACJ;sEACC,QAAQ,eAAe,GAAG,iBAAiB;;;;;;;;;;;kEAGhD,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAK,WAAW,CAAC,2CAA2C,EAC3D,QAAQ,SAAS,GACb,gCACA,2BACJ;sEACC,QAAQ,SAAS,GAAG,WAAW;;;;;;;;;;;kEAGpC,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEAAC,SAAQ;oEAAU,MAAK;8EAC7B,cAAA,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;gEAEhB,YAAY,iBAAiB,oBAC5B;;sFACE,8OAAC,kIAAA,CAAA,SAAM;4EAAC,SAAQ;4EAAU,MAAK;sFAC7B,cAAA,8OAAC,2MAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;;;;;;sFAElB,8OAAC,kIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,MAAK;4EACL,SAAS,IAAM,iBAAiB;sFAEhC,cAAA,8OAAC,kMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;;;;;;sFAElB,8OAAC,kIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,MAAK;4EACL,SAAS,IAAM,oBAAoB,QAAQ,EAAE;sFAE5C,QAAQ,SAAS,iBAChB,8OAAC,0MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;qGAElB,8OAAC,gMAAA,CAAA,MAAG;gFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;+CAtEpB,QAAQ,EAAE;;;;;wCA+EvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQT,iCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,8OAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAEnB,8OAAC,gIAAA,CAAA,cAAW;;8CACV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,OAAM;4CAAe,aAAY;;;;;;sDACxC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,OAAM;4CAAM,aAAY;;;;;;sDAC/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,OAAM;oDAAQ,MAAK;oDAAS,aAAY;;;;;;8DAC/C,8OAAC,iIAAA,CAAA,QAAK;oDAAC,OAAM;oDAAO,MAAK;oDAAS,aAAY;;;;;;;;;;;;sDAEhD,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC;wDAAyB,OAAO,SAAS,EAAE;kEAAG,SAAS,IAAI;uDAA/C,SAAS,EAAE;;;;;;;;;;;sDAG5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,MAAK;oDAAW,IAAG;;;;;;8DAC1B,8OAAC;oDAAM,SAAQ;oDAAkB,WAAU;8DAAU;;;;;;;;;;;;;;;;;;8CAGzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS,IAAM,mBAAmB;sDAAQ;;;;;;sDAGpE,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAS,IAAM,mBAAmB;sDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUlE", "debugId": null}}]}