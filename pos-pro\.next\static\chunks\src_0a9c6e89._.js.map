{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/utils';\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'rounded-lg border bg-card text-card-foreground shadow-sm',\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\n    {...props}\n  />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight',\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n));\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/utils';\n\nexport interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  leftIcon?: React.ReactNode;\n  rightIcon?: React.ReactNode;\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, label, error, helperText, leftIcon, rightIcon, ...props }, ref) => {\n    const inputId = React.useId();\n\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label\n            htmlFor={inputId}\n            className=\"block text-sm font-medium text-gray-700 mb-1\"\n          >\n            {label}\n          </label>\n        )}\n        <div className=\"relative\">\n          {leftIcon && (\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <div className=\"h-5 w-5 text-gray-400\">{leftIcon}</div>\n            </div>\n          )}\n          <input\n            id={inputId}\n            type={type}\n            className={cn(\n              'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n              leftIcon && 'pl-10',\n              rightIcon && 'pr-10',\n              error && 'border-red-500 focus-visible:ring-red-500',\n              className\n            )}\n            ref={ref}\n            {...props}\n          />\n          {rightIcon && (\n            <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center\">\n              <div className=\"h-5 w-5 text-gray-400\">{rightIcon}</div>\n            </div>\n          )}\n        </div>\n        {error && (\n          <p className=\"mt-1 text-sm text-red-600\">{error}</p>\n        )}\n        {helperText && !error && (\n          <p className=\"mt-1 text-sm text-gray-500\">{helperText}</p>\n        )}\n      </div>\n    );\n  }\n);\n\nInput.displayName = 'Input';\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAUA,MAAM,sBAAQ,GAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,UAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;;IAC7E,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,QAAW,AAAD;IAE1B,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;0BAET;;;;;;0BAGL,6LAAC;gBAAI,WAAU;;oBACZ,0BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCAAyB;;;;;;;;;;;kCAG5C,6LAAC;wBACC,IAAI;wBACJ,MAAM;wBACN,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,gWACA,YAAY,SACZ,aAAa,SACb,SAAS,6CACT;wBAEF,KAAK;wBACJ,GAAG,KAAK;;;;;;oBAEV,2BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCAAyB;;;;;;;;;;;;;;;;;YAI7C,uBACC,6LAAC;gBAAE,WAAU;0BAA6B;;;;;;YAE3C,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;AAInD;;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/app/%28dashboard%29/dashboard/manufacturing/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useAuth, usePermissions } from '@/lib/auth-context';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { \n  ChefHat, \n  Plus, \n  Edit, \n  Eye,\n  Clock,\n  CheckCircle,\n  Play,\n  Pause,\n  Search,\n  Filter,\n  Package,\n  Utensils,\n  Timer,\n  AlertTriangle\n} from 'lucide-react';\nimport { formatCurrency, formatDateTime } from '@/utils';\n\ninterface Recipe {\n  id: string;\n  name: string;\n  product_name: string;\n  description: string;\n  yield_quantity: number;\n  preparation_time: number; // in minutes\n  cost_per_unit: number;\n  instructions: string;\n  is_active: boolean;\n  ingredients: Array<{\n    id: string;\n    ingredient_name: string;\n    ingredient_sku: string;\n    quantity: number;\n    unit: string;\n    cost: number;\n  }>;\n  created_at: string;\n  updated_at: string;\n}\n\ninterface ManufacturingOrder {\n  id: string;\n  mo_number: string;\n  recipe_name: string;\n  product_name: string;\n  quantity_to_produce: number;\n  quantity_produced: number;\n  branch_name: string;\n  status: 'planned' | 'in_progress' | 'completed' | 'cancelled';\n  priority: 'low' | 'normal' | 'high' | 'urgent';\n  estimated_completion: string;\n  actual_completion?: string;\n  assigned_to?: string;\n  notes?: string;\n  created_by: string;\n  created_at: string;\n  updated_at: string;\n}\n\nconst statusColors = {\n  planned: 'bg-blue-100 text-blue-800',\n  in_progress: 'bg-orange-100 text-orange-800',\n  completed: 'bg-green-100 text-green-800',\n  cancelled: 'bg-red-100 text-red-800',\n};\n\nconst priorityColors = {\n  low: 'bg-gray-100 text-gray-800',\n  normal: 'bg-blue-100 text-blue-800',\n  high: 'bg-yellow-100 text-yellow-800',\n  urgent: 'bg-red-100 text-red-800',\n};\n\nexport default function ManufacturingPage() {\n  const { user } = useAuth();\n  const permissions = usePermissions();\n  const [recipes, setRecipes] = useState<Recipe[]>([]);\n  const [manufacturingOrders, setManufacturingOrders] = useState<ManufacturingOrder[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('');\n  const [selectedTab, setSelectedTab] = useState<'orders' | 'recipes'>('orders');\n  const [showCreateModal, setShowCreateModal] = useState(false);\n\n  // Mock data for demonstration\n  const mockRecipes: Recipe[] = [\n    {\n      id: 'recipe-1',\n      name: 'Cappuccino Recipe',\n      product_name: 'Cappuccino',\n      description: 'Classic cappuccino with perfect foam',\n      yield_quantity: 1,\n      preparation_time: 4,\n      cost_per_unit: 1.25,\n      instructions: '1. Grind 18g coffee beans\\n2. Extract espresso shot\\n3. Steam milk to 150°F\\n4. Pour steamed milk over espresso\\n5. Top with foam',\n      is_active: true,\n      ingredients: [\n        {\n          id: 'ing-1',\n          ingredient_name: 'Coffee Beans',\n          ingredient_sku: 'ING-CB-001',\n          quantity: 0.04,\n          unit: 'lb',\n          cost: 0.32,\n        },\n        {\n          id: 'ing-2',\n          ingredient_name: 'Whole Milk',\n          ingredient_sku: 'ING-MLK-001',\n          quantity: 0.02,\n          unit: 'gallon',\n          cost: 0.07,\n        },\n      ],\n      created_at: '2024-01-01T00:00:00Z',\n      updated_at: '2024-01-15T10:30:00Z',\n    },\n    {\n      id: 'recipe-2',\n      name: 'Caesar Salad Recipe',\n      product_name: 'Caesar Salad',\n      description: 'Fresh romaine with homemade caesar dressing',\n      yield_quantity: 1,\n      preparation_time: 8,\n      cost_per_unit: 3.00,\n      instructions: '1. Wash and chop romaine lettuce\\n2. Prepare caesar dressing\\n3. Toss lettuce with dressing\\n4. Add croutons and parmesan\\n5. Serve immediately',\n      is_active: true,\n      ingredients: [\n        {\n          id: 'ing-3',\n          ingredient_name: 'Romaine Lettuce',\n          ingredient_sku: 'ING-LET-ROM',\n          quantity: 1,\n          unit: 'head',\n          cost: 1.50,\n        },\n        {\n          id: 'ing-4',\n          ingredient_name: 'Parmesan Cheese',\n          ingredient_sku: 'ING-CHE-PAR',\n          quantity: 0.25,\n          unit: 'cup',\n          cost: 0.75,\n        },\n        {\n          id: 'ing-5',\n          ingredient_name: 'Croutons',\n          ingredient_sku: 'ING-CRO-001',\n          quantity: 0.5,\n          unit: 'cup',\n          cost: 0.50,\n        },\n      ],\n      created_at: '2024-01-01T00:00:00Z',\n      updated_at: '2024-01-15T10:30:00Z',\n    },\n  ];\n\n  const mockManufacturingOrders: ManufacturingOrder[] = [\n    {\n      id: 'mo-1',\n      mo_number: 'MO-001-2024',\n      recipe_name: 'Cappuccino Recipe',\n      product_name: 'Cappuccino',\n      quantity_to_produce: 50,\n      quantity_produced: 35,\n      branch_name: 'Downtown Branch',\n      status: 'in_progress',\n      priority: 'normal',\n      estimated_completion: '2024-01-15T16:00:00Z',\n      assigned_to: 'Chef Mike',\n      notes: 'Morning rush preparation',\n      created_by: 'John Manager',\n      created_at: '2024-01-15T08:00:00Z',\n      updated_at: '2024-01-15T14:30:00Z',\n    },\n    {\n      id: 'mo-2',\n      mo_number: 'MO-002-2024',\n      recipe_name: 'Caesar Salad Recipe',\n      product_name: 'Caesar Salad',\n      quantity_to_produce: 20,\n      quantity_produced: 0,\n      branch_name: 'Downtown Branch',\n      status: 'planned',\n      priority: 'high',\n      estimated_completion: '2024-01-15T18:00:00Z',\n      assigned_to: 'Chef Sarah',\n      notes: 'Lunch prep for tomorrow',\n      created_by: 'John Manager',\n      created_at: '2024-01-15T10:00:00Z',\n      updated_at: '2024-01-15T10:00:00Z',\n    },\n    {\n      id: 'mo-3',\n      mo_number: 'MO-003-2024',\n      recipe_name: 'Cappuccino Recipe',\n      product_name: 'Cappuccino',\n      quantity_to_produce: 30,\n      quantity_produced: 30,\n      branch_name: 'Downtown Branch',\n      status: 'completed',\n      priority: 'normal',\n      estimated_completion: '2024-01-14T12:00:00Z',\n      actual_completion: '2024-01-14T11:45:00Z',\n      assigned_to: 'Chef Mike',\n      created_by: 'John Manager',\n      created_at: '2024-01-14T09:00:00Z',\n      updated_at: '2024-01-14T11:45:00Z',\n    },\n  ];\n\n  useEffect(() => {\n    // In a real app, this would fetch from the API\n    setRecipes(mockRecipes);\n    setManufacturingOrders(mockManufacturingOrders);\n    setLoading(false);\n  }, []);\n\n  const filteredManufacturingOrders = manufacturingOrders.filter(mo => {\n    const matchesSearch = mo.mo_number.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         mo.product_name.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesStatus = !statusFilter || mo.status === statusFilter;\n    return matchesSearch && matchesStatus;\n  });\n\n  const filteredRecipes = recipes.filter(recipe => \n    recipe.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    recipe.product_name.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  const updateMOStatus = (moId: string, newStatus: ManufacturingOrder['status']) => {\n    setManufacturingOrders(prev => prev.map(mo => \n      mo.id === moId \n        ? { \n            ...mo, \n            status: newStatus,\n            actual_completion: newStatus === 'completed' ? new Date().toISOString() : mo.actual_completion,\n            updated_at: new Date().toISOString()\n          }\n        : mo\n    ));\n  };\n\n  const updateProducedQuantity = (moId: string, quantity: number) => {\n    setManufacturingOrders(prev => prev.map(mo => \n      mo.id === moId \n        ? { \n            ...mo, \n            quantity_produced: quantity,\n            status: quantity >= mo.quantity_to_produce ? 'completed' : mo.status,\n            actual_completion: quantity >= mo.quantity_to_produce ? new Date().toISOString() : mo.actual_completion,\n            updated_at: new Date().toISOString()\n          }\n        : mo\n    ));\n  };\n\n  const moStats = {\n    total: manufacturingOrders.length,\n    planned: manufacturingOrders.filter(mo => mo.status === 'planned').length,\n    in_progress: manufacturingOrders.filter(mo => mo.status === 'in_progress').length,\n    completed: manufacturingOrders.filter(mo => mo.status === 'completed').length,\n    efficiency: manufacturingOrders.filter(mo => mo.status === 'completed').length / manufacturingOrders.length * 100,\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-4\">\n        <h1 className=\"text-2xl font-bold\">Manufacturing</h1>\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          {[...Array(4)].map((_, i) => (\n            <Card key={i} className=\"animate-pulse\">\n              <CardContent className=\"p-6\">\n                <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n                <div className=\"h-8 bg-gray-200 rounded w-1/2\"></div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <h1 className=\"text-2xl font-bold text-gray-900\">Manufacturing</h1>\n        <div className=\"flex space-x-2\">\n          <Button variant=\"outline\" size=\"sm\">\n            <Timer className=\"h-4 w-4 mr-2\" />\n            Production Schedule\n          </Button>\n          {permissions.canManageProducts() && (\n            <Button size=\"sm\" onClick={() => setShowCreateModal(true)}>\n              <Plus className=\"h-4 w-4 mr-2\" />\n              {selectedTab === 'orders' ? 'New Manufacturing Order' : 'New Recipe'}\n            </Button>\n          )}\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Total Orders</p>\n                <p className=\"text-2xl font-bold\">{moStats.total}</p>\n              </div>\n              <Package className=\"h-8 w-8 text-blue-600\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">In Progress</p>\n                <p className=\"text-2xl font-bold text-orange-600\">{moStats.in_progress}</p>\n              </div>\n              <Play className=\"h-8 w-8 text-orange-600\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Completed</p>\n                <p className=\"text-2xl font-bold text-green-600\">{moStats.completed}</p>\n              </div>\n              <CheckCircle className=\"h-8 w-8 text-green-600\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Efficiency</p>\n                <p className=\"text-2xl font-bold text-purple-600\">{moStats.efficiency.toFixed(0)}%</p>\n              </div>\n              <ChefHat className=\"h-8 w-8 text-purple-600\" />\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"border-b border-gray-200\">\n        <nav className=\"-mb-px flex space-x-8\">\n          <button\n            onClick={() => setSelectedTab('orders')}\n            className={`py-2 px-1 border-b-2 font-medium text-sm ${\n              selectedTab === 'orders'\n                ? 'border-blue-500 text-blue-600'\n                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n            }`}\n          >\n            Manufacturing Orders\n          </button>\n          <button\n            onClick={() => setSelectedTab('recipes')}\n            className={`py-2 px-1 border-b-2 font-medium text-sm ${\n              selectedTab === 'recipes'\n                ? 'border-blue-500 text-blue-600'\n                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n            }`}\n          >\n            Recipes & BOM\n          </button>\n        </nav>\n      </div>\n\n      {/* Filters */}\n      <Card>\n        <CardContent className=\"p-4\">\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <div className=\"flex-1\">\n              <Input\n                placeholder={selectedTab === 'orders' ? \"Search manufacturing orders...\" : \"Search recipes...\"}\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                leftIcon={<Search size={20} />}\n              />\n            </div>\n            {selectedTab === 'orders' && (\n              <select\n                value={statusFilter}\n                onChange={(e) => setStatusFilter(e.target.value)}\n                className=\"px-3 py-2 border border-gray-300 rounded-md\"\n              >\n                <option value=\"\">All Statuses</option>\n                <option value=\"planned\">Planned</option>\n                <option value=\"in_progress\">In Progress</option>\n                <option value=\"completed\">Completed</option>\n                <option value=\"cancelled\">Cancelled</option>\n              </select>\n            )}\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Content */}\n      {selectedTab === 'orders' ? (\n        <div className=\"space-y-4\">\n          {filteredManufacturingOrders.length === 0 ? (\n            <Card>\n              <CardContent className=\"p-12 text-center\">\n                <Package className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No manufacturing orders found</h3>\n                <p className=\"text-gray-600\">Create your first manufacturing order to get started</p>\n              </CardContent>\n            </Card>\n          ) : (\n            filteredManufacturingOrders.map((mo) => {\n              const completionRate = (mo.quantity_produced / mo.quantity_to_produce) * 100;\n              const isOverdue = new Date(mo.estimated_completion) < new Date() && mo.status !== 'completed';\n              \n              return (\n                <Card key={mo.id} className={isOverdue ? 'border-red-300 bg-red-50' : ''}>\n                  <CardContent className=\"p-6\">\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <div className=\"flex items-center space-x-4\">\n                        <div>\n                          <h3 className=\"text-lg font-semibold flex items-center\">\n                            {mo.mo_number}\n                            {isOverdue && <AlertTriangle className=\"h-4 w-4 text-red-500 ml-2\" />}\n                          </h3>\n                          <p className=\"text-sm text-gray-500\">\n                            {mo.product_name} • {formatDateTime(mo.created_at)}\n                          </p>\n                        </div>\n                        <div className=\"flex space-x-2\">\n                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusColors[mo.status]}`}>\n                            {mo.status.replace('_', ' ').toUpperCase()}\n                          </span>\n                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${priorityColors[mo.priority]}`}>\n                            {mo.priority.toUpperCase()}\n                          </span>\n                        </div>\n                      </div>\n                      <div className=\"text-right\">\n                        <div className=\"text-lg font-bold\">\n                          {mo.quantity_produced}/{mo.quantity_to_produce}\n                        </div>\n                        <div className=\"text-sm text-gray-500\">units</div>\n                      </div>\n                    </div>\n\n                    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\">\n                      <div>\n                        <label className=\"text-sm font-medium text-gray-500\">Assigned To</label>\n                        <p className=\"text-sm\">{mo.assigned_to || 'Unassigned'}</p>\n                      </div>\n                      <div>\n                        <label className=\"text-sm font-medium text-gray-500\">Estimated Completion</label>\n                        <p className=\"text-sm\">\n                          {new Date(mo.estimated_completion).toLocaleString()}\n                        </p>\n                      </div>\n                      <div>\n                        <label className=\"text-sm font-medium text-gray-500\">Progress</label>\n                        <div className=\"flex items-center space-x-2\">\n                          <div className=\"flex-1 bg-gray-200 rounded-full h-2\">\n                            <div \n                              className=\"bg-green-600 h-2 rounded-full\" \n                              style={{ width: `${completionRate}%` }}\n                            ></div>\n                          </div>\n                          <span className=\"text-sm font-medium\">{completionRate.toFixed(0)}%</span>\n                        </div>\n                      </div>\n                    </div>\n\n                    {mo.notes && (\n                      <div className=\"mb-4\">\n                        <label className=\"text-sm font-medium text-gray-500\">Notes</label>\n                        <p className=\"text-sm text-gray-700\">{mo.notes}</p>\n                      </div>\n                    )}\n\n                    {/* Actions */}\n                    <div className=\"flex justify-between items-center\">\n                      <div className=\"flex space-x-2\">\n                        <Button variant=\"outline\" size=\"sm\">\n                          <Eye className=\"h-4 w-4 mr-1\" />\n                          View Recipe\n                        </Button>\n                        {mo.status === 'in_progress' && (\n                          <div className=\"flex items-center space-x-2\">\n                            <Input\n                              type=\"number\"\n                              placeholder=\"Qty\"\n                              className=\"w-20\"\n                              max={mo.quantity_to_produce}\n                              min={0}\n                              defaultValue={mo.quantity_produced}\n                              onBlur={(e) => updateProducedQuantity(mo.id, parseInt(e.target.value) || 0)}\n                            />\n                            <span className=\"text-sm text-gray-500\">produced</span>\n                          </div>\n                        )}\n                      </div>\n                      \n                      <div className=\"flex space-x-2\">\n                        {mo.status === 'planned' && (\n                          <Button\n                            size=\"sm\"\n                            onClick={() => updateMOStatus(mo.id, 'in_progress')}\n                          >\n                            <Play className=\"h-4 w-4 mr-1\" />\n                            Start Production\n                          </Button>\n                        )}\n                        {mo.status === 'in_progress' && (\n                          <Button\n                            size=\"sm\"\n                            onClick={() => updateMOStatus(mo.id, 'completed')}\n                          >\n                            <CheckCircle className=\"h-4 w-4 mr-1\" />\n                            Complete\n                          </Button>\n                        )}\n                        {mo.status !== 'completed' && mo.status !== 'cancelled' && (\n                          <Button\n                            variant=\"outline\"\n                            size=\"sm\"\n                            onClick={() => updateMOStatus(mo.id, 'cancelled')}\n                          >\n                            Cancel\n                          </Button>\n                        )}\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              );\n            })\n          )}\n        </div>\n      ) : (\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {filteredRecipes.map((recipe) => (\n            <Card key={recipe.id}>\n              <CardContent className=\"p-6\">\n                <div className=\"flex justify-between items-start mb-4\">\n                  <div>\n                    <h3 className=\"text-lg font-semibold\">{recipe.name}</h3>\n                    <p className=\"text-sm text-gray-500\">{recipe.product_name}</p>\n                    <p className=\"text-sm text-gray-600 mt-1\">{recipe.description}</p>\n                  </div>\n                  <div className=\"text-right\">\n                    <div className=\"text-sm font-medium\">\n                      {formatCurrency(recipe.cost_per_unit)}/unit\n                    </div>\n                    <div className=\"text-xs text-gray-500\">\n                      {recipe.preparation_time} min prep\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"mb-4\">\n                  <label className=\"text-sm font-medium text-gray-500 mb-2 block\">Ingredients</label>\n                  <div className=\"space-y-1\">\n                    {recipe.ingredients.map((ingredient) => (\n                      <div key={ingredient.id} className=\"flex justify-between text-sm\">\n                        <span>\n                          {ingredient.quantity} {ingredient.unit} {ingredient.ingredient_name}\n                        </span>\n                        <span>{formatCurrency(ingredient.cost)}</span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                <div className=\"mb-4\">\n                  <label className=\"text-sm font-medium text-gray-500 mb-2 block\">Instructions</label>\n                  <div className=\"text-sm text-gray-700 whitespace-pre-line max-h-32 overflow-y-auto\">\n                    {recipe.instructions}\n                  </div>\n                </div>\n\n                <div className=\"flex justify-between items-center\">\n                  <div className=\"text-sm text-gray-500\">\n                    Yield: {recipe.yield_quantity} unit(s)\n                  </div>\n                  <div className=\"flex space-x-2\">\n                    <Button variant=\"outline\" size=\"sm\">\n                      <Edit className=\"h-3 w-3 mr-1\" />\n                      Edit\n                    </Button>\n                    <Button size=\"sm\">\n                      <Plus className=\"h-3 w-3 mr-1\" />\n                      Create MO\n                    </Button>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;;;AAvBA;;;;;;;;AAkEA,MAAM,eAAe;IACnB,SAAS;IACT,aAAa;IACb,WAAW;IACX,WAAW;AACb;AAEA,MAAM,iBAAiB;IACrB,KAAK;IACL,QAAQ;IACR,MAAM;IACN,QAAQ;AACV;AAEe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB,EAAE;IACvF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACrE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,8BAA8B;IAC9B,MAAM,cAAwB;QAC5B;YACE,IAAI;YACJ,MAAM;YACN,cAAc;YACd,aAAa;YACb,gBAAgB;YAChB,kBAAkB;YAClB,eAAe;YACf,cAAc;YACd,WAAW;YACX,aAAa;gBACX;oBACE,IAAI;oBACJ,iBAAiB;oBACjB,gBAAgB;oBAChB,UAAU;oBACV,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,IAAI;oBACJ,iBAAiB;oBACjB,gBAAgB;oBAChB,UAAU;oBACV,MAAM;oBACN,MAAM;gBACR;aACD;YACD,YAAY;YACZ,YAAY;QACd;QACA;YACE,IAAI;YACJ,MAAM;YACN,cAAc;YACd,aAAa;YACb,gBAAgB;YAChB,kBAAkB;YAClB,eAAe;YACf,cAAc;YACd,WAAW;YACX,aAAa;gBACX;oBACE,IAAI;oBACJ,iBAAiB;oBACjB,gBAAgB;oBAChB,UAAU;oBACV,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,IAAI;oBACJ,iBAAiB;oBACjB,gBAAgB;oBAChB,UAAU;oBACV,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,IAAI;oBACJ,iBAAiB;oBACjB,gBAAgB;oBAChB,UAAU;oBACV,MAAM;oBACN,MAAM;gBACR;aACD;YACD,YAAY;YACZ,YAAY;QACd;KACD;IAED,MAAM,0BAAgD;QACpD;YACE,IAAI;YACJ,WAAW;YACX,aAAa;YACb,cAAc;YACd,qBAAqB;YACrB,mBAAmB;YACnB,aAAa;YACb,QAAQ;YACR,UAAU;YACV,sBAAsB;YACtB,aAAa;YACb,OAAO;YACP,YAAY;YACZ,YAAY;YACZ,YAAY;QACd;QACA;YACE,IAAI;YACJ,WAAW;YACX,aAAa;YACb,cAAc;YACd,qBAAqB;YACrB,mBAAmB;YACnB,aAAa;YACb,QAAQ;YACR,UAAU;YACV,sBAAsB;YACtB,aAAa;YACb,OAAO;YACP,YAAY;YACZ,YAAY;YACZ,YAAY;QACd;QACA;YACE,IAAI;YACJ,WAAW;YACX,aAAa;YACb,cAAc;YACd,qBAAqB;YACrB,mBAAmB;YACnB,aAAa;YACb,QAAQ;YACR,UAAU;YACV,sBAAsB;YACtB,mBAAmB;YACnB,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,YAAY;QACd;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,+CAA+C;YAC/C,WAAW;YACX,uBAAuB;YACvB,WAAW;QACb;sCAAG,EAAE;IAEL,MAAM,8BAA8B,oBAAoB,MAAM,CAAC,CAAA;QAC7D,MAAM,gBAAgB,GAAG,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3D,GAAG,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAClF,MAAM,gBAAgB,CAAC,gBAAgB,GAAG,MAAM,KAAK;QACrD,OAAO,iBAAiB;IAC1B;IAEA,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA,SACrC,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,OAAO,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAGnE,MAAM,iBAAiB,CAAC,MAAc;QACpC,uBAAuB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,KACtC,GAAG,EAAE,KAAK,OACN;oBACE,GAAG,EAAE;oBACL,QAAQ;oBACR,mBAAmB,cAAc,cAAc,IAAI,OAAO,WAAW,KAAK,GAAG,iBAAiB;oBAC9F,YAAY,IAAI,OAAO,WAAW;gBACpC,IACA;IAER;IAEA,MAAM,yBAAyB,CAAC,MAAc;QAC5C,uBAAuB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,KACtC,GAAG,EAAE,KAAK,OACN;oBACE,GAAG,EAAE;oBACL,mBAAmB;oBACnB,QAAQ,YAAY,GAAG,mBAAmB,GAAG,cAAc,GAAG,MAAM;oBACpE,mBAAmB,YAAY,GAAG,mBAAmB,GAAG,IAAI,OAAO,WAAW,KAAK,GAAG,iBAAiB;oBACvG,YAAY,IAAI,OAAO,WAAW;gBACpC,IACA;IAER;IAEA,MAAM,UAAU;QACd,OAAO,oBAAoB,MAAM;QACjC,SAAS,oBAAoB,MAAM,CAAC,CAAA,KAAM,GAAG,MAAM,KAAK,WAAW,MAAM;QACzE,aAAa,oBAAoB,MAAM,CAAC,CAAA,KAAM,GAAG,MAAM,KAAK,eAAe,MAAM;QACjF,WAAW,oBAAoB,MAAM,CAAC,CAAA,KAAM,GAAG,MAAM,KAAK,aAAa,MAAM;QAC7E,YAAY,oBAAoB,MAAM,CAAC,CAAA,KAAM,GAAG,MAAM,KAAK,aAAa,MAAM,GAAG,oBAAoB,MAAM,GAAG;IAChH;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAqB;;;;;;8BACnC,6LAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,mIAAA,CAAA,OAAI;4BAAS,WAAU;sCACtB,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;2BAHR;;;;;;;;;;;;;;;;IAUrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;kDAC7B,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;4BAGnC,YAAY,iBAAiB,oBAC5B,6LAAC,qIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,SAAS,IAAM,mBAAmB;;kDAClD,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCACf,gBAAgB,WAAW,4BAA4B;;;;;;;;;;;;;;;;;;;0BAOhE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAsB,QAAQ,KAAK;;;;;;;;;;;;kDAElD,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAKzB,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAsC,QAAQ,WAAW;;;;;;;;;;;;kDAExE,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAKtB,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAqC,QAAQ,SAAS;;;;;;;;;;;;kDAErE,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK7B,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;;oDAAsC,QAAQ,UAAU,CAAC,OAAO,CAAC;oDAAG;;;;;;;;;;;;;kDAEnF,6LAAC,+MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO3B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,eAAe;4BAC9B,WAAW,CAAC,yCAAyC,EACnD,gBAAgB,WACZ,kCACA,8EACJ;sCACH;;;;;;sCAGD,6LAAC;4BACC,SAAS,IAAM,eAAe;4BAC9B,WAAW,CAAC,yCAAyC,EACnD,gBAAgB,YACZ,kCACA,8EACJ;sCACH;;;;;;;;;;;;;;;;;0BAOL,6LAAC,mIAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;oCACJ,aAAa,gBAAgB,WAAW,mCAAmC;oCAC3E,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,wBAAU,6LAAC,yMAAA,CAAA,SAAM;wCAAC,MAAM;;;;;;;;;;;;;;;;4BAG3B,gBAAgB,0BACf,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAC/C,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAG;;;;;;kDACjB,6LAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,6LAAC;wCAAO,OAAM;kDAAc;;;;;;kDAC5B,6LAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,6LAAC;wCAAO,OAAM;kDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQnC,gBAAgB,yBACf,6LAAC;gBAAI,WAAU;0BACZ,4BAA4B,MAAM,KAAK,kBACtC,6LAAC,mIAAA,CAAA,OAAI;8BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC,2MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;2BAIjC,4BAA4B,GAAG,CAAC,CAAC;oBAC/B,MAAM,iBAAiB,AAAC,GAAG,iBAAiB,GAAG,GAAG,mBAAmB,GAAI;oBACzE,MAAM,YAAY,IAAI,KAAK,GAAG,oBAAoB,IAAI,IAAI,UAAU,GAAG,MAAM,KAAK;oBAElF,qBACE,6LAAC,mIAAA,CAAA,OAAI;wBAAa,WAAW,YAAY,6BAA6B;kCACpE,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;;gEACX,GAAG,SAAS;gEACZ,2BAAa,6LAAC,2NAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;;;;;;;sEAEzC,6LAAC;4DAAE,WAAU;;gEACV,GAAG,YAAY;gEAAC;gEAAI,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,GAAG,UAAU;;;;;;;;;;;;;8DAGrD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAW,CAAC,2CAA2C,EAAE,YAAY,CAAC,GAAG,MAAM,CAAC,EAAE;sEACrF,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;sEAE1C,6LAAC;4DAAK,WAAW,CAAC,2CAA2C,EAAE,cAAc,CAAC,GAAG,QAAQ,CAAC,EAAE;sEACzF,GAAG,QAAQ,CAAC,WAAW;;;;;;;;;;;;;;;;;;sDAI9B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;wDACZ,GAAG,iBAAiB;wDAAC;wDAAE,GAAG,mBAAmB;;;;;;;8DAEhD,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAI3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAoC;;;;;;8DACrD,6LAAC;oDAAE,WAAU;8DAAW,GAAG,WAAW,IAAI;;;;;;;;;;;;sDAE5C,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAoC;;;;;;8DACrD,6LAAC;oDAAE,WAAU;8DACV,IAAI,KAAK,GAAG,oBAAoB,EAAE,cAAc;;;;;;;;;;;;sDAGrD,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAoC;;;;;;8DACrD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,OAAO,GAAG,eAAe,CAAC,CAAC;gEAAC;;;;;;;;;;;sEAGzC,6LAAC;4DAAK,WAAU;;gEAAuB,eAAe,OAAO,CAAC;gEAAG;;;;;;;;;;;;;;;;;;;;;;;;;gCAKtE,GAAG,KAAK,kBACP,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAAoC;;;;;;sDACrD,6LAAC;4CAAE,WAAU;sDAAyB,GAAG,KAAK;;;;;;;;;;;;8CAKlD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,MAAK;;sEAC7B,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;gDAGjC,GAAG,MAAM,KAAK,+BACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DACJ,MAAK;4DACL,aAAY;4DACZ,WAAU;4DACV,KAAK,GAAG,mBAAmB;4DAC3B,KAAK;4DACL,cAAc,GAAG,iBAAiB;4DAClC,QAAQ,CAAC,IAAM,uBAAuB,GAAG,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;sEAE3E,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAK9C,6LAAC;4CAAI,WAAU;;gDACZ,GAAG,MAAM,KAAK,2BACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS,IAAM,eAAe,GAAG,EAAE,EAAE;;sEAErC,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;gDAIpC,GAAG,MAAM,KAAK,+BACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS,IAAM,eAAe,GAAG,EAAE,EAAE;;sEAErC,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;gDAI3C,GAAG,MAAM,KAAK,eAAe,GAAG,MAAM,KAAK,6BAC1C,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,eAAe,GAAG,EAAE,EAAE;8DACtC;;;;;;;;;;;;;;;;;;;;;;;;uBA7GA,GAAG,EAAE;;;;;gBAsHpB;;;;;qCAIJ,6LAAC;gBAAI,WAAU;0BACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAyB,OAAO,IAAI;;;;;;8DAClD,6LAAC;oDAAE,WAAU;8DAAyB,OAAO,YAAY;;;;;;8DACzD,6LAAC;oDAAE,WAAU;8DAA8B,OAAO,WAAW;;;;;;;;;;;;sDAE/D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;wDACZ,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,aAAa;wDAAE;;;;;;;8DAExC,6LAAC;oDAAI,WAAU;;wDACZ,OAAO,gBAAgB;wDAAC;;;;;;;;;;;;;;;;;;;8CAK/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,6LAAC;4CAAI,WAAU;sDACZ,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,2BACvB,6LAAC;oDAAwB,WAAU;;sEACjC,6LAAC;;gEACE,WAAW,QAAQ;gEAAC;gEAAE,WAAW,IAAI;gEAAC;gEAAE,WAAW,eAAe;;;;;;;sEAErE,6LAAC;sEAAM,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,IAAI;;;;;;;mDAJ7B,WAAW,EAAE;;;;;;;;;;;;;;;;8CAU7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,6LAAC;4CAAI,WAAU;sDACZ,OAAO,YAAY;;;;;;;;;;;;8CAIxB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;gDAAwB;gDAC7B,OAAO,cAAc;gDAAC;;;;;;;sDAEhC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,MAAK;;sEAC7B,6LAAC,8MAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGnC,6LAAC,qIAAA,CAAA,SAAM;oDAAC,MAAK;;sEACX,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;uBAjDhC,OAAO,EAAE;;;;;;;;;;;;;;;;AA6DhC;GA1hBwB;;QACL,iIAAA,CAAA,UAAO;QACJ,iIAAA,CAAA,iBAAc;;;KAFZ", "debugId": null}}]}