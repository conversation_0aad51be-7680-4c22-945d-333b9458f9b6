{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/utils';\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'rounded-lg border bg-card text-card-foreground shadow-sm',\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\n    {...props}\n  />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight',\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n));\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/utils';\n\nexport interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  leftIcon?: React.ReactNode;\n  rightIcon?: React.ReactNode;\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, label, error, helperText, leftIcon, rightIcon, ...props }, ref) => {\n    const inputId = React.useId();\n\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label\n            htmlFor={inputId}\n            className=\"block text-sm font-medium text-gray-700 mb-1\"\n          >\n            {label}\n          </label>\n        )}\n        <div className=\"relative\">\n          {leftIcon && (\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <div className=\"h-5 w-5 text-gray-400\">{leftIcon}</div>\n            </div>\n          )}\n          <input\n            id={inputId}\n            type={type}\n            className={cn(\n              'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n              leftIcon && 'pl-10',\n              rightIcon && 'pr-10',\n              error && 'border-red-500 focus-visible:ring-red-500',\n              className\n            )}\n            ref={ref}\n            {...props}\n          />\n          {rightIcon && (\n            <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center\">\n              <div className=\"h-5 w-5 text-gray-400\">{rightIcon}</div>\n            </div>\n          )}\n        </div>\n        {error && (\n          <p className=\"mt-1 text-sm text-red-600\">{error}</p>\n        )}\n        {helperText && !error && (\n          <p className=\"mt-1 text-sm text-gray-500\">{helperText}</p>\n        )}\n      </div>\n    );\n  }\n);\n\nInput.displayName = 'Input';\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAUA,MAAM,sBAAQ,GAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,UAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;;IAC7E,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,QAAW,AAAD;IAE1B,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;0BAET;;;;;;0BAGL,6LAAC;gBAAI,WAAU;;oBACZ,0BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCAAyB;;;;;;;;;;;kCAG5C,6LAAC;wBACC,IAAI;wBACJ,MAAM;wBACN,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EACV,gWACA,YAAY,SACZ,aAAa,SACb,SAAS,6CACT;wBAEF,KAAK;wBACJ,GAAG,KAAK;;;;;;oBAEV,2BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCAAyB;;;;;;;;;;;;;;;;;YAI7C,uBACC,6LAAC;gBAAE,WAAU;0BAA6B;;;;;;YAE3C,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;AAInD;;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/app/%28dashboard%29/dashboard/customers/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useAuth } from '@/lib/auth-context';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { \n  Users, \n  Plus, \n  Edit, \n  Phone,\n  Mail,\n  MapPin,\n  Star,\n  Gift,\n  Search,\n  Filter,\n  Download,\n  TrendingUp\n} from 'lucide-react';\nimport { formatCurrency } from '@/utils';\nimport { Customer } from '@/types';\n\ninterface CustomerWithStats extends Customer {\n  total_orders: number;\n  total_spent: number;\n  last_order_date?: string;\n  average_order_value: number;\n  favorite_items: string[];\n}\n\nexport default function CustomersPage() {\n  const { user } = useAuth();\n  const [customers, setCustomers] = useState<CustomerWithStats[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [loyaltyFilter, setLoyaltyFilter] = useState('');\n  const [showCreateModal, setShowCreateModal] = useState(false);\n\n  // Mock data for demonstration\n  const mockCustomers: CustomerWithStats[] = [\n    {\n      id: 'cust-1',\n      name: 'John Doe',\n      email: '<EMAIL>',\n      phone: '******-0150',\n      address: '123 Customer St, Downtown',\n      location: { latitude: 40.7127, longitude: -74.005 },\n      loyalty_points: 1250,\n      rating: 4.8,\n      notes: 'Regular customer, prefers oat milk in coffee',\n      is_active: true,\n      total_orders: 45,\n      total_spent: 567.50,\n      last_order_date: '2024-01-15T10:30:00Z',\n      average_order_value: 12.61,\n      favorite_items: ['Cappuccino', 'Caesar Salad', 'Grilled Chicken'],\n      created_at: '2023-06-15T00:00:00Z',\n      updated_at: '2024-01-15T10:30:00Z',\n    },\n    {\n      id: 'cust-2',\n      name: 'Jane Smith',\n      email: '<EMAIL>',\n      phone: '******-0151',\n      address: '456 Client Ave, Uptown',\n      location: { latitude: 40.7483, longitude: -73.9856 },\n      loyalty_points: 890,\n      rating: 4.5,\n      notes: 'Vegetarian, allergic to nuts',\n      is_active: true,\n      total_orders: 32,\n      total_spent: 398.75,\n      last_order_date: '2024-01-14T16:45:00Z',\n      average_order_value: 12.46,\n      favorite_items: ['Latte', 'Caesar Salad', 'Chocolate Cake'],\n      created_at: '2023-08-22T00:00:00Z',\n      updated_at: '2024-01-14T16:45:00Z',\n    },\n    {\n      id: 'cust-3',\n      name: 'Bob Johnson',\n      email: '<EMAIL>',\n      phone: '******-0152',\n      address: '789 Buyer Blvd, Westside',\n      location: { latitude: 40.7588, longitude: -74.0058 },\n      loyalty_points: 2100,\n      rating: 4.9,\n      notes: 'VIP customer, owns local business',\n      is_active: true,\n      total_orders: 78,\n      total_spent: 1245.80,\n      last_order_date: '2024-01-15T12:15:00Z',\n      average_order_value: 15.97,\n      favorite_items: ['Espresso', 'Grilled Chicken', 'Cappuccino'],\n      created_at: '2023-03-10T00:00:00Z',\n      updated_at: '2024-01-15T12:15:00Z',\n    },\n    {\n      id: 'cust-4',\n      name: 'Alice Brown',\n      email: '<EMAIL>',\n      phone: '******-0153',\n      address: '321 Patron Place, Downtown',\n      location: { latitude: 40.7129, longitude: -74.007 },\n      loyalty_points: 450,\n      rating: 4.2,\n      notes: null,\n      is_active: true,\n      total_orders: 18,\n      total_spent: 189.25,\n      last_order_date: '2024-01-13T09:30:00Z',\n      average_order_value: 10.51,\n      favorite_items: ['Latte', 'Chocolate Cake'],\n      created_at: '2023-11-05T00:00:00Z',\n      updated_at: '2024-01-13T09:30:00Z',\n    },\n    {\n      id: 'cust-5',\n      name: 'Charlie Wilson',\n      email: '<EMAIL>',\n      phone: '******-0154',\n      address: '654 Regular Rd, Suburbs',\n      location: { latitude: 40.7200, longitude: -74.0100 },\n      loyalty_points: 75,\n      rating: 3.8,\n      notes: 'New customer, referred by Bob Johnson',\n      is_active: true,\n      total_orders: 3,\n      total_spent: 42.50,\n      last_order_date: '2024-01-12T14:20:00Z',\n      average_order_value: 14.17,\n      favorite_items: ['Cappuccino'],\n      created_at: '2024-01-10T00:00:00Z',\n      updated_at: '2024-01-12T14:20:00Z',\n    },\n  ];\n\n  useEffect(() => {\n    // In a real app, this would fetch from the API\n    setCustomers(mockCustomers);\n    setLoading(false);\n  }, []);\n\n  const filteredCustomers = customers.filter(customer => {\n    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         customer.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         customer.phone.includes(searchTerm);\n    \n    let matchesLoyalty = true;\n    if (loyaltyFilter === 'vip') {\n      matchesLoyalty = customer.loyalty_points >= 1000;\n    } else if (loyaltyFilter === 'regular') {\n      matchesLoyalty = customer.loyalty_points >= 500 && customer.loyalty_points < 1000;\n    } else if (loyaltyFilter === 'new') {\n      matchesLoyalty = customer.loyalty_points < 500;\n    }\n    \n    return matchesSearch && matchesLoyalty;\n  });\n\n  const getLoyaltyTier = (points: number) => {\n    if (points >= 1000) return { tier: 'VIP', color: 'bg-purple-100 text-purple-800' };\n    if (points >= 500) return { tier: 'Regular', color: 'bg-blue-100 text-blue-800' };\n    return { tier: 'New', color: 'bg-gray-100 text-gray-800' };\n  };\n\n  const customerStats = {\n    total: customers.length,\n    vip: customers.filter(c => c.loyalty_points >= 1000).length,\n    regular: customers.filter(c => c.loyalty_points >= 500 && c.loyalty_points < 1000).length,\n    new: customers.filter(c => c.loyalty_points < 500).length,\n    totalSpent: customers.reduce((sum, c) => sum + c.total_spent, 0),\n    averageOrderValue: customers.reduce((sum, c) => sum + c.average_order_value, 0) / customers.length,\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-4\">\n        <h1 className=\"text-2xl font-bold\">Customers</h1>\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          {[...Array(4)].map((_, i) => (\n            <Card key={i} className=\"animate-pulse\">\n              <CardContent className=\"p-6\">\n                <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n                <div className=\"h-8 bg-gray-200 rounded w-1/2\"></div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <h1 className=\"text-2xl font-bold text-gray-900\">Customers</h1>\n        <div className=\"flex space-x-2\">\n          <Button variant=\"outline\" size=\"sm\">\n            <Download className=\"h-4 w-4 mr-2\" />\n            Export\n          </Button>\n          <Button size=\"sm\" onClick={() => setShowCreateModal(true)}>\n            <Plus className=\"h-4 w-4 mr-2\" />\n            Add Customer\n          </Button>\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Total Customers</p>\n                <p className=\"text-2xl font-bold\">{customerStats.total}</p>\n              </div>\n              <Users className=\"h-8 w-8 text-blue-600\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">VIP Customers</p>\n                <p className=\"text-2xl font-bold text-purple-600\">{customerStats.vip}</p>\n              </div>\n              <Star className=\"h-8 w-8 text-purple-600\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Total Revenue</p>\n                <p className=\"text-2xl font-bold text-green-600\">\n                  {formatCurrency(customerStats.totalSpent)}\n                </p>\n              </div>\n              <TrendingUp className=\"h-8 w-8 text-green-600\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600\">Avg Order Value</p>\n                <p className=\"text-2xl font-bold text-orange-600\">\n                  {formatCurrency(customerStats.averageOrderValue)}\n                </p>\n              </div>\n              <Gift className=\"h-8 w-8 text-orange-600\" />\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Filters */}\n      <Card>\n        <CardContent className=\"p-4\">\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <div className=\"flex-1\">\n              <Input\n                placeholder=\"Search customers...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                leftIcon={<Search size={20} />}\n              />\n            </div>\n            <select\n              value={loyaltyFilter}\n              onChange={(e) => setLoyaltyFilter(e.target.value)}\n              className=\"px-3 py-2 border border-gray-300 rounded-md\"\n            >\n              <option value=\"\">All Customers</option>\n              <option value=\"vip\">VIP (1000+ points)</option>\n              <option value=\"regular\">Regular (500-999 points)</option>\n              <option value=\"new\">New (0-499 points)</option>\n            </select>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Customers Grid */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\">\n        {filteredCustomers.length === 0 ? (\n          <div className=\"col-span-full text-center py-12\">\n            <Users className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No customers found</h3>\n            <p className=\"text-gray-600\">Try adjusting your search criteria</p>\n          </div>\n        ) : (\n          filteredCustomers.map((customer) => {\n            const loyaltyTier = getLoyaltyTier(customer.loyalty_points);\n            \n            return (\n              <Card key={customer.id} className=\"hover:shadow-lg transition-shadow\">\n                <CardHeader className=\"pb-3\">\n                  <div className=\"flex justify-between items-start\">\n                    <div>\n                      <CardTitle className=\"text-lg\">{customer.name}</CardTitle>\n                      <div className=\"flex items-center space-x-2 mt-1\">\n                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${loyaltyTier.color}`}>\n                          {loyaltyTier.tier}\n                        </span>\n                        <div className=\"flex items-center\">\n                          <Star className=\"h-3 w-3 text-yellow-500 mr-1\" />\n                          <span className=\"text-sm\">{customer.rating.toFixed(1)}</span>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-sm font-medium text-purple-600\">\n                        {customer.loyalty_points} pts\n                      </div>\n                    </div>\n                  </div>\n                </CardHeader>\n\n                <CardContent className=\"space-y-3\">\n                  {/* Contact Information */}\n                  <div className=\"space-y-2\">\n                    <div className=\"flex items-center text-sm text-gray-600\">\n                      <Phone className=\"h-4 w-4 mr-2\" />\n                      {customer.phone}\n                    </div>\n                    {customer.email && (\n                      <div className=\"flex items-center text-sm text-gray-600\">\n                        <Mail className=\"h-4 w-4 mr-2\" />\n                        {customer.email}\n                      </div>\n                    )}\n                    <div className=\"flex items-start text-sm text-gray-600\">\n                      <MapPin className=\"h-4 w-4 mr-2 mt-0.5 flex-shrink-0\" />\n                      <span className=\"line-clamp-2\">{customer.address}</span>\n                    </div>\n                  </div>\n\n                  {/* Customer Stats */}\n                  <div className=\"grid grid-cols-2 gap-4 pt-2 border-t\">\n                    <div>\n                      <div className=\"text-xs text-gray-500\">Total Orders</div>\n                      <div className=\"font-semibold\">{customer.total_orders}</div>\n                    </div>\n                    <div>\n                      <div className=\"text-xs text-gray-500\">Total Spent</div>\n                      <div className=\"font-semibold\">{formatCurrency(customer.total_spent)}</div>\n                    </div>\n                    <div>\n                      <div className=\"text-xs text-gray-500\">Avg Order</div>\n                      <div className=\"font-semibold\">{formatCurrency(customer.average_order_value)}</div>\n                    </div>\n                    <div>\n                      <div className=\"text-xs text-gray-500\">Last Order</div>\n                      <div className=\"font-semibold text-xs\">\n                        {customer.last_order_date \n                          ? new Date(customer.last_order_date).toLocaleDateString()\n                          : 'Never'\n                        }\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Favorite Items */}\n                  {customer.favorite_items.length > 0 && (\n                    <div className=\"pt-2 border-t\">\n                      <div className=\"text-xs text-gray-500 mb-1\">Favorite Items</div>\n                      <div className=\"flex flex-wrap gap-1\">\n                        {customer.favorite_items.slice(0, 3).map((item, index) => (\n                          <span key={index} className=\"px-2 py-1 bg-gray-100 text-xs rounded\">\n                            {item}\n                          </span>\n                        ))}\n                        {customer.favorite_items.length > 3 && (\n                          <span className=\"px-2 py-1 bg-gray-100 text-xs rounded\">\n                            +{customer.favorite_items.length - 3} more\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Notes */}\n                  {customer.notes && (\n                    <div className=\"pt-2 border-t\">\n                      <div className=\"text-xs text-gray-500 mb-1\">Notes</div>\n                      <div className=\"text-sm text-gray-700 line-clamp-2\">\n                        {customer.notes}\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Actions */}\n                  <div className=\"flex space-x-2 pt-2\">\n                    <Button variant=\"outline\" size=\"sm\" className=\"flex-1\">\n                      <Edit className=\"h-3 w-3 mr-1\" />\n                      Edit\n                    </Button>\n                    <Button variant=\"outline\" size=\"sm\" className=\"flex-1\">\n                      <Gift className=\"h-3 w-3 mr-1\" />\n                      Reward\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n            );\n          })\n        )}\n      </div>\n\n      {/* Create Customer Modal Placeholder */}\n      {showCreateModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <Card className=\"w-full max-w-2xl mx-4\">\n            <CardHeader>\n              <CardTitle>Add New Customer</CardTitle>\n              <CardDescription>Create a new customer profile</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <Input label=\"Full Name\" placeholder=\"Enter customer name\" />\n                  <Input label=\"Phone\" placeholder=\"Enter phone number\" />\n                </div>\n                <Input label=\"Email\" type=\"email\" placeholder=\"Enter email address\" />\n                <Input label=\"Address\" placeholder=\"Enter full address\" />\n                <Input label=\"Notes\" placeholder=\"Any special notes or preferences\" />\n              </div>\n              <div className=\"flex justify-end space-x-2 mt-6\">\n                <Button variant=\"outline\" onClick={() => setShowCreateModal(false)}>\n                  Cancel\n                </Button>\n                <Button onClick={() => setShowCreateModal(false)}>\n                  Create Customer\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;;;AArBA;;;;;;;;AAgCe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,8BAA8B;IAC9B,MAAM,gBAAqC;QACzC;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,SAAS;YACT,UAAU;gBAAE,UAAU;gBAAS,WAAW,CAAC;YAAO;YAClD,gBAAgB;YAChB,QAAQ;YACR,OAAO;YACP,WAAW;YACX,cAAc;YACd,aAAa;YACb,iBAAiB;YACjB,qBAAqB;YACrB,gBAAgB;gBAAC;gBAAc;gBAAgB;aAAkB;YACjE,YAAY;YACZ,YAAY;QACd;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,SAAS;YACT,UAAU;gBAAE,UAAU;gBAAS,WAAW,CAAC;YAAQ;YACnD,gBAAgB;YAChB,QAAQ;YACR,OAAO;YACP,WAAW;YACX,cAAc;YACd,aAAa;YACb,iBAAiB;YACjB,qBAAqB;YACrB,gBAAgB;gBAAC;gBAAS;gBAAgB;aAAiB;YAC3D,YAAY;YACZ,YAAY;QACd;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,SAAS;YACT,UAAU;gBAAE,UAAU;gBAAS,WAAW,CAAC;YAAQ;YACnD,gBAAgB;YAChB,QAAQ;YACR,OAAO;YACP,WAAW;YACX,cAAc;YACd,aAAa;YACb,iBAAiB;YACjB,qBAAqB;YACrB,gBAAgB;gBAAC;gBAAY;gBAAmB;aAAa;YAC7D,YAAY;YACZ,YAAY;QACd;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,SAAS;YACT,UAAU;gBAAE,UAAU;gBAAS,WAAW,CAAC;YAAO;YAClD,gBAAgB;YAChB,QAAQ;YACR,OAAO;YACP,WAAW;YACX,cAAc;YACd,aAAa;YACb,iBAAiB;YACjB,qBAAqB;YACrB,gBAAgB;gBAAC;gBAAS;aAAiB;YAC3C,YAAY;YACZ,YAAY;QACd;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,SAAS;YACT,UAAU;gBAAE,UAAU;gBAAS,WAAW,CAAC;YAAQ;YACnD,gBAAgB;YAChB,QAAQ;YACR,OAAO;YACP,WAAW;YACX,cAAc;YACd,aAAa;YACb,iBAAiB;YACjB,qBAAqB;YACrB,gBAAgB;gBAAC;aAAa;YAC9B,YAAY;YACZ,YAAY;QACd;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,+CAA+C;YAC/C,aAAa;YACb,WAAW;QACb;kCAAG,EAAE;IAEL,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA;QACzC,MAAM,gBAAgB,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC5D,SAAS,KAAK,EAAE,cAAc,SAAS,WAAW,WAAW,OAC7D,SAAS,KAAK,CAAC,QAAQ,CAAC;QAE7C,IAAI,iBAAiB;QACrB,IAAI,kBAAkB,OAAO;YAC3B,iBAAiB,SAAS,cAAc,IAAI;QAC9C,OAAO,IAAI,kBAAkB,WAAW;YACtC,iBAAiB,SAAS,cAAc,IAAI,OAAO,SAAS,cAAc,GAAG;QAC/E,OAAO,IAAI,kBAAkB,OAAO;YAClC,iBAAiB,SAAS,cAAc,GAAG;QAC7C;QAEA,OAAO,iBAAiB;IAC1B;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,MAAM,OAAO;YAAE,MAAM;YAAO,OAAO;QAAgC;QACjF,IAAI,UAAU,KAAK,OAAO;YAAE,MAAM;YAAW,OAAO;QAA4B;QAChF,OAAO;YAAE,MAAM;YAAO,OAAO;QAA4B;IAC3D;IAEA,MAAM,gBAAgB;QACpB,OAAO,UAAU,MAAM;QACvB,KAAK,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,cAAc,IAAI,MAAM,MAAM;QAC3D,SAAS,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,cAAc,IAAI,OAAO,EAAE,cAAc,GAAG,MAAM,MAAM;QACzF,KAAK,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,cAAc,GAAG,KAAK,MAAM;QACzD,YAAY,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,WAAW,EAAE;QAC9D,mBAAmB,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,mBAAmB,EAAE,KAAK,UAAU,MAAM;IACpG;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAqB;;;;;;8BACnC,6LAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,mIAAA,CAAA,OAAI;4BAAS,WAAU;sCACtB,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;2BAHR;;;;;;;;;;;;;;;;IAUrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;kDAC7B,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,SAAS,IAAM,mBAAmB;;kDAClD,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAOvC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAsB,cAAc,KAAK;;;;;;;;;;;;kDAExD,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAKvB,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAsC,cAAc,GAAG;;;;;;;;;;;;kDAEtE,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAKtB,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DACV,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,cAAc,UAAU;;;;;;;;;;;;kDAG5C,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK5B,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DACV,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,cAAc,iBAAiB;;;;;;;;;;;;kDAGnD,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOxB,6LAAC,mIAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,wBAAU,6LAAC,yMAAA,CAAA,SAAM;wCAAC,MAAM;;;;;;;;;;;;;;;;0CAG5B,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gCAChD,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAG;;;;;;kDACjB,6LAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,6LAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,6LAAC;wCAAO,OAAM;kDAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO5B,6LAAC;gBAAI,WAAU;0BACZ,kBAAkB,MAAM,KAAK,kBAC5B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;2BAG/B,kBAAkB,GAAG,CAAC,CAAC;oBACrB,MAAM,cAAc,eAAe,SAAS,cAAc;oBAE1D,qBACE,6LAAC,mIAAA,CAAA,OAAI;wBAAmB,WAAU;;0CAChC,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAW,SAAS,IAAI;;;;;;8DAC7C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAW,CAAC,2CAA2C,EAAE,YAAY,KAAK,EAAE;sEAC/E,YAAY,IAAI;;;;;;sEAEnB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC;oEAAK,WAAU;8EAAW,SAAS,MAAM,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;sDAIzD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;oDACZ,SAAS,cAAc;oDAAC;;;;;;;;;;;;;;;;;;;;;;;0CAMjC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDAErB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAChB,SAAS,KAAK;;;;;;;4CAEhB,SAAS,KAAK,kBACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDACf,SAAS,KAAK;;;;;;;0DAGnB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;wDAAK,WAAU;kEAAgB,SAAS,OAAO;;;;;;;;;;;;;;;;;;kDAKpD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;kEACvC,6LAAC;wDAAI,WAAU;kEAAiB,SAAS,YAAY;;;;;;;;;;;;0DAEvD,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;kEACvC,6LAAC;wDAAI,WAAU;kEAAiB,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,WAAW;;;;;;;;;;;;0DAErE,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;kEACvC,6LAAC;wDAAI,WAAU;kEAAiB,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,mBAAmB;;;;;;;;;;;;0DAE7E,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;kEACvC,6LAAC;wDAAI,WAAU;kEACZ,SAAS,eAAe,GACrB,IAAI,KAAK,SAAS,eAAe,EAAE,kBAAkB,KACrD;;;;;;;;;;;;;;;;;;oCAOT,SAAS,cAAc,CAAC,MAAM,GAAG,mBAChC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA6B;;;;;;0DAC5C,6LAAC;gDAAI,WAAU;;oDACZ,SAAS,cAAc,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBAC9C,6LAAC;4DAAiB,WAAU;sEACzB;2DADQ;;;;;oDAIZ,SAAS,cAAc,CAAC,MAAM,GAAG,mBAChC,6LAAC;wDAAK,WAAU;;4DAAwC;4DACpD,SAAS,cAAc,CAAC,MAAM,GAAG;4DAAE;;;;;;;;;;;;;;;;;;;oCAQ9C,SAAS,KAAK,kBACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA6B;;;;;;0DAC5C,6LAAC;gDAAI,WAAU;0DACZ,SAAS,KAAK;;;;;;;;;;;;kDAMrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;;kEAC5C,6LAAC,8MAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;;kEAC5C,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;uBAvG9B,SAAS,EAAE;;;;;gBA8G1B;;;;;;YAKH,iCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC,mIAAA,CAAA,aAAU;;8CACT,6LAAC,mIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,6LAAC,mIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAEnB,6LAAC,mIAAA,CAAA,cAAW;;8CACV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,OAAM;oDAAY,aAAY;;;;;;8DACrC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,OAAM;oDAAQ,aAAY;;;;;;;;;;;;sDAEnC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,OAAM;4CAAQ,MAAK;4CAAQ,aAAY;;;;;;sDAC9C,6LAAC,oIAAA,CAAA,QAAK;4CAAC,OAAM;4CAAU,aAAY;;;;;;sDACnC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,OAAM;4CAAQ,aAAY;;;;;;;;;;;;8CAEnC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS,IAAM,mBAAmB;sDAAQ;;;;;;sDAGpE,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS,IAAM,mBAAmB;sDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUlE;GApawB;;QACL,iIAAA,CAAA,UAAO;;;KADF", "debugId": null}}]}