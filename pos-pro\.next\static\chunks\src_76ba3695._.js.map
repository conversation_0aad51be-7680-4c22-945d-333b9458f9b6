{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/components/auth/protected-route.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/lib/auth-context';\nimport { User } from '@/types';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  requiredRoles?: User['role'][];\n  fallbackPath?: string;\n}\n\nexport function ProtectedRoute({ \n  children, \n  requiredRoles = [], \n  fallbackPath = '/login' \n}: ProtectedRouteProps) {\n  const { user, loading } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!loading) {\n      if (!user) {\n        router.push(fallbackPath);\n        return;\n      }\n\n      if (requiredRoles.length > 0 && !requiredRoles.includes(user.role)) {\n        router.push('/unauthorized');\n        return;\n      }\n    }\n  }, [user, loading, requiredRoles, router, fallbackPath]);\n\n  // Show loading spinner while checking authentication\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  // Don't render children if user is not authenticated or doesn't have required role\n  if (!user || (requiredRoles.length > 0 && !requiredRoles.includes(user.role))) {\n    return null;\n  }\n\n  return <>{children}</>;\n}\n\n// Convenience components for specific roles\nexport function AdminRoute({ children }: { children: React.ReactNode }) {\n  return (\n    <ProtectedRoute requiredRoles={['admin']}>\n      {children}\n    </ProtectedRoute>\n  );\n}\n\nexport function ManagerRoute({ children }: { children: React.ReactNode }) {\n  return (\n    <ProtectedRoute requiredRoles={['admin', 'manager']}>\n      {children}\n    </ProtectedRoute>\n  );\n}\n\nexport function CashierRoute({ children }: { children: React.ReactNode }) {\n  return (\n    <ProtectedRoute requiredRoles={['admin', 'manager', 'cashier']}>\n      {children}\n    </ProtectedRoute>\n  );\n}\n\nexport function KitchenRoute({ children }: { children: React.ReactNode }) {\n  return (\n    <ProtectedRoute requiredRoles={['admin', 'manager', 'kitchen_staff']}>\n      {children}\n    </ProtectedRoute>\n  );\n}\n\nexport function SalesRoute({ children }: { children: React.ReactNode }) {\n  return (\n    <ProtectedRoute requiredRoles={['admin', 'manager', 'sales_rep']}>\n      {children}\n    </ProtectedRoute>\n  );\n}\n\nexport function WarehouseRoute({ children }: { children: React.ReactNode }) {\n  return (\n    <ProtectedRoute requiredRoles={['admin', 'manager', 'warehouse_staff']}>\n      {children}\n    </ProtectedRoute>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;;;AAJA;;;;AAaO,SAAS,eAAe,EAC7B,QAAQ,EACR,gBAAgB,EAAE,EAClB,eAAe,QAAQ,EACH;;IACpB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,SAAS;gBACZ,IAAI,CAAC,MAAM;oBACT,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,IAAI,cAAc,MAAM,GAAG,KAAK,CAAC,cAAc,QAAQ,CAAC,KAAK,IAAI,GAAG;oBAClE,OAAO,IAAI,CAAC;oBACZ;gBACF;YACF;QACF;mCAAG;QAAC;QAAM;QAAS;QAAe;QAAQ;KAAa;IAEvD,qDAAqD;IACrD,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,mFAAmF;IACnF,IAAI,CAAC,QAAS,cAAc,MAAM,GAAG,KAAK,CAAC,cAAc,QAAQ,CAAC,KAAK,IAAI,GAAI;QAC7E,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ;GArCgB;;QAKY,iIAAA,CAAA,UAAO;QAClB,qIAAA,CAAA,YAAS;;;KANV;AAwCT,SAAS,WAAW,EAAE,QAAQ,EAAiC;IACpE,qBACE,6LAAC;QAAe,eAAe;YAAC;SAAQ;kBACrC;;;;;;AAGP;MANgB;AAQT,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,qBACE,6LAAC;QAAe,eAAe;YAAC;YAAS;SAAU;kBAChD;;;;;;AAGP;MANgB;AAQT,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,qBACE,6LAAC;QAAe,eAAe;YAAC;YAAS;YAAW;SAAU;kBAC3D;;;;;;AAGP;MANgB;AAQT,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,qBACE,6LAAC;QAAe,eAAe;YAAC;YAAS;YAAW;SAAgB;kBACjE;;;;;;AAGP;MANgB;AAQT,SAAS,WAAW,EAAE,QAAQ,EAAiC;IACpE,qBACE,6LAAC;QAAe,eAAe;YAAC;YAAS;YAAW;SAAY;kBAC7D;;;;;;AAGP;MANgB;AAQT,SAAS,eAAe,EAAE,QAAQ,EAAiC;IACxE,qBACE,6LAAC;QAAe,eAAe;YAAC;YAAS;YAAW;SAAkB;kBACnE;;;;;;AAGP;MANgB", "debugId": null}}, {"offset": {"line": 186, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/utils/index.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\nimport { format, parseISO } from 'date-fns';\n\n// Utility function for combining Tailwind classes\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// Currency formatting\nexport function formatCurrency(amount: number, currency: string = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: currency,\n  }).format(amount);\n}\n\n// Date formatting utilities\nexport function formatDate(date: string | Date, formatString: string = 'PPP'): string {\n  const dateObj = typeof date === 'string' ? parseISO(date) : date;\n  return format(dateObj, formatString);\n}\n\nexport function formatDateTime(date: string | Date): string {\n  return formatDate(date, 'PPP p');\n}\n\nexport function formatTime(date: string | Date): string {\n  return formatDate(date, 'p');\n}\n\n// Number formatting\nexport function formatNumber(num: number, decimals: number = 2): string {\n  return num.toFixed(decimals);\n}\n\nexport function formatPercentage(num: number, decimals: number = 1): string {\n  return `${(num * 100).toFixed(decimals)}%`;\n}\n\n// String utilities\nexport function capitalize(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\n\nexport function slugify(str: string): string {\n  return str\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '');\n}\n\n// Generate unique IDs\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function generateOrderNumber(): string {\n  const timestamp = Date.now().toString().slice(-6);\n  const random = Math.random().toString(36).substr(2, 4).toUpperCase();\n  return `ORD-${timestamp}-${random}`;\n}\n\nexport function generatePONumber(): string {\n  const timestamp = Date.now().toString().slice(-6);\n  const random = Math.random().toString(36).substr(2, 4).toUpperCase();\n  return `PO-${timestamp}-${random}`;\n}\n\n// Validation utilities\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function isValidPhone(phone: string): boolean {\n  const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]+$/;\n  return phoneRegex.test(phone) && phone.replace(/\\D/g, '').length >= 10;\n}\n\n// Geographic utilities\nexport function calculateDistance(\n  lat1: number,\n  lon1: number,\n  lat2: number,\n  lon2: number\n): number {\n  const R = 6371; // Radius of the Earth in kilometers\n  const dLat = deg2rad(lat2 - lat1);\n  const dLon = deg2rad(lon2 - lon1);\n  const a =\n    Math.sin(dLat / 2) * Math.sin(dLat / 2) +\n    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *\n    Math.sin(dLon / 2) * Math.sin(dLon / 2);\n  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n  const d = R * c; // Distance in kilometers\n  return d;\n}\n\nfunction deg2rad(deg: number): number {\n  return deg * (Math.PI / 180);\n}\n\nexport function isPointInRadius(\n  centerLat: number,\n  centerLon: number,\n  pointLat: number,\n  pointLon: number,\n  radiusKm: number\n): boolean {\n  const distance = calculateDistance(centerLat, centerLon, pointLat, pointLon);\n  return distance <= radiusKm;\n}\n\n// Check if point is inside polygon (for territory boundaries)\nexport function isPointInPolygon(\n  point: { latitude: number; longitude: number },\n  polygon: { latitude: number; longitude: number }[]\n): boolean {\n  const x = point.longitude;\n  const y = point.latitude;\n  let inside = false;\n\n  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\n    const xi = polygon[i].longitude;\n    const yi = polygon[i].latitude;\n    const xj = polygon[j].longitude;\n    const yj = polygon[j].latitude;\n\n    if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {\n      inside = !inside;\n    }\n  }\n\n  return inside;\n}\n\n// Array utilities\nexport function groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {\n  return array.reduce((groups, item) => {\n    const group = String(item[key]);\n    groups[group] = groups[group] || [];\n    groups[group].push(item);\n    return groups;\n  }, {} as Record<string, T[]>);\n}\n\nexport function sortBy<T>(array: T[], key: keyof T, direction: 'asc' | 'desc' = 'asc'): T[] {\n  return [...array].sort((a, b) => {\n    const aVal = a[key];\n    const bVal = b[key];\n    \n    if (aVal < bVal) return direction === 'asc' ? -1 : 1;\n    if (aVal > bVal) return direction === 'asc' ? 1 : -1;\n    return 0;\n  });\n}\n\n// Local storage utilities\nexport function getFromStorage<T>(key: string, defaultValue: T): T {\n  if (typeof window === 'undefined') return defaultValue;\n  \n  try {\n    const item = window.localStorage.getItem(key);\n    return item ? JSON.parse(item) : defaultValue;\n  } catch (error) {\n    console.error(`Error reading from localStorage:`, error);\n    return defaultValue;\n  }\n}\n\nexport function setToStorage<T>(key: string, value: T): void {\n  if (typeof window === 'undefined') return;\n  \n  try {\n    window.localStorage.setItem(key, JSON.stringify(value));\n  } catch (error) {\n    console.error(`Error writing to localStorage:`, error);\n  }\n}\n\nexport function removeFromStorage(key: string): void {\n  if (typeof window === 'undefined') return;\n  \n  try {\n    window.localStorage.removeItem(key);\n  } catch (error) {\n    console.error(`Error removing from localStorage:`, error);\n  }\n}\n\n// Debounce utility\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  \n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// Throttle utility\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  \n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n}\n\n// Error handling\nexport function handleError(error: unknown): string {\n  if (error instanceof Error) {\n    return error.message;\n  }\n  if (typeof error === 'string') {\n    return error;\n  }\n  return 'An unknown error occurred';\n}\n\n// Tax calculation\nexport function calculateTax(amount: number, taxRate: number): number {\n  return amount * (taxRate / 100);\n}\n\nexport function calculateTotal(subtotal: number, taxRate: number, discount: number = 0): {\n  subtotal: number;\n  taxAmount: number;\n  discountAmount: number;\n  total: number;\n} {\n  const discountAmount = subtotal * (discount / 100);\n  const taxableAmount = subtotal - discountAmount;\n  const taxAmount = calculateTax(taxableAmount, taxRate);\n  const total = taxableAmount + taxAmount;\n\n  return {\n    subtotal,\n    taxAmount,\n    discountAmount,\n    total,\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,eAAe,MAAc,EAAE,WAAmB,KAAK;IACrE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,WAAW,IAAmB,EAAE,eAAuB,KAAK;IAC1E,MAAM,UAAU,OAAO,SAAS,WAAW,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;IAC5D,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;AACzB;AAEO,SAAS,eAAe,IAAmB;IAChD,OAAO,WAAW,MAAM;AAC1B;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,WAAW,MAAM;AAC1B;AAGO,SAAS,aAAa,GAAW,EAAE,WAAmB,CAAC;IAC5D,OAAO,IAAI,OAAO,CAAC;AACrB;AAEO,SAAS,iBAAiB,GAAW,EAAE,WAAmB,CAAC;IAChE,OAAO,GAAG,CAAC,MAAM,GAAG,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;AAC5C;AAGO,SAAS,WAAW,GAAW;IACpC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD;AAEO,SAAS,QAAQ,GAAW;IACjC,OAAO,IACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAGO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS;IACd,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;IAC/C,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW;IAClE,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,QAAQ;AACrC;AAEO,SAAS;IACd,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;IAC/C,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW;IAClE,OAAO,CAAC,GAAG,EAAE,UAAU,CAAC,EAAE,QAAQ;AACpC;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,UAAU,MAAM,OAAO,CAAC,OAAO,IAAI,MAAM,IAAI;AACtE;AAGO,SAAS,kBACd,IAAY,EACZ,IAAY,EACZ,IAAY,EACZ,IAAY;IAEZ,MAAM,IAAI,MAAM,oCAAoC;IACpD,MAAM,OAAO,QAAQ,OAAO;IAC5B,MAAM,OAAO,QAAQ,OAAO;IAC5B,MAAM,IACJ,KAAK,GAAG,CAAC,OAAO,KAAK,KAAK,GAAG,CAAC,OAAO,KACrC,KAAK,GAAG,CAAC,QAAQ,SAAS,KAAK,GAAG,CAAC,QAAQ,SAC3C,KAAK,GAAG,CAAC,OAAO,KAAK,KAAK,GAAG,CAAC,OAAO;IACvC,MAAM,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI;IACrD,MAAM,IAAI,IAAI,GAAG,yBAAyB;IAC1C,OAAO;AACT;AAEA,SAAS,QAAQ,GAAW;IAC1B,OAAO,MAAM,CAAC,KAAK,EAAE,GAAG,GAAG;AAC7B;AAEO,SAAS,gBACd,SAAiB,EACjB,SAAiB,EACjB,QAAgB,EAChB,QAAgB,EAChB,QAAgB;IAEhB,MAAM,WAAW,kBAAkB,WAAW,WAAW,UAAU;IACnE,OAAO,YAAY;AACrB;AAGO,SAAS,iBACd,KAA8C,EAC9C,OAAkD;IAElD,MAAM,IAAI,MAAM,SAAS;IACzB,MAAM,IAAI,MAAM,QAAQ;IACxB,IAAI,SAAS;IAEb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,GAAG,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAI,IAAK;QACnE,MAAM,KAAK,OAAO,CAAC,EAAE,CAAC,SAAS;QAC/B,MAAM,KAAK,OAAO,CAAC,EAAE,CAAC,QAAQ;QAC9B,MAAM,KAAK,OAAO,CAAC,EAAE,CAAC,SAAS;QAC/B,MAAM,KAAK,OAAO,CAAC,EAAE,CAAC,QAAQ;QAE9B,IAAI,AAAE,KAAK,MAAQ,KAAK,KAAQ,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,IAAK;YAC1E,SAAS,CAAC;QACZ;IACF;IAEA,OAAO;AACT;AAGO,SAAS,QAAW,KAAU,EAAE,GAAY;IACjD,OAAO,MAAM,MAAM,CAAC,CAAC,QAAQ;QAC3B,MAAM,QAAQ,OAAO,IAAI,CAAC,IAAI;QAC9B,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,EAAE;QACnC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;QACnB,OAAO;IACT,GAAG,CAAC;AACN;AAEO,SAAS,OAAU,KAAU,EAAE,GAAY,EAAE,YAA4B,KAAK;IACnF,OAAO;WAAI;KAAM,CAAC,IAAI,CAAC,CAAC,GAAG;QACzB,MAAM,OAAO,CAAC,CAAC,IAAI;QACnB,MAAM,OAAO,CAAC,CAAC,IAAI;QAEnB,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,CAAC,IAAI;QACnD,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,IAAI,CAAC;QACnD,OAAO;IACT;AACF;AAGO,SAAS,eAAkB,GAAW,EAAE,YAAe;IAC5D,uCAAmC;;IAAmB;IAEtD,IAAI;QACF,MAAM,OAAO,OAAO,YAAY,CAAC,OAAO,CAAC;QACzC,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,CAAC,EAAE;QAClD,OAAO;IACT;AACF;AAEO,SAAS,aAAgB,GAAW,EAAE,KAAQ;IACnD,uCAAmC;;IAAM;IAEzC,IAAI;QACF,OAAO,YAAY,CAAC,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;IAClD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,8BAA8B,CAAC,EAAE;IAClD;AACF;AAEO,SAAS,kBAAkB,GAAW;IAC3C,uCAAmC;;IAAM;IAEzC,IAAI;QACF,OAAO,YAAY,CAAC,UAAU,CAAC;IACjC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,iCAAiC,CAAC,EAAE;IACrD;AACF;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAM,aAAa,OAAO;QACvC;IACF;AACF;AAGO,SAAS,YAAY,KAAc;IACxC,IAAI,iBAAiB,OAAO;QAC1B,OAAO,MAAM,OAAO;IACtB;IACA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IACA,OAAO;AACT;AAGO,SAAS,aAAa,MAAc,EAAE,OAAe;IAC1D,OAAO,SAAS,CAAC,UAAU,GAAG;AAChC;AAEO,SAAS,eAAe,QAAgB,EAAE,OAAe,EAAE,WAAmB,CAAC;IAMpF,MAAM,iBAAiB,WAAW,CAAC,WAAW,GAAG;IACjD,MAAM,gBAAgB,WAAW;IACjC,MAAM,YAAY,aAAa,eAAe;IAC9C,MAAM,QAAQ,gBAAgB;IAE9B,OAAO;QACL;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 406, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/utils';\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n  {\n    variants: {\n      variant: {\n        default: 'bg-primary text-primary-foreground hover:bg-primary/90',\n        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',\n        outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',\n        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',\n        ghost: 'hover:bg-accent hover:text-accent-foreground',\n        link: 'text-primary underline-offset-4 hover:underline',\n        success: 'bg-green-600 text-white hover:bg-green-700',\n        warning: 'bg-yellow-600 text-white hover:bg-yellow-700',\n      },\n      size: {\n        default: 'h-10 px-4 py-2',\n        sm: 'h-9 rounded-md px-3',\n        lg: 'h-11 rounded-md px-8',\n        xl: 'h-12 rounded-md px-10 text-base',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n  loading?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, loading = false, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;QACX;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC7F,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,wHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 510, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/app/%28dashboard%29/layout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useAuth, usePermissions } from '@/lib/auth-context';\nimport { ProtectedRoute } from '@/components/auth/protected-route';\nimport { Button } from '@/components/ui/button';\nimport {\n  ShoppingCart,\n  BarChart3,\n  Package,\n  Users,\n  Settings,\n  Menu,\n  X,\n  LogOut,\n  Home,\n  CreditCard,\n  ChefHat,\n  MapPin,\n  TrendingUp,\n  UserCircle,\n} from 'lucide-react';\n\ninterface NavigationItem {\n  name: string;\n  href: string;\n  icon: React.ComponentType<{ className?: string }>;\n  permission?: () => boolean;\n}\n\nexport default function DashboardLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const { user, signOut } = useAuth();\n  const permissions = usePermissions();\n\n  const navigation: NavigationItem[] = [\n    {\n      name: 'Dashboard',\n      href: '/dashboard',\n      icon: Home,\n    },\n    {\n      name: 'Point of Sale',\n      href: '/dashboard/pos',\n      icon: ShoppingCart,\n      permission: permissions.canAccessPOS,\n    },\n    {\n      name: 'Orders',\n      href: '/dashboard/orders',\n      icon: CreditCard,\n      permission: permissions.canManageOrders,\n    },\n    {\n      name: 'Products',\n      href: '/dashboard/products',\n      icon: Package,\n      permission: permissions.canManageProducts,\n    },\n    {\n      name: 'Inventory',\n      href: '/dashboard/inventory',\n      icon: Package,\n      permission: permissions.canManageInventory,\n    },\n    {\n      name: 'Customers',\n      href: '/dashboard/customers',\n      icon: Users,\n      permission: permissions.canManageOrders,\n    },\n    {\n      name: 'Kitchen Display',\n      href: '/dashboard/kitchen',\n      icon: ChefHat,\n      permission: permissions.canManageKitchen,\n    },\n    {\n      name: 'Delivery Tracking',\n      href: '/dashboard/delivery',\n      icon: MapPin,\n      permission: permissions.canManageDelivery,\n    },\n    {\n      name: 'Manufacturing',\n      href: '/dashboard/manufacturing',\n      icon: TrendingUp,\n      permission: permissions.canManageProducts,\n    },\n    {\n      name: 'Analytics',\n      href: '/dashboard/analytics',\n      icon: BarChart3,\n      permission: permissions.canViewReports,\n    },\n    {\n      name: 'Users',\n      href: '/dashboard/users',\n      icon: Users,\n      permission: permissions.canManageUsers,\n    },\n    {\n      name: 'Settings',\n      href: '/dashboard/settings',\n      icon: Settings,\n      permission: permissions.canManageUsers,\n    },\n  ];\n\n  const filteredNavigation = navigation.filter(\n    item => !item.permission || item.permission()\n  );\n\n  const handleSignOut = async () => {\n    await signOut();\n  };\n\n  return (\n    <ProtectedRoute>\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Mobile sidebar */}\n        <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>\n          <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n          <div className=\"fixed inset-y-0 left-0 flex w-64 flex-col bg-white\">\n            <div className=\"flex h-16 items-center justify-between px-4 border-b\">\n              <div className=\"flex items-center\">\n                <ShoppingCart className=\"h-8 w-8 text-blue-600 mr-2\" />\n                <span className=\"text-xl font-bold text-gray-900\">POS Pro</span>\n              </div>\n              <button\n                onClick={() => setSidebarOpen(false)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <X className=\"h-6 w-6\" />\n              </button>\n            </div>\n            <nav className=\"flex-1 px-4 py-4 space-y-1\">\n              {filteredNavigation.map((item) => (\n                <a\n                  key={item.name}\n                  href={item.href}\n                  className=\"flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900\"\n                  onClick={() => setSidebarOpen(false)}\n                >\n                  <item.icon className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </a>\n              ))}\n            </nav>\n          </div>\n        </div>\n\n        {/* Desktop sidebar */}\n        <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n          <div className=\"flex flex-col flex-grow bg-white border-r border-gray-200\">\n            <div className=\"flex h-16 items-center px-4 border-b\">\n              <ShoppingCart className=\"h-8 w-8 text-blue-600 mr-2\" />\n              <span className=\"text-xl font-bold text-gray-900\">POS Pro</span>\n            </div>\n            <nav className=\"flex-1 px-4 py-4 space-y-1\">\n              {filteredNavigation.map((item) => (\n                <a\n                  key={item.name}\n                  href={item.href}\n                  className=\"flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900\"\n                >\n                  <item.icon className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </a>\n              ))}\n            </nav>\n          </div>\n        </div>\n\n        {/* Main content */}\n        <div className=\"lg:pl-64\">\n          {/* Top navigation */}\n          <div className=\"sticky top-0 z-40 bg-white border-b border-gray-200\">\n            <div className=\"flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8\">\n              <button\n                onClick={() => setSidebarOpen(true)}\n                className=\"text-gray-500 hover:text-gray-600 lg:hidden\"\n              >\n                <Menu className=\"h-6 w-6\" />\n              </button>\n\n              <div className=\"flex items-center space-x-4\">\n                {/* User info */}\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"flex items-center space-x-2\">\n                    <UserCircle className=\"h-8 w-8 text-gray-400\" />\n                    <div className=\"hidden sm:block\">\n                      <div className=\"text-sm font-medium text-gray-900\">{user?.name}</div>\n                      <div className=\"text-xs text-gray-500 capitalize\">{user?.role.replace('_', ' ')}</div>\n                    </div>\n                  </div>\n                  \n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={handleSignOut}\n                    className=\"text-gray-500 hover:text-gray-700\"\n                  >\n                    <LogOut className=\"h-4 w-4 mr-2\" />\n                    Sign Out\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Page content */}\n          <main className=\"flex-1\">\n            <div className=\"py-6\">\n              <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n                {children}\n              </div>\n            </div>\n          </main>\n        </div>\n      </div>\n    </ProtectedRoute>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AA8Be,SAAS,gBAAgB,EACtC,QAAQ,EAGT;;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,aAA+B;QACnC;YACE,MAAM;YACN,MAAM;YACN,MAAM,sMAAA,CAAA,OAAI;QACZ;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,yNAAA,CAAA,eAAY;YAClB,YAAY,YAAY,YAAY;QACtC;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,qNAAA,CAAA,aAAU;YAChB,YAAY,YAAY,eAAe;QACzC;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,2MAAA,CAAA,UAAO;YACb,YAAY,YAAY,iBAAiB;QAC3C;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,2MAAA,CAAA,UAAO;YACb,YAAY,YAAY,kBAAkB;QAC5C;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,uMAAA,CAAA,QAAK;YACX,YAAY,YAAY,eAAe;QACzC;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,+MAAA,CAAA,UAAO;YACb,YAAY,YAAY,gBAAgB;QAC1C;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,6MAAA,CAAA,SAAM;YACZ,YAAY,YAAY,iBAAiB;QAC3C;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,qNAAA,CAAA,aAAU;YAChB,YAAY,YAAY,iBAAiB;QAC3C;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,qNAAA,CAAA,YAAS;YACf,YAAY,YAAY,cAAc;QACxC;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,uMAAA,CAAA,QAAK;YACX,YAAY,YAAY,cAAc;QACxC;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,6MAAA,CAAA,WAAQ;YACd,YAAY,YAAY,cAAc;QACxC;KACD;IAED,MAAM,qBAAqB,WAAW,MAAM,CAC1C,CAAA,OAAQ,CAAC,KAAK,UAAU,IAAI,KAAK,UAAU;IAG7C,MAAM,gBAAgB;QACpB,MAAM;IACR;IAEA,qBACE,6LAAC,mJAAA,CAAA,iBAAc;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAW,CAAC,6BAA6B,EAAE,cAAc,UAAU,UAAU;;sCAChF,6LAAC;4BAAI,WAAU;4BAA0C,SAAS,IAAM,eAAe;;;;;;sCACvF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,6LAAC;oDAAK,WAAU;8DAAkC;;;;;;;;;;;;sDAEpD,6LAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGjB,6LAAC;oCAAI,WAAU;8CACZ,mBAAmB,GAAG,CAAC,CAAC,qBACvB,6LAAC;4CAEC,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,SAAS,IAAM,eAAe;;8DAE9B,6LAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;2CANL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAcxB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,6LAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;0CAEpD,6LAAC;gCAAI,WAAU;0CACZ,mBAAmB,GAAG,CAAC,CAAC,qBACvB,6LAAC;wCAEC,MAAM,KAAK,IAAI;wCACf,WAAU;;0DAEV,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CACpB,KAAK,IAAI;;uCALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;8BAaxB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,eAAe;wCAC9B,WAAU;kDAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAGlB,6LAAC;wCAAI,WAAU;kDAEb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAqC,MAAM;;;;;;8EAC1D,6LAAC;oEAAI,WAAU;8EAAoC,MAAM,KAAK,QAAQ,KAAK;;;;;;;;;;;;;;;;;;8DAI/E,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;;sEAEV,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAS7C,6LAAC;4BAAK,WAAU;sCACd,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjB;GArMwB;;QAMI,iIAAA,CAAA,UAAO;QACb,iIAAA,CAAA,iBAAc;;;KAPZ", "debugId": null}}]}