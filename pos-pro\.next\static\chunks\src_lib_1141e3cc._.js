(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/supabase.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "createSupabaseBrowserClient": (()=>createSupabaseBrowserClient),
    "supabase": (()=>supabase)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createBrowserClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createBrowserClient.js [app-client] (ecmascript)");
;
;
const supabaseUrl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseAnonKey);
function createSupabaseBrowserClient() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createBrowserClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createBrowserClient"])(supabaseUrl, supabaseAnonKey);
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/auth-context.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth),
    "usePermissions": (()=>usePermissions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature();
'use client';
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function AuthProvider({ children }) {
    _s();
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [session, setSession] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createSupabaseBrowserClient"])();
    // Fetch user profile from our users table
    const fetchUserProfile = async (authUser)=>{
        try {
            const { data, error } = await supabase.from('users').select(`
          id,
          email,
          name,
          role,
          branch_id,
          territory_id,
          phone,
          avatar_url,
          is_active,
          branches:branch_id (
            id,
            name,
            address
          ),
          territories:territory_id (
            id,
            name
          )
        `).eq('id', authUser.id).eq('is_active', true).single();
            if (error || !data) {
                console.error('Error fetching user profile:', error);
                return null;
            }
            return {
                id: data.id,
                email: data.email,
                name: data.name,
                role: data.role,
                branch_id: data.branch_id,
                territory_id: data.territory_id,
                phone: data.phone,
                avatar_url: data.avatar_url,
                is_active: data.is_active,
                created_at: '',
                updated_at: ''
            };
        } catch (error) {
            console.error('Error in fetchUserProfile:', error);
            return null;
        }
    };
    // Initialize auth state
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            const initializeAuth = {
                "AuthProvider.useEffect.initializeAuth": async ()=>{
                    try {
                        const { data: { session: initialSession } } = await supabase.auth.getSession();
                        if (initialSession?.user) {
                            const userProfile = await fetchUserProfile(initialSession.user);
                            setUser(userProfile);
                            setSession(initialSession);
                        }
                    } catch (error) {
                        console.error('Error initializing auth:', error);
                    } finally{
                        setLoading(false);
                    }
                }
            }["AuthProvider.useEffect.initializeAuth"];
            initializeAuth();
            // Listen for auth changes
            const { data: { subscription } } = supabase.auth.onAuthStateChange({
                "AuthProvider.useEffect": async (event, session)=>{
                    console.log('Auth state changed:', event, session?.user?.email);
                    if (session?.user) {
                        const userProfile = await fetchUserProfile(session.user);
                        setUser(userProfile);
                        setSession(session);
                    } else {
                        setUser(null);
                        setSession(null);
                    }
                    setLoading(false);
                }
            }["AuthProvider.useEffect"]);
            return ({
                "AuthProvider.useEffect": ()=>{
                    subscription.unsubscribe();
                }
            })["AuthProvider.useEffect"];
        }
    }["AuthProvider.useEffect"], []);
    const signIn = async (email, password)=>{
        try {
            setLoading(true);
            const { data, error } = await supabase.auth.signInWithPassword({
                email,
                password
            });
            if (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
            if (data.user) {
                const userProfile = await fetchUserProfile(data.user);
                if (!userProfile) {
                    await supabase.auth.signOut();
                    return {
                        success: false,
                        error: 'User profile not found or inactive'
                    };
                }
                setUser(userProfile);
                setSession(data.session);
            }
            return {
                success: true
            };
        } catch (error) {
            console.error('Sign in error:', error);
            return {
                success: false,
                error: 'An unexpected error occurred'
            };
        } finally{
            setLoading(false);
        }
    };
    const signOut = async ()=>{
        try {
            setLoading(true);
            await supabase.auth.signOut();
            setUser(null);
            setSession(null);
        } catch (error) {
            console.error('Sign out error:', error);
        } finally{
            setLoading(false);
        }
    };
    const refreshUser = async ()=>{
        if (session?.user) {
            const userProfile = await fetchUserProfile(session.user);
            setUser(userProfile);
        }
    };
    const value = {
        user,
        session,
        loading,
        signIn,
        signOut,
        refreshUser
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/lib/auth-context.tsx",
        lineNumber: 183,
        columnNumber: 5
    }, this);
}
_s(AuthProvider, "sIDOCMze9iVqwxkgWIhOu8vskSI=");
_c = AuthProvider;
function useAuth() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
}
_s1(useAuth, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function usePermissions() {
    _s2();
    const { user } = useAuth();
    const hasRole = (roles)=>{
        if (!user) return false;
        const roleArray = Array.isArray(roles) ? roles : [
            roles
        ];
        return roleArray.includes(user.role);
    };
    const canManageProducts = ()=>hasRole([
            'admin',
            'manager'
        ]);
    const canManageOrders = ()=>hasRole([
            'admin',
            'manager',
            'cashier'
        ]);
    const canManageInventory = ()=>hasRole([
            'admin',
            'manager',
            'warehouse_staff'
        ]);
    const canManageUsers = ()=>hasRole([
            'admin',
            'manager'
        ]);
    const canViewReports = ()=>hasRole([
            'admin',
            'manager'
        ]);
    const canManageKitchen = ()=>hasRole([
            'admin',
            'manager',
            'kitchen_staff'
        ]);
    const canManageDelivery = ()=>hasRole([
            'admin',
            'manager',
            'sales_rep'
        ]);
    const canAccessPOS = ()=>hasRole([
            'admin',
            'manager',
            'cashier',
            'sales_rep'
        ]);
    return {
        hasRole,
        canManageProducts,
        canManageOrders,
        canManageInventory,
        canManageUsers,
        canViewReports,
        canManageKitchen,
        canManageDelivery,
        canAccessPOS
    };
}
_s2(usePermissions, "9ep4vdl3mBfipxjmc+tQCDhw6Ik=", false, function() {
    return [
        useAuth
    ];
});
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_lib_1141e3cc._.js.map