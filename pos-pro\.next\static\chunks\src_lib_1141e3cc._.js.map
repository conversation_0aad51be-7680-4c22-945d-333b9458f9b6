{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\nimport { createBrowserClient } from '@supabase/ssr';\n\n// Get environment variables with fallbacks for development\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://demo.supabase.co';\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'demo_anon_key';\n\n// Check if we're in a browser environment and have valid Supabase credentials\nconst isValidSupabaseConfig = () => {\n  return supabaseUrl !== 'https://demo.supabase.co' && supabaseAnonKey !== 'demo_anon_key';\n};\n\n// Client-side Supabase client with error handling\nexport const supabase = (() => {\n  try {\n    if (isValidSupabaseConfig()) {\n      return createClient(supabaseUrl, supabaseAnonKey);\n    } else {\n      // Return a mock client for development when Supabase is not configured\n      console.warn('Supabase not configured. Using mock client for development.');\n      return null;\n    }\n  } catch (error) {\n    console.error('Error creating Supabase client:', error);\n    return null;\n  }\n})();\n\n// Browser client for SSR with error handling\nexport function createSupabaseBrowserClient() {\n  try {\n    if (isValidSupabaseConfig()) {\n      return createBrowserClient(supabaseUrl, supabaseAnonKey);\n    } else {\n      console.warn('Supabase not configured. Using mock client for development.');\n      return null;\n    }\n  } catch (error) {\n    console.error('Error creating Supabase browser client:', error);\n    return null;\n  }\n}\n\n// Database types (will be generated from Supabase)\nexport type Database = {\n  public: {\n    Tables: {\n      users: {\n        Row: {\n          id: string;\n          email: string;\n          name: string;\n          role: string;\n          branch_id: string | null;\n          territory_id: string | null;\n          phone: string | null;\n          avatar_url: string | null;\n          is_active: boolean;\n          created_at: string;\n          updated_at: string;\n        };\n        Insert: {\n          id?: string;\n          email: string;\n          name: string;\n          role: string;\n          branch_id?: string | null;\n          territory_id?: string | null;\n          phone?: string | null;\n          avatar_url?: string | null;\n          is_active?: boolean;\n          created_at?: string;\n          updated_at?: string;\n        };\n        Update: {\n          id?: string;\n          email?: string;\n          name?: string;\n          role?: string;\n          branch_id?: string | null;\n          territory_id?: string | null;\n          phone?: string | null;\n          avatar_url?: string | null;\n          is_active?: boolean;\n          updated_at?: string;\n        };\n      };\n      branches: {\n        Row: {\n          id: string;\n          name: string;\n          address: string;\n          phone: string;\n          email: string;\n          latitude: number;\n          longitude: number;\n          delivery_radius: number;\n          is_active: boolean;\n          manager_id: string;\n          created_at: string;\n          updated_at: string;\n        };\n        Insert: {\n          id?: string;\n          name: string;\n          address: string;\n          phone: string;\n          email: string;\n          latitude: number;\n          longitude: number;\n          delivery_radius?: number;\n          is_active?: boolean;\n          manager_id: string;\n          created_at?: string;\n          updated_at?: string;\n        };\n        Update: {\n          id?: string;\n          name?: string;\n          address?: string;\n          phone?: string;\n          email?: string;\n          latitude?: number;\n          longitude?: number;\n          delivery_radius?: number;\n          is_active?: boolean;\n          manager_id?: string;\n          updated_at?: string;\n        };\n      };\n      customers: {\n        Row: {\n          id: string;\n          name: string;\n          email: string | null;\n          phone: string;\n          address: string;\n          latitude: number | null;\n          longitude: number | null;\n          loyalty_points: number;\n          rating: number;\n          notes: string | null;\n          is_active: boolean;\n          created_at: string;\n          updated_at: string;\n        };\n        Insert: {\n          id?: string;\n          name: string;\n          email?: string | null;\n          phone: string;\n          address: string;\n          latitude?: number | null;\n          longitude?: number | null;\n          loyalty_points?: number;\n          rating?: number;\n          notes?: string | null;\n          is_active?: boolean;\n          created_at?: string;\n          updated_at?: string;\n        };\n        Update: {\n          id?: string;\n          name?: string;\n          email?: string | null;\n          phone?: string;\n          address?: string;\n          latitude?: number | null;\n          longitude?: number | null;\n          loyalty_points?: number;\n          rating?: number;\n          notes?: string | null;\n          is_active?: boolean;\n          updated_at?: string;\n        };\n      };\n      products: {\n        Row: {\n          id: string;\n          name: string;\n          description: string | null;\n          category_id: string;\n          sku: string;\n          barcode: string | null;\n          price: number;\n          cost: number;\n          unit: string;\n          is_manufactured: boolean;\n          recipe_id: string | null;\n          image_url: string | null;\n          is_active: boolean;\n          created_at: string;\n          updated_at: string;\n        };\n        Insert: {\n          id?: string;\n          name: string;\n          description?: string | null;\n          category_id: string;\n          sku: string;\n          barcode?: string | null;\n          price: number;\n          cost: number;\n          unit: string;\n          is_manufactured?: boolean;\n          recipe_id?: string | null;\n          image_url?: string | null;\n          is_active?: boolean;\n          created_at?: string;\n          updated_at?: string;\n        };\n        Update: {\n          id?: string;\n          name?: string;\n          description?: string | null;\n          category_id?: string;\n          sku?: string;\n          barcode?: string | null;\n          price?: number;\n          cost?: number;\n          unit?: string;\n          is_manufactured?: boolean;\n          recipe_id?: string | null;\n          image_url?: string | null;\n          is_active?: boolean;\n          updated_at?: string;\n        };\n      };\n      orders: {\n        Row: {\n          id: string;\n          order_number: string;\n          customer_id: string | null;\n          branch_id: string;\n          cashier_id: string;\n          sales_rep_id: string | null;\n          table_id: string | null;\n          order_type: string;\n          status: string;\n          subtotal: number;\n          tax_amount: number;\n          discount_amount: number;\n          total_amount: number;\n          payment_method: string;\n          payment_status: string;\n          notes: string | null;\n          delivery_address: string | null;\n          delivery_latitude: number | null;\n          delivery_longitude: number | null;\n          estimated_delivery_time: string | null;\n          created_at: string;\n          updated_at: string;\n        };\n        Insert: {\n          id?: string;\n          order_number: string;\n          customer_id?: string | null;\n          branch_id: string;\n          cashier_id: string;\n          sales_rep_id?: string | null;\n          table_id?: string | null;\n          order_type: string;\n          status?: string;\n          subtotal: number;\n          tax_amount: number;\n          discount_amount: number;\n          total_amount: number;\n          payment_method: string;\n          payment_status?: string;\n          notes?: string | null;\n          delivery_address?: string | null;\n          delivery_latitude?: number | null;\n          delivery_longitude?: number | null;\n          estimated_delivery_time?: string | null;\n          created_at?: string;\n          updated_at?: string;\n        };\n        Update: {\n          id?: string;\n          order_number?: string;\n          customer_id?: string | null;\n          branch_id?: string;\n          cashier_id?: string;\n          sales_rep_id?: string | null;\n          table_id?: string | null;\n          order_type?: string;\n          status?: string;\n          subtotal?: number;\n          tax_amount?: number;\n          discount_amount?: number;\n          total_amount?: number;\n          payment_method?: string;\n          payment_status?: string;\n          notes?: string | null;\n          delivery_address?: string | null;\n          delivery_latitude?: number | null;\n          delivery_longitude?: number | null;\n          estimated_delivery_time?: string | null;\n          updated_at?: string;\n        };\n      };\n    };\n    Views: {\n      [_ in never]: never;\n    };\n    Functions: {\n      [_ in never]: never;\n    };\n    Enums: {\n      user_role: 'admin' | 'manager' | 'cashier' | 'kitchen_staff' | 'sales_rep' | 'warehouse_staff';\n      order_type: 'dine_in' | 'takeaway' | 'delivery' | 'field_sales';\n      order_status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'delivered' | 'cancelled';\n      payment_method: 'cash' | 'card' | 'digital_wallet' | 'credit';\n      payment_status: 'pending' | 'paid' | 'partial' | 'refunded';\n    };\n  };\n};\n"], "names": [], "mappings": ";;;;AAIoB;AAHpB;;;AAEA,2DAA2D;AAC3D,MAAM,cAAc,gEAAwC;AAC5D,MAAM,kBAAkB,qDAA6C;AAErE,8EAA8E;AAC9E,MAAM,wBAAwB;IAC5B,OAAO,gBAAgB,8BAA8B,oBAAoB;AAC3E;AAGO,MAAM,WAAW,CAAC;IACvB,IAAI;QACF,IAAI,yBAAyB;;QAE7B,OAAO;YACL,uEAAuE;YACvE,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;IACT;AACF,CAAC;AAGM,SAAS;IACd,IAAI;QACF,IAAI,yBAAyB;;QAE7B,OAAO;YACL,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2CAA2C;QACzD,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/nova/Pos%20pro/pos-pro/src/lib/auth-context.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { User, Session } from '@supabase/supabase-js';\nimport { createSupabaseBrowserClient } from '@/lib/supabase';\nimport { User as AppUser } from '@/types';\n\ninterface AuthContextType {\n  user: AppUser | null;\n  session: Session | null;\n  loading: boolean;\n  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;\n  signOut: () => Promise<void>;\n  refreshUser: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<AppUser | null>(null);\n  const [session, setSession] = useState<Session | null>(null);\n  const [loading, setLoading] = useState(true);\n  const supabase = createSupabaseBrowserClient();\n\n  // If Supabase is not configured, provide mock authentication for development\n  const isDemoMode = !supabase;\n\n  // Fetch user profile from our users table\n  const fetchUserProfile = async (authUser: User): Promise<AppUser | null> => {\n    try {\n      if (!supabase) {\n        console.warn('Supabase not available for fetchUserProfile');\n        return null;\n      }\n\n      const { data, error } = await supabase\n        .from('users')\n        .select(`\n          id,\n          email,\n          name,\n          role,\n          branch_id,\n          territory_id,\n          phone,\n          avatar_url,\n          is_active,\n          branches:branch_id (\n            id,\n            name,\n            address\n          ),\n          territories:territory_id (\n            id,\n            name\n          )\n        `)\n        .eq('id', authUser.id)\n        .eq('is_active', true)\n        .single();\n\n      if (error || !data) {\n        console.error('Error fetching user profile:', error);\n        return null;\n      }\n\n      return {\n        id: data.id,\n        email: data.email,\n        name: data.name,\n        role: data.role as AppUser['role'],\n        branch_id: data.branch_id,\n        territory_id: data.territory_id,\n        phone: data.phone,\n        avatar_url: data.avatar_url,\n        is_active: data.is_active,\n        created_at: '', // Not needed for context\n        updated_at: '', // Not needed for context\n      };\n    } catch (error) {\n      console.error('Error in fetchUserProfile:', error);\n      return null;\n    }\n  };\n\n  // Initialize auth state\n  useEffect(() => {\n    const initializeAuth = async () => {\n      try {\n        if (isDemoMode) {\n          // Demo mode - provide mock user for development\n          console.log('Running in demo mode - using mock authentication');\n          const mockUser: AppUser = {\n            id: 'demo-user-id',\n            email: '<EMAIL>',\n            name: 'Demo User',\n            role: 'admin',\n            branch_id: 'demo-branch-id',\n            territory_id: null,\n            phone: '******-0100',\n            avatar_url: null,\n            is_active: true,\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString(),\n          };\n          setUser(mockUser);\n          setLoading(false);\n          return;\n        }\n\n        const { data: { session: initialSession } } = await supabase!.auth.getSession();\n\n        if (initialSession?.user) {\n          const userProfile = await fetchUserProfile(initialSession.user);\n          setUser(userProfile);\n          setSession(initialSession);\n        }\n      } catch (error) {\n        console.error('Error initializing auth:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    initializeAuth();\n\n    if (!isDemoMode && supabase) {\n      // Listen for auth changes only if Supabase is configured\n      const { data: { subscription } } = supabase.auth.onAuthStateChange(\n        async (event, session) => {\n          console.log('Auth state changed:', event, session?.user?.email);\n\n          if (session?.user) {\n            const userProfile = await fetchUserProfile(session.user);\n            setUser(userProfile);\n            setSession(session);\n          } else {\n            setUser(null);\n            setSession(null);\n          }\n\n          setLoading(false);\n        }\n      );\n\n      return () => {\n        subscription.unsubscribe();\n      };\n    }\n  }, [isDemoMode]);\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      setLoading(true);\n\n      if (isDemoMode) {\n        // Demo mode - simulate login with predefined users\n        const demoUsers: Record<string, AppUser> = {\n          '<EMAIL>': {\n            id: 'demo-admin-id',\n            email: '<EMAIL>',\n            name: 'Demo Admin',\n            role: 'admin',\n            branch_id: null,\n            territory_id: null,\n            phone: '******-0110',\n            avatar_url: null,\n            is_active: true,\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString(),\n          },\n          '<EMAIL>': {\n            id: 'demo-manager-id',\n            email: '<EMAIL>',\n            name: 'Demo Manager',\n            role: 'manager',\n            branch_id: 'demo-branch-id',\n            territory_id: null,\n            phone: '******-0111',\n            avatar_url: null,\n            is_active: true,\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString(),\n          },\n          '<EMAIL>': {\n            id: 'demo-cashier-id',\n            email: '<EMAIL>',\n            name: 'Demo Cashier',\n            role: 'cashier',\n            branch_id: 'demo-branch-id',\n            territory_id: null,\n            phone: '******-0113',\n            avatar_url: null,\n            is_active: true,\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString(),\n          },\n        };\n\n        const demoUser = demoUsers[email];\n        if (demoUser && password.includes('123')) {\n          setUser(demoUser);\n          return { success: true };\n        } else {\n          return { success: false, error: 'Invalid demo credentials' };\n        }\n      }\n\n      const { data, error } = await supabase!.auth.signInWithPassword({\n        email,\n        password,\n      });\n\n      if (error) {\n        return { success: false, error: error.message };\n      }\n\n      if (data.user) {\n        const userProfile = await fetchUserProfile(data.user);\n        if (!userProfile) {\n          await supabase!.auth.signOut();\n          return { success: false, error: 'User profile not found or inactive' };\n        }\n        setUser(userProfile);\n        setSession(data.session);\n      }\n\n      return { success: true };\n    } catch (error) {\n      console.error('Sign in error:', error);\n      return { success: false, error: 'An unexpected error occurred' };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const signOut = async () => {\n    try {\n      setLoading(true);\n      if (!isDemoMode && supabase) {\n        await supabase.auth.signOut();\n      }\n      setUser(null);\n      setSession(null);\n    } catch (error) {\n      console.error('Sign out error:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const refreshUser = async () => {\n    if (isDemoMode) {\n      // In demo mode, user is already set\n      return;\n    }\n    if (session?.user) {\n      const userProfile = await fetchUserProfile(session.user);\n      setUser(userProfile);\n    }\n  };\n\n  const value = {\n    user,\n    session,\n    loading,\n    signIn,\n    signOut,\n    refreshUser,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n\n// Permission checking hooks\nexport function usePermissions() {\n  const { user } = useAuth();\n\n  const hasRole = (roles: AppUser['role'] | AppUser['role'][]) => {\n    if (!user) return false;\n    const roleArray = Array.isArray(roles) ? roles : [roles];\n    return roleArray.includes(user.role);\n  };\n\n  const canManageProducts = () => hasRole(['admin', 'manager']);\n  const canManageOrders = () => hasRole(['admin', 'manager', 'cashier']);\n  const canManageInventory = () => hasRole(['admin', 'manager', 'warehouse_staff']);\n  const canManageUsers = () => hasRole(['admin', 'manager']);\n  const canViewReports = () => hasRole(['admin', 'manager']);\n  const canManageKitchen = () => hasRole(['admin', 'manager', 'kitchen_staff']);\n  const canManageDelivery = () => hasRole(['admin', 'manager', 'sales_rep']);\n  const canAccessPOS = () => hasRole(['admin', 'manager', 'cashier', 'sales_rep']);\n\n  return {\n    hasRole,\n    canManageProducts,\n    canManageOrders,\n    canManageInventory,\n    canManageUsers,\n    canViewReports,\n    canManageKitchen,\n    canManageDelivery,\n    canAccessPOS,\n  };\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAEA;;;AAJA;;;AAgBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,8BAA2B,AAAD;IAE3C,6EAA6E;IAC7E,MAAM,aAAa,CAAC;IAEpB,0CAA0C;IAC1C,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,IAAI,CAAC,UAAU;gBACb,QAAQ,IAAI,CAAC;gBACb,OAAO;YACT;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;;;QAmBT,CAAC,EACA,EAAE,CAAC,MAAM,SAAS,EAAE,EACpB,EAAE,CAAC,aAAa,MAChB,MAAM;YAET,IAAI,SAAS,CAAC,MAAM;gBAClB,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,OAAO;YACT;YAEA,OAAO;gBACL,IAAI,KAAK,EAAE;gBACX,OAAO,KAAK,KAAK;gBACjB,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,WAAW,KAAK,SAAS;gBACzB,cAAc,KAAK,YAAY;gBAC/B,OAAO,KAAK,KAAK;gBACjB,YAAY,KAAK,UAAU;gBAC3B,WAAW,KAAK,SAAS;gBACzB,YAAY;gBACZ,YAAY;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;QACT;IACF;IAEA,wBAAwB;IACxB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;yDAAiB;oBACrB,IAAI;wBACF,IAAI,YAAY;4BACd,gDAAgD;4BAChD,QAAQ,GAAG,CAAC;4BACZ,MAAM,WAAoB;gCACxB,IAAI;gCACJ,OAAO;gCACP,MAAM;gCACN,MAAM;gCACN,WAAW;gCACX,cAAc;gCACd,OAAO;gCACP,YAAY;gCACZ,WAAW;gCACX,YAAY,IAAI,OAAO,WAAW;gCAClC,YAAY,IAAI,OAAO,WAAW;4BACpC;4BACA,QAAQ;4BACR,WAAW;4BACX;wBACF;wBAEA,MAAM,EAAE,MAAM,EAAE,SAAS,cAAc,EAAE,EAAE,GAAG,MAAM,SAAU,IAAI,CAAC,UAAU;wBAE7E,IAAI,gBAAgB,MAAM;4BACxB,MAAM,cAAc,MAAM,iBAAiB,eAAe,IAAI;4BAC9D,QAAQ;4BACR,WAAW;wBACb;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,4BAA4B;oBAC5C,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;YAEA,IAAI,CAAC,cAAc,UAAU;gBAC3B,yDAAyD;gBACzD,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB;8CAChE,OAAO,OAAO;wBACZ,QAAQ,GAAG,CAAC,uBAAuB,OAAO,SAAS,MAAM;wBAEzD,IAAI,SAAS,MAAM;4BACjB,MAAM,cAAc,MAAM,iBAAiB,QAAQ,IAAI;4BACvD,QAAQ;4BACR,WAAW;wBACb,OAAO;4BACL,QAAQ;4BACR,WAAW;wBACb;wBAEA,WAAW;oBACb;;gBAGF;8CAAO;wBACL,aAAa,WAAW;oBAC1B;;YACF;QACF;iCAAG;QAAC;KAAW;IAEf,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,WAAW;YAEX,IAAI,YAAY;gBACd,mDAAmD;gBACnD,MAAM,YAAqC;oBACzC,oBAAoB;wBAClB,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,MAAM;wBACN,WAAW;wBACX,cAAc;wBACd,OAAO;wBACP,YAAY;wBACZ,WAAW;wBACX,YAAY,IAAI,OAAO,WAAW;wBAClC,YAAY,IAAI,OAAO,WAAW;oBACpC;oBACA,uBAAuB;wBACrB,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,MAAM;wBACN,WAAW;wBACX,cAAc;wBACd,OAAO;wBACP,YAAY;wBACZ,WAAW;wBACX,YAAY,IAAI,OAAO,WAAW;wBAClC,YAAY,IAAI,OAAO,WAAW;oBACpC;oBACA,uBAAuB;wBACrB,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,MAAM;wBACN,WAAW;wBACX,cAAc;wBACd,OAAO;wBACP,YAAY;wBACZ,WAAW;wBACX,YAAY,IAAI,OAAO,WAAW;wBAClC,YAAY,IAAI,OAAO,WAAW;oBACpC;gBACF;gBAEA,MAAM,WAAW,SAAS,CAAC,MAAM;gBACjC,IAAI,YAAY,SAAS,QAAQ,CAAC,QAAQ;oBACxC,QAAQ;oBACR,OAAO;wBAAE,SAAS;oBAAK;gBACzB,OAAO;oBACL,OAAO;wBAAE,SAAS;wBAAO,OAAO;oBAA2B;gBAC7D;YACF;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAU,IAAI,CAAC,kBAAkB,CAAC;gBAC9D;gBACA;YACF;YAEA,IAAI,OAAO;gBACT,OAAO;oBAAE,SAAS;oBAAO,OAAO,MAAM,OAAO;gBAAC;YAChD;YAEA,IAAI,KAAK,IAAI,EAAE;gBACb,MAAM,cAAc,MAAM,iBAAiB,KAAK,IAAI;gBACpD,IAAI,CAAC,aAAa;oBAChB,MAAM,SAAU,IAAI,CAAC,OAAO;oBAC5B,OAAO;wBAAE,SAAS;wBAAO,OAAO;oBAAqC;gBACvE;gBACA,QAAQ;gBACR,WAAW,KAAK,OAAO;YACzB;YAEA,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA+B;QACjE,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,UAAU;QACd,IAAI;YACF,WAAW;YACX,IAAI,CAAC,cAAc,UAAU;gBAC3B,MAAM,SAAS,IAAI,CAAC,OAAO;YAC7B;YACA,QAAQ;YACR,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,YAAY;YACd,oCAAoC;YACpC;QACF;QACA,IAAI,SAAS,MAAM;YACjB,MAAM,cAAc,MAAM,iBAAiB,QAAQ,IAAI;YACvD,QAAQ;QACV;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GAlQgB;KAAA;AAoQT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB;AAST,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,GAAG;IAEjB,MAAM,UAAU,CAAC;QACf,IAAI,CAAC,MAAM,OAAO;QAClB,MAAM,YAAY,MAAM,OAAO,CAAC,SAAS,QAAQ;YAAC;SAAM;QACxD,OAAO,UAAU,QAAQ,CAAC,KAAK,IAAI;IACrC;IAEA,MAAM,oBAAoB,IAAM,QAAQ;YAAC;YAAS;SAAU;IAC5D,MAAM,kBAAkB,IAAM,QAAQ;YAAC;YAAS;YAAW;SAAU;IACrE,MAAM,qBAAqB,IAAM,QAAQ;YAAC;YAAS;YAAW;SAAkB;IAChF,MAAM,iBAAiB,IAAM,QAAQ;YAAC;YAAS;SAAU;IACzD,MAAM,iBAAiB,IAAM,QAAQ;YAAC;YAAS;SAAU;IACzD,MAAM,mBAAmB,IAAM,QAAQ;YAAC;YAAS;YAAW;SAAgB;IAC5E,MAAM,oBAAoB,IAAM,QAAQ;YAAC;YAAS;YAAW;SAAY;IACzE,MAAM,eAAe,IAAM,QAAQ;YAAC;YAAS;YAAW;YAAW;SAAY;IAE/E,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;IA7BgB;;QACG", "debugId": null}}]}