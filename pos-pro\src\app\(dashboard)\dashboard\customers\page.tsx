'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/lib/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Users, 
  Plus, 
  Edit, 
  Phone,
  Mail,
  MapPin,
  Star,
  Gift,
  Search,
  Filter,
  Download,
  TrendingUp
} from 'lucide-react';
import { formatCurrency } from '@/utils';
import { Customer } from '@/types';

interface CustomerWithStats extends Customer {
  total_orders: number;
  total_spent: number;
  last_order_date?: string;
  average_order_value: number;
  favorite_items: string[];
}

export default function CustomersPage() {
  const { user } = useAuth();
  const [customers, setCustomers] = useState<CustomerWithStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [loyaltyFilter, setLoyaltyFilter] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);

  // Mock data for demonstration
  const mockCustomers: CustomerWithStats[] = [
    {
      id: 'cust-1',
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '******-0150',
      address: '123 Customer St, Downtown',
      location: { latitude: 40.7127, longitude: -74.005 },
      loyalty_points: 1250,
      rating: 4.8,
      notes: 'Regular customer, prefers oat milk in coffee',
      is_active: true,
      total_orders: 45,
      total_spent: 567.50,
      last_order_date: '2024-01-15T10:30:00Z',
      average_order_value: 12.61,
      favorite_items: ['Cappuccino', 'Caesar Salad', 'Grilled Chicken'],
      created_at: '2023-06-15T00:00:00Z',
      updated_at: '2024-01-15T10:30:00Z',
    },
    {
      id: 'cust-2',
      name: 'Jane Smith',
      email: '<EMAIL>',
      phone: '******-0151',
      address: '456 Client Ave, Uptown',
      location: { latitude: 40.7483, longitude: -73.9856 },
      loyalty_points: 890,
      rating: 4.5,
      notes: 'Vegetarian, allergic to nuts',
      is_active: true,
      total_orders: 32,
      total_spent: 398.75,
      last_order_date: '2024-01-14T16:45:00Z',
      average_order_value: 12.46,
      favorite_items: ['Latte', 'Caesar Salad', 'Chocolate Cake'],
      created_at: '2023-08-22T00:00:00Z',
      updated_at: '2024-01-14T16:45:00Z',
    },
    {
      id: 'cust-3',
      name: 'Bob Johnson',
      email: '<EMAIL>',
      phone: '******-0152',
      address: '789 Buyer Blvd, Westside',
      location: { latitude: 40.7588, longitude: -74.0058 },
      loyalty_points: 2100,
      rating: 4.9,
      notes: 'VIP customer, owns local business',
      is_active: true,
      total_orders: 78,
      total_spent: 1245.80,
      last_order_date: '2024-01-15T12:15:00Z',
      average_order_value: 15.97,
      favorite_items: ['Espresso', 'Grilled Chicken', 'Cappuccino'],
      created_at: '2023-03-10T00:00:00Z',
      updated_at: '2024-01-15T12:15:00Z',
    },
    {
      id: 'cust-4',
      name: 'Alice Brown',
      email: '<EMAIL>',
      phone: '******-0153',
      address: '321 Patron Place, Downtown',
      location: { latitude: 40.7129, longitude: -74.007 },
      loyalty_points: 450,
      rating: 4.2,
      notes: null,
      is_active: true,
      total_orders: 18,
      total_spent: 189.25,
      last_order_date: '2024-01-13T09:30:00Z',
      average_order_value: 10.51,
      favorite_items: ['Latte', 'Chocolate Cake'],
      created_at: '2023-11-05T00:00:00Z',
      updated_at: '2024-01-13T09:30:00Z',
    },
    {
      id: 'cust-5',
      name: 'Charlie Wilson',
      email: '<EMAIL>',
      phone: '******-0154',
      address: '654 Regular Rd, Suburbs',
      location: { latitude: 40.7200, longitude: -74.0100 },
      loyalty_points: 75,
      rating: 3.8,
      notes: 'New customer, referred by Bob Johnson',
      is_active: true,
      total_orders: 3,
      total_spent: 42.50,
      last_order_date: '2024-01-12T14:20:00Z',
      average_order_value: 14.17,
      favorite_items: ['Cappuccino'],
      created_at: '2024-01-10T00:00:00Z',
      updated_at: '2024-01-12T14:20:00Z',
    },
  ];

  useEffect(() => {
    // In a real app, this would fetch from the API
    setCustomers(mockCustomers);
    setLoading(false);
  }, []);

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.phone.includes(searchTerm);
    
    let matchesLoyalty = true;
    if (loyaltyFilter === 'vip') {
      matchesLoyalty = customer.loyalty_points >= 1000;
    } else if (loyaltyFilter === 'regular') {
      matchesLoyalty = customer.loyalty_points >= 500 && customer.loyalty_points < 1000;
    } else if (loyaltyFilter === 'new') {
      matchesLoyalty = customer.loyalty_points < 500;
    }
    
    return matchesSearch && matchesLoyalty;
  });

  const getLoyaltyTier = (points: number) => {
    if (points >= 1000) return { tier: 'VIP', color: 'bg-purple-100 text-purple-800' };
    if (points >= 500) return { tier: 'Regular', color: 'bg-blue-100 text-blue-800' };
    return { tier: 'New', color: 'bg-gray-100 text-gray-800' };
  };

  const customerStats = {
    total: customers.length,
    vip: customers.filter(c => c.loyalty_points >= 1000).length,
    regular: customers.filter(c => c.loyalty_points >= 500 && c.loyalty_points < 1000).length,
    new: customers.filter(c => c.loyalty_points < 500).length,
    totalSpent: customers.reduce((sum, c) => sum + c.total_spent, 0),
    averageOrderValue: customers.reduce((sum, c) => sum + c.average_order_value, 0) / customers.length,
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <h1 className="text-2xl font-bold">Customers</h1>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Customers</h1>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button size="sm" onClick={() => setShowCreateModal(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Customer
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Customers</p>
                <p className="text-2xl font-bold">{customerStats.total}</p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">VIP Customers</p>
                <p className="text-2xl font-bold text-purple-600">{customerStats.vip}</p>
              </div>
              <Star className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                <p className="text-2xl font-bold text-green-600">
                  {formatCurrency(customerStats.totalSpent)}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Order Value</p>
                <p className="text-2xl font-bold text-orange-600">
                  {formatCurrency(customerStats.averageOrderValue)}
                </p>
              </div>
              <Gift className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search customers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                leftIcon={<Search size={20} />}
              />
            </div>
            <select
              value={loyaltyFilter}
              onChange={(e) => setLoyaltyFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="">All Customers</option>
              <option value="vip">VIP (1000+ points)</option>
              <option value="regular">Regular (500-999 points)</option>
              <option value="new">New (0-499 points)</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Customers Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredCustomers.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No customers found</h3>
            <p className="text-gray-600">Try adjusting your search criteria</p>
          </div>
        ) : (
          filteredCustomers.map((customer) => {
            const loyaltyTier = getLoyaltyTier(customer.loyalty_points);
            
            return (
              <Card key={customer.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">{customer.name}</CardTitle>
                      <div className="flex items-center space-x-2 mt-1">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${loyaltyTier.color}`}>
                          {loyaltyTier.tier}
                        </span>
                        <div className="flex items-center">
                          <Star className="h-3 w-3 text-yellow-500 mr-1" />
                          <span className="text-sm">{customer.rating.toFixed(1)}</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-purple-600">
                        {customer.loyalty_points} pts
                      </div>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="space-y-3">
                  {/* Contact Information */}
                  <div className="space-y-2">
                    <div className="flex items-center text-sm text-gray-600">
                      <Phone className="h-4 w-4 mr-2" />
                      {customer.phone}
                    </div>
                    {customer.email && (
                      <div className="flex items-center text-sm text-gray-600">
                        <Mail className="h-4 w-4 mr-2" />
                        {customer.email}
                      </div>
                    )}
                    <div className="flex items-start text-sm text-gray-600">
                      <MapPin className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                      <span className="line-clamp-2">{customer.address}</span>
                    </div>
                  </div>

                  {/* Customer Stats */}
                  <div className="grid grid-cols-2 gap-4 pt-2 border-t">
                    <div>
                      <div className="text-xs text-gray-500">Total Orders</div>
                      <div className="font-semibold">{customer.total_orders}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Total Spent</div>
                      <div className="font-semibold">{formatCurrency(customer.total_spent)}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Avg Order</div>
                      <div className="font-semibold">{formatCurrency(customer.average_order_value)}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Last Order</div>
                      <div className="font-semibold text-xs">
                        {customer.last_order_date 
                          ? new Date(customer.last_order_date).toLocaleDateString()
                          : 'Never'
                        }
                      </div>
                    </div>
                  </div>

                  {/* Favorite Items */}
                  {customer.favorite_items.length > 0 && (
                    <div className="pt-2 border-t">
                      <div className="text-xs text-gray-500 mb-1">Favorite Items</div>
                      <div className="flex flex-wrap gap-1">
                        {customer.favorite_items.slice(0, 3).map((item, index) => (
                          <span key={index} className="px-2 py-1 bg-gray-100 text-xs rounded">
                            {item}
                          </span>
                        ))}
                        {customer.favorite_items.length > 3 && (
                          <span className="px-2 py-1 bg-gray-100 text-xs rounded">
                            +{customer.favorite_items.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Notes */}
                  {customer.notes && (
                    <div className="pt-2 border-t">
                      <div className="text-xs text-gray-500 mb-1">Notes</div>
                      <div className="text-sm text-gray-700 line-clamp-2">
                        {customer.notes}
                      </div>
                    </div>
                  )}

                  {/* Actions */}
                  <div className="flex space-x-2 pt-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Edit className="h-3 w-3 mr-1" />
                      Edit
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      <Gift className="h-3 w-3 mr-1" />
                      Reward
                    </Button>
                  </div>
                </CardContent>
              </Card>
            );
          })
        )}
      </div>

      {/* Create Customer Modal Placeholder */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-2xl mx-4">
            <CardHeader>
              <CardTitle>Add New Customer</CardTitle>
              <CardDescription>Create a new customer profile</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <Input label="Full Name" placeholder="Enter customer name" />
                  <Input label="Phone" placeholder="Enter phone number" />
                </div>
                <Input label="Email" type="email" placeholder="Enter email address" />
                <Input label="Address" placeholder="Enter full address" />
                <Input label="Notes" placeholder="Any special notes or preferences" />
              </div>
              <div className="flex justify-end space-x-2 mt-6">
                <Button variant="outline" onClick={() => setShowCreateModal(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setShowCreateModal(false)}>
                  Create Customer
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
