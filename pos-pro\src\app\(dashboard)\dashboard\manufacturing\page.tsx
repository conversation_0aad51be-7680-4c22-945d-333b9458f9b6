'use client';

import { useState, useEffect } from 'react';
import { useAuth, usePermissions } from '@/lib/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  ChefHat, 
  Plus, 
  Edit, 
  Eye,
  Clock,
  CheckCircle,
  Play,
  Pause,
  Search,
  Filter,
  Package,
  Utensils,
  Timer,
  AlertTriangle
} from 'lucide-react';
import { formatCurrency, formatDateTime } from '@/utils';

interface Recipe {
  id: string;
  name: string;
  product_name: string;
  description: string;
  yield_quantity: number;
  preparation_time: number; // in minutes
  cost_per_unit: number;
  instructions: string;
  is_active: boolean;
  ingredients: Array<{
    id: string;
    ingredient_name: string;
    ingredient_sku: string;
    quantity: number;
    unit: string;
    cost: number;
  }>;
  created_at: string;
  updated_at: string;
}

interface ManufacturingOrder {
  id: string;
  mo_number: string;
  recipe_name: string;
  product_name: string;
  quantity_to_produce: number;
  quantity_produced: number;
  branch_name: string;
  status: 'planned' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  estimated_completion: string;
  actual_completion?: string;
  assigned_to?: string;
  notes?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

const statusColors = {
  planned: 'bg-blue-100 text-blue-800',
  in_progress: 'bg-orange-100 text-orange-800',
  completed: 'bg-green-100 text-green-800',
  cancelled: 'bg-red-100 text-red-800',
};

const priorityColors = {
  low: 'bg-gray-100 text-gray-800',
  normal: 'bg-blue-100 text-blue-800',
  high: 'bg-yellow-100 text-yellow-800',
  urgent: 'bg-red-100 text-red-800',
};

export default function ManufacturingPage() {
  const { user } = useAuth();
  const permissions = usePermissions();
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [manufacturingOrders, setManufacturingOrders] = useState<ManufacturingOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [selectedTab, setSelectedTab] = useState<'orders' | 'recipes'>('orders');
  const [showCreateModal, setShowCreateModal] = useState(false);

  // Mock data for demonstration
  const mockRecipes: Recipe[] = [
    {
      id: 'recipe-1',
      name: 'Cappuccino Recipe',
      product_name: 'Cappuccino',
      description: 'Classic cappuccino with perfect foam',
      yield_quantity: 1,
      preparation_time: 4,
      cost_per_unit: 1.25,
      instructions: '1. Grind 18g coffee beans\n2. Extract espresso shot\n3. Steam milk to 150°F\n4. Pour steamed milk over espresso\n5. Top with foam',
      is_active: true,
      ingredients: [
        {
          id: 'ing-1',
          ingredient_name: 'Coffee Beans',
          ingredient_sku: 'ING-CB-001',
          quantity: 0.04,
          unit: 'lb',
          cost: 0.32,
        },
        {
          id: 'ing-2',
          ingredient_name: 'Whole Milk',
          ingredient_sku: 'ING-MLK-001',
          quantity: 0.02,
          unit: 'gallon',
          cost: 0.07,
        },
      ],
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-15T10:30:00Z',
    },
    {
      id: 'recipe-2',
      name: 'Caesar Salad Recipe',
      product_name: 'Caesar Salad',
      description: 'Fresh romaine with homemade caesar dressing',
      yield_quantity: 1,
      preparation_time: 8,
      cost_per_unit: 3.00,
      instructions: '1. Wash and chop romaine lettuce\n2. Prepare caesar dressing\n3. Toss lettuce with dressing\n4. Add croutons and parmesan\n5. Serve immediately',
      is_active: true,
      ingredients: [
        {
          id: 'ing-3',
          ingredient_name: 'Romaine Lettuce',
          ingredient_sku: 'ING-LET-ROM',
          quantity: 1,
          unit: 'head',
          cost: 1.50,
        },
        {
          id: 'ing-4',
          ingredient_name: 'Parmesan Cheese',
          ingredient_sku: 'ING-CHE-PAR',
          quantity: 0.25,
          unit: 'cup',
          cost: 0.75,
        },
        {
          id: 'ing-5',
          ingredient_name: 'Croutons',
          ingredient_sku: 'ING-CRO-001',
          quantity: 0.5,
          unit: 'cup',
          cost: 0.50,
        },
      ],
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-15T10:30:00Z',
    },
  ];

  const mockManufacturingOrders: ManufacturingOrder[] = [
    {
      id: 'mo-1',
      mo_number: 'MO-001-2024',
      recipe_name: 'Cappuccino Recipe',
      product_name: 'Cappuccino',
      quantity_to_produce: 50,
      quantity_produced: 35,
      branch_name: 'Downtown Branch',
      status: 'in_progress',
      priority: 'normal',
      estimated_completion: '2024-01-15T16:00:00Z',
      assigned_to: 'Chef Mike',
      notes: 'Morning rush preparation',
      created_by: 'John Manager',
      created_at: '2024-01-15T08:00:00Z',
      updated_at: '2024-01-15T14:30:00Z',
    },
    {
      id: 'mo-2',
      mo_number: 'MO-002-2024',
      recipe_name: 'Caesar Salad Recipe',
      product_name: 'Caesar Salad',
      quantity_to_produce: 20,
      quantity_produced: 0,
      branch_name: 'Downtown Branch',
      status: 'planned',
      priority: 'high',
      estimated_completion: '2024-01-15T18:00:00Z',
      assigned_to: 'Chef Sarah',
      notes: 'Lunch prep for tomorrow',
      created_by: 'John Manager',
      created_at: '2024-01-15T10:00:00Z',
      updated_at: '2024-01-15T10:00:00Z',
    },
    {
      id: 'mo-3',
      mo_number: 'MO-003-2024',
      recipe_name: 'Cappuccino Recipe',
      product_name: 'Cappuccino',
      quantity_to_produce: 30,
      quantity_produced: 30,
      branch_name: 'Downtown Branch',
      status: 'completed',
      priority: 'normal',
      estimated_completion: '2024-01-14T12:00:00Z',
      actual_completion: '2024-01-14T11:45:00Z',
      assigned_to: 'Chef Mike',
      created_by: 'John Manager',
      created_at: '2024-01-14T09:00:00Z',
      updated_at: '2024-01-14T11:45:00Z',
    },
  ];

  useEffect(() => {
    // In a real app, this would fetch from the API
    setRecipes(mockRecipes);
    setManufacturingOrders(mockManufacturingOrders);
    setLoading(false);
  }, []);

  const filteredManufacturingOrders = manufacturingOrders.filter(mo => {
    const matchesSearch = mo.mo_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         mo.product_name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = !statusFilter || mo.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const filteredRecipes = recipes.filter(recipe => 
    recipe.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    recipe.product_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const updateMOStatus = (moId: string, newStatus: ManufacturingOrder['status']) => {
    setManufacturingOrders(prev => prev.map(mo => 
      mo.id === moId 
        ? { 
            ...mo, 
            status: newStatus,
            actual_completion: newStatus === 'completed' ? new Date().toISOString() : mo.actual_completion,
            updated_at: new Date().toISOString()
          }
        : mo
    ));
  };

  const updateProducedQuantity = (moId: string, quantity: number) => {
    setManufacturingOrders(prev => prev.map(mo => 
      mo.id === moId 
        ? { 
            ...mo, 
            quantity_produced: quantity,
            status: quantity >= mo.quantity_to_produce ? 'completed' : mo.status,
            actual_completion: quantity >= mo.quantity_to_produce ? new Date().toISOString() : mo.actual_completion,
            updated_at: new Date().toISOString()
          }
        : mo
    ));
  };

  const moStats = {
    total: manufacturingOrders.length,
    planned: manufacturingOrders.filter(mo => mo.status === 'planned').length,
    in_progress: manufacturingOrders.filter(mo => mo.status === 'in_progress').length,
    completed: manufacturingOrders.filter(mo => mo.status === 'completed').length,
    efficiency: manufacturingOrders.filter(mo => mo.status === 'completed').length / manufacturingOrders.length * 100,
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <h1 className="text-2xl font-bold">Manufacturing</h1>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Manufacturing</h1>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <Timer className="h-4 w-4 mr-2" />
            Production Schedule
          </Button>
          {permissions.canManageProducts() && (
            <Button size="sm" onClick={() => setShowCreateModal(true)}>
              <Plus className="h-4 w-4 mr-2" />
              {selectedTab === 'orders' ? 'New Manufacturing Order' : 'New Recipe'}
            </Button>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Orders</p>
                <p className="text-2xl font-bold">{moStats.total}</p>
              </div>
              <Package className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">In Progress</p>
                <p className="text-2xl font-bold text-orange-600">{moStats.in_progress}</p>
              </div>
              <Play className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-green-600">{moStats.completed}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Efficiency</p>
                <p className="text-2xl font-bold text-purple-600">{moStats.efficiency.toFixed(0)}%</p>
              </div>
              <ChefHat className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setSelectedTab('orders')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              selectedTab === 'orders'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Manufacturing Orders
          </button>
          <button
            onClick={() => setSelectedTab('recipes')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              selectedTab === 'recipes'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Recipes & BOM
          </button>
        </nav>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder={selectedTab === 'orders' ? "Search manufacturing orders..." : "Search recipes..."}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                leftIcon={<Search size={20} />}
              />
            </div>
            {selectedTab === 'orders' && (
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="">All Statuses</option>
                <option value="planned">Planned</option>
                <option value="in_progress">In Progress</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Content */}
      {selectedTab === 'orders' ? (
        <div className="space-y-4">
          {filteredManufacturingOrders.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No manufacturing orders found</h3>
                <p className="text-gray-600">Create your first manufacturing order to get started</p>
              </CardContent>
            </Card>
          ) : (
            filteredManufacturingOrders.map((mo) => {
              const completionRate = (mo.quantity_produced / mo.quantity_to_produce) * 100;
              const isOverdue = new Date(mo.estimated_completion) < new Date() && mo.status !== 'completed';
              
              return (
                <Card key={mo.id} className={isOverdue ? 'border-red-300 bg-red-50' : ''}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-4">
                        <div>
                          <h3 className="text-lg font-semibold flex items-center">
                            {mo.mo_number}
                            {isOverdue && <AlertTriangle className="h-4 w-4 text-red-500 ml-2" />}
                          </h3>
                          <p className="text-sm text-gray-500">
                            {mo.product_name} • {formatDateTime(mo.created_at)}
                          </p>
                        </div>
                        <div className="flex space-x-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusColors[mo.status]}`}>
                            {mo.status.replace('_', ' ').toUpperCase()}
                          </span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${priorityColors[mo.priority]}`}>
                            {mo.priority.toUpperCase()}
                          </span>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold">
                          {mo.quantity_produced}/{mo.quantity_to_produce}
                        </div>
                        <div className="text-sm text-gray-500">units</div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Assigned To</label>
                        <p className="text-sm">{mo.assigned_to || 'Unassigned'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Estimated Completion</label>
                        <p className="text-sm">
                          {new Date(mo.estimated_completion).toLocaleString()}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Progress</label>
                        <div className="flex items-center space-x-2">
                          <div className="flex-1 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-green-600 h-2 rounded-full" 
                              style={{ width: `${completionRate}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-medium">{completionRate.toFixed(0)}%</span>
                        </div>
                      </div>
                    </div>

                    {mo.notes && (
                      <div className="mb-4">
                        <label className="text-sm font-medium text-gray-500">Notes</label>
                        <p className="text-sm text-gray-700">{mo.notes}</p>
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex justify-between items-center">
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-1" />
                          View Recipe
                        </Button>
                        {mo.status === 'in_progress' && (
                          <div className="flex items-center space-x-2">
                            <Input
                              type="number"
                              placeholder="Qty"
                              className="w-20"
                              max={mo.quantity_to_produce}
                              min={0}
                              defaultValue={mo.quantity_produced}
                              onBlur={(e) => updateProducedQuantity(mo.id, parseInt(e.target.value) || 0)}
                            />
                            <span className="text-sm text-gray-500">produced</span>
                          </div>
                        )}
                      </div>
                      
                      <div className="flex space-x-2">
                        {mo.status === 'planned' && (
                          <Button
                            size="sm"
                            onClick={() => updateMOStatus(mo.id, 'in_progress')}
                          >
                            <Play className="h-4 w-4 mr-1" />
                            Start Production
                          </Button>
                        )}
                        {mo.status === 'in_progress' && (
                          <Button
                            size="sm"
                            onClick={() => updateMOStatus(mo.id, 'completed')}
                          >
                            <CheckCircle className="h-4 w-4 mr-1" />
                            Complete
                          </Button>
                        )}
                        {mo.status !== 'completed' && mo.status !== 'cancelled' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateMOStatus(mo.id, 'cancelled')}
                          >
                            Cancel
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredRecipes.map((recipe) => (
            <Card key={recipe.id}>
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold">{recipe.name}</h3>
                    <p className="text-sm text-gray-500">{recipe.product_name}</p>
                    <p className="text-sm text-gray-600 mt-1">{recipe.description}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">
                      {formatCurrency(recipe.cost_per_unit)}/unit
                    </div>
                    <div className="text-xs text-gray-500">
                      {recipe.preparation_time} min prep
                    </div>
                  </div>
                </div>

                <div className="mb-4">
                  <label className="text-sm font-medium text-gray-500 mb-2 block">Ingredients</label>
                  <div className="space-y-1">
                    {recipe.ingredients.map((ingredient) => (
                      <div key={ingredient.id} className="flex justify-between text-sm">
                        <span>
                          {ingredient.quantity} {ingredient.unit} {ingredient.ingredient_name}
                        </span>
                        <span>{formatCurrency(ingredient.cost)}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="mb-4">
                  <label className="text-sm font-medium text-gray-500 mb-2 block">Instructions</label>
                  <div className="text-sm text-gray-700 whitespace-pre-line max-h-32 overflow-y-auto">
                    {recipe.instructions}
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <div className="text-sm text-gray-500">
                    Yield: {recipe.yield_quantity} unit(s)
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm">
                      <Edit className="h-3 w-3 mr-1" />
                      Edit
                    </Button>
                    <Button size="sm">
                      <Plus className="h-3 w-3 mr-1" />
                      Create MO
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
