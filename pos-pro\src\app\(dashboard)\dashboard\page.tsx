'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/lib/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  DollarSign, 
  ShoppingCart, 
  Clock, 
  Package, 
  Users, 
  TrendingUp,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { formatCurrency, formatNumber } from '@/utils';

interface DashboardStats {
  daily_sales: number;
  daily_orders: number;
  pending_orders: number;
  low_stock_items: number;
  active_tables: number;
  sales_growth: number;
  sales_by_type: Record<string, number>;
  top_products: Array<{
    product_id: string;
    name: string;
    sku: string;
    total_quantity: number;
    total_revenue: number;
  }>;
  hourly_sales: Array<{
    hour: number;
    sales: number;
    orders: number;
  }>;
}

export default function DashboardPage() {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchDashboardStats();
  }, [user]);

  const fetchDashboardStats = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      
      if (user?.branch_id) {
        params.append('branch_id', user.branch_id);
      }
      
      const today = new Date().toISOString().split('T')[0];
      params.append('start_date', today);
      params.append('end_date', today);

      const response = await fetch(`/api/dashboard/stats?${params}`);
      const data = await response.json();

      if (data.success) {
        setStats(data.data);
      } else {
        setError(data.error || 'Failed to fetch dashboard stats');
      }
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      setError('Failed to fetch dashboard stats');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Dashboard</h3>
        <p className="text-gray-600 mb-4">{error}</p>
        <Button onClick={fetchDashboardStats}>Try Again</Button>
      </div>
    );
  }

  const statCards = [
    {
      title: 'Daily Sales',
      value: formatCurrency(stats?.daily_sales || 0),
      change: stats?.sales_growth || 0,
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      title: 'Daily Orders',
      value: formatNumber(stats?.daily_orders || 0, 0),
      icon: ShoppingCart,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      title: 'Pending Orders',
      value: formatNumber(stats?.pending_orders || 0, 0),
      icon: Clock,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
    },
    {
      title: 'Low Stock Items',
      value: formatNumber(stats?.low_stock_items || 0, 0),
      icon: Package,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">
          Welcome back, {user?.name}!
        </h1>
        <p className="text-gray-600">
          Here's what's happening at your restaurant today.
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-full ${stat.bgColor}`}>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
              {stat.change !== undefined && (
                <div className="flex items-center text-sm">
                  <TrendingUp className={`h-4 w-4 mr-1 ${
                    stat.change >= 0 ? 'text-green-500' : 'text-red-500'
                  }`} />
                  <span className={stat.change >= 0 ? 'text-green-600' : 'text-red-600'}>
                    {stat.change >= 0 ? '+' : ''}{stat.change.toFixed(1)}%
                  </span>
                  <span className="text-gray-500 ml-1">from yesterday</span>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Sales by Order Type */}
      {stats?.sales_by_type && Object.keys(stats.sales_by_type).length > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Sales by Order Type</CardTitle>
              <CardDescription>Today's revenue breakdown</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {Object.entries(stats.sales_by_type).map(([type, amount]) => (
                  <div key={type} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-blue-500 mr-3"></div>
                      <span className="text-sm font-medium capitalize">
                        {type.replace('_', ' ')}
                      </span>
                    </div>
                    <span className="text-sm font-semibold">
                      {formatCurrency(amount)}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Top Products */}
          <Card>
            <CardHeader>
              <CardTitle>Top Selling Products</CardTitle>
              <CardDescription>Most popular items today</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {stats?.top_products?.slice(0, 5).map((product, index) => (
                  <div key={product.product_id} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center mr-3">
                        <span className="text-xs font-medium text-gray-600">
                          {index + 1}
                        </span>
                      </div>
                      <div>
                        <div className="text-sm font-medium">{product.name}</div>
                        <div className="text-xs text-gray-500">{product.sku}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-semibold">
                        {product.total_quantity} sold
                      </div>
                      <div className="text-xs text-gray-500">
                        {formatCurrency(product.total_revenue)}
                      </div>
                    </div>
                  </div>
                )) || (
                  <div className="text-center py-4 text-gray-500">
                    No sales data available
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common tasks and shortcuts</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" className="h-20 flex-col" asChild>
              <a href="/dashboard/pos">
                <ShoppingCart className="h-6 w-6 mb-2" />
                New Order
              </a>
            </Button>
            <Button variant="outline" className="h-20 flex-col" asChild>
              <a href="/dashboard/products">
                <Package className="h-6 w-6 mb-2" />
                Manage Products
              </a>
            </Button>
            <Button variant="outline" className="h-20 flex-col" asChild>
              <a href="/dashboard/inventory">
                <AlertTriangle className="h-6 w-6 mb-2" />
                Check Inventory
              </a>
            </Button>
            <Button variant="outline" className="h-20 flex-col" asChild>
              <a href="/dashboard/orders">
                <CheckCircle className="h-6 w-6 mb-2" />
                View Orders
              </a>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
