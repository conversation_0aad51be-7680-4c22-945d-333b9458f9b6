'use client';

import { useState, useEffect } from 'react';
import { useAuth, usePermissions } from '@/lib/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  ShoppingCart, 
  Plus, 
  Edit, 
  Eye,
  Truck,
  Clock,
  CheckCircle,
  XCircle,
  Search,
  Filter,
  Download,
  FileText
} from 'lucide-react';
import { formatCurrency, formatDateTime } from '@/utils';

interface PurchaseOrder {
  id: string;
  po_number: string;
  supplier_name: string;
  supplier_contact: string;
  branch_name: string;
  status: 'draft' | 'sent' | 'confirmed' | 'partial_received' | 'received' | 'cancelled';
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  expected_delivery_date?: string;
  actual_delivery_date?: string;
  notes?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  items: Array<{
    id: string;
    product_name: string;
    product_sku: string;
    quantity: number;
    unit_cost: number;
    total_cost: number;
    received_quantity: number;
  }>;
}

interface Supplier {
  id: string;
  name: string;
  contact_person: string;
  email: string;
  phone: string;
  rating: number;
  payment_terms: string;
}

const statusColors = {
  draft: 'bg-gray-100 text-gray-800',
  sent: 'bg-blue-100 text-blue-800',
  confirmed: 'bg-yellow-100 text-yellow-800',
  partial_received: 'bg-orange-100 text-orange-800',
  received: 'bg-green-100 text-green-800',
  cancelled: 'bg-red-100 text-red-800',
};

const statusIcons = {
  draft: FileText,
  sent: Clock,
  confirmed: CheckCircle,
  partial_received: Truck,
  received: CheckCircle,
  cancelled: XCircle,
};

export default function PurchasingPage() {
  const { user } = useAuth();
  const permissions = usePermissions();
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [selectedTab, setSelectedTab] = useState<'orders' | 'suppliers'>('orders');
  const [showCreateModal, setShowCreateModal] = useState(false);

  // Mock data for demonstration
  const mockPurchaseOrders: PurchaseOrder[] = [
    {
      id: 'po-1',
      po_number: 'PO-001-2024',
      supplier_name: 'Coffee Supreme',
      supplier_contact: 'Mike Coffee',
      branch_name: 'Downtown Branch',
      status: 'confirmed',
      subtotal: 480.00,
      tax_amount: 48.00,
      total_amount: 528.00,
      expected_delivery_date: '2024-01-18T00:00:00Z',
      notes: 'Weekly coffee bean delivery',
      created_by: 'John Manager',
      created_at: '2024-01-15T09:00:00Z',
      updated_at: '2024-01-15T10:30:00Z',
      items: [
        {
          id: 'poi-1',
          product_name: 'Coffee Beans - Colombian',
          product_sku: 'ING-CB-COL',
          quantity: 50,
          unit_cost: 8.00,
          total_cost: 400.00,
          received_quantity: 0,
        },
        {
          id: 'poi-2',
          product_name: 'Coffee Beans - Ethiopian',
          product_sku: 'ING-CB-ETH',
          quantity: 10,
          unit_cost: 8.00,
          total_cost: 80.00,
          received_quantity: 0,
        },
      ],
    },
    {
      id: 'po-2',
      po_number: 'PO-002-2024',
      supplier_name: 'Fresh Dairy Co',
      supplier_contact: 'Sarah Milk',
      branch_name: 'Downtown Branch',
      status: 'partial_received',
      subtotal: 175.00,
      tax_amount: 17.50,
      total_amount: 192.50,
      expected_delivery_date: '2024-01-16T00:00:00Z',
      actual_delivery_date: '2024-01-16T08:30:00Z',
      notes: 'Daily dairy delivery',
      created_by: 'John Manager',
      created_at: '2024-01-14T16:00:00Z',
      updated_at: '2024-01-16T08:30:00Z',
      items: [
        {
          id: 'poi-3',
          product_name: 'Whole Milk',
          product_sku: 'ING-MLK-WHL',
          quantity: 20,
          unit_cost: 3.50,
          total_cost: 70.00,
          received_quantity: 20,
        },
        {
          id: 'poi-4',
          product_name: 'Heavy Cream',
          product_sku: 'ING-CRM-HVY',
          quantity: 15,
          unit_cost: 7.00,
          total_cost: 105.00,
          received_quantity: 10,
        },
      ],
    },
    {
      id: 'po-3',
      po_number: 'PO-003-2024',
      supplier_name: 'Premium Meats',
      supplier_contact: 'Tom Butcher',
      branch_name: 'Downtown Branch',
      status: 'draft',
      subtotal: 320.00,
      tax_amount: 32.00,
      total_amount: 352.00,
      expected_delivery_date: '2024-01-20T00:00:00Z',
      notes: 'Weekly meat order',
      created_by: 'John Manager',
      created_at: '2024-01-15T14:00:00Z',
      updated_at: '2024-01-15T14:00:00Z',
      items: [
        {
          id: 'poi-5',
          product_name: 'Chicken Breast',
          product_sku: 'ING-CHB-001',
          quantity: 40,
          unit_cost: 6.50,
          total_cost: 260.00,
          received_quantity: 0,
        },
        {
          id: 'poi-6',
          product_name: 'Ground Beef',
          product_sku: 'ING-BEF-GRD',
          quantity: 10,
          unit_cost: 6.00,
          total_cost: 60.00,
          received_quantity: 0,
        },
      ],
    },
  ];

  const mockSuppliers: Supplier[] = [
    {
      id: 'sup-1',
      name: 'Coffee Supreme',
      contact_person: 'Mike Coffee',
      email: '<EMAIL>',
      phone: '******-0160',
      rating: 4.8,
      payment_terms: '30 days',
    },
    {
      id: 'sup-2',
      name: 'Fresh Dairy Co',
      contact_person: 'Sarah Milk',
      email: '<EMAIL>',
      phone: '******-0161',
      rating: 4.5,
    },
    {
      id: 'sup-3',
      name: 'Premium Meats',
      contact_person: 'Tom Butcher',
      email: '<EMAIL>',
      phone: '******-0162',
      rating: 4.9,
      payment_terms: '15 days',
    },
  ];

  useEffect(() => {
    // In a real app, this would fetch from the API
    setPurchaseOrders(mockPurchaseOrders);
    setSuppliers(mockSuppliers);
    setLoading(false);
  }, []);

  const filteredPurchaseOrders = purchaseOrders.filter(po => {
    const matchesSearch = po.po_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         po.supplier_name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = !statusFilter || po.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const filteredSuppliers = suppliers.filter(supplier => 
    supplier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    supplier.contact_person.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const updatePOStatus = (poId: string, newStatus: PurchaseOrder['status']) => {
    setPurchaseOrders(prev => prev.map(po => 
      po.id === poId 
        ? { 
            ...po, 
            status: newStatus,
            actual_delivery_date: newStatus === 'received' ? new Date().toISOString() : po.actual_delivery_date,
            updated_at: new Date().toISOString()
          }
        : po
    ));
  };

  const poStats = {
    total: purchaseOrders.length,
    draft: purchaseOrders.filter(po => po.status === 'draft').length,
    pending: purchaseOrders.filter(po => ['sent', 'confirmed'].includes(po.status)).length,
    received: purchaseOrders.filter(po => po.status === 'received').length,
    totalValue: purchaseOrders.reduce((sum, po) => sum + po.total_amount, 0),
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <h1 className="text-2xl font-bold">Purchasing Management</h1>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Purchasing Management</h1>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          {permissions.canManageInventory() && (
            <Button size="sm" onClick={() => setShowCreateModal(true)}>
              <Plus className="h-4 w-4 mr-2" />
              New Purchase Order
            </Button>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Orders</p>
                <p className="text-2xl font-bold">{poStats.total}</p>
              </div>
              <ShoppingCart className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-yellow-600">{poStats.pending}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Received</p>
                <p className="text-2xl font-bold text-green-600">{poStats.received}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Value</p>
                <p className="text-2xl font-bold text-purple-600">
                  {formatCurrency(poStats.totalValue)}
                </p>
              </div>
              <Truck className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setSelectedTab('orders')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              selectedTab === 'orders'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Purchase Orders
          </button>
          <button
            onClick={() => setSelectedTab('suppliers')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              selectedTab === 'suppliers'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Suppliers
          </button>
        </nav>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder={selectedTab === 'orders' ? "Search purchase orders..." : "Search suppliers..."}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                leftIcon={<Search size={20} />}
              />
            </div>
            {selectedTab === 'orders' && (
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="">All Statuses</option>
                <option value="draft">Draft</option>
                <option value="sent">Sent</option>
                <option value="confirmed">Confirmed</option>
                <option value="partial_received">Partial Received</option>
                <option value="received">Received</option>
                <option value="cancelled">Cancelled</option>
              </select>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Content */}
      {selectedTab === 'orders' ? (
        <div className="space-y-4">
          {filteredPurchaseOrders.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <ShoppingCart className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No purchase orders found</h3>
                <p className="text-gray-600">Create your first purchase order to get started</p>
              </CardContent>
            </Card>
          ) : (
            filteredPurchaseOrders.map((po) => {
              const StatusIcon = statusIcons[po.status];
              const completionRate = po.items.reduce((sum, item) => sum + item.received_quantity, 0) / 
                                   po.items.reduce((sum, item) => sum + item.quantity, 0) * 100;
              
              return (
                <Card key={po.id}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-4">
                        <div>
                          <h3 className="text-lg font-semibold">{po.po_number}</h3>
                          <p className="text-sm text-gray-500">
                            {po.supplier_name} • {formatDateTime(po.created_at)}
                          </p>
                        </div>
                        <div className={`px-2 py-1 rounded-full text-xs font-medium flex items-center ${statusColors[po.status]}`}>
                          <StatusIcon className="h-3 w-3 mr-1" />
                          {po.status.replace('_', ' ').toUpperCase()}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold">{formatCurrency(po.total_amount)}</div>
                        <div className="text-sm text-gray-500">
                          {po.items.length} items
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Contact</label>
                        <p className="text-sm">{po.supplier_contact}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Expected Delivery</label>
                        <p className="text-sm">
                          {po.expected_delivery_date 
                            ? new Date(po.expected_delivery_date).toLocaleDateString()
                            : 'Not set'
                          }
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Completion</label>
                        <div className="flex items-center space-x-2">
                          <div className="flex-1 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-green-600 h-2 rounded-full" 
                              style={{ width: `${completionRate}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-medium">{completionRate.toFixed(0)}%</span>
                        </div>
                      </div>
                    </div>

                    {/* Items Preview */}
                    <div className="mb-4">
                      <label className="text-sm font-medium text-gray-500 mb-2 block">Items</label>
                      <div className="space-y-1">
                        {po.items.slice(0, 2).map((item) => (
                          <div key={item.id} className="flex justify-between text-sm">
                            <span>
                              {item.quantity}x {item.product_name}
                              {item.received_quantity > 0 && (
                                <span className="text-green-600 ml-2">
                                  ({item.received_quantity} received)
                                </span>
                              )}
                            </span>
                            <span>{formatCurrency(item.total_cost)}</span>
                          </div>
                        ))}
                        {po.items.length > 2 && (
                          <div className="text-sm text-gray-500">
                            +{po.items.length - 2} more items
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex justify-between items-center">
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        {permissions.canManageInventory() && po.status === 'draft' && (
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4 mr-1" />
                            Edit
                          </Button>
                        )}
                      </div>
                      
                      <div className="flex space-x-2">
                        {po.status === 'draft' && permissions.canManageInventory() && (
                          <Button
                            size="sm"
                            onClick={() => updatePOStatus(po.id, 'sent')}
                          >
                            Send to Supplier
                          </Button>
                        )}
                        {po.status === 'sent' && (
                          <Button
                            size="sm"
                            onClick={() => updatePOStatus(po.id, 'confirmed')}
                          >
                            Mark Confirmed
                          </Button>
                        )}
                        {['confirmed', 'partial_received'].includes(po.status) && (
                          <Button
                            size="sm"
                            onClick={() => updatePOStatus(po.id, 'received')}
                          >
                            Mark Received
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredSuppliers.map((supplier) => (
            <Card key={supplier.id}>
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold">{supplier.name}</h3>
                    <p className="text-sm text-gray-500">{supplier.contact_person}</p>
                  </div>
                  <div className="flex items-center">
                    <span className="text-yellow-500 mr-1">★</span>
                    <span className="text-sm font-medium">{supplier.rating}</span>
                  </div>
                </div>
                
                <div className="space-y-2 mb-4">
                  <div className="text-sm">
                    <span className="text-gray-500">Email:</span> {supplier.email}
                  </div>
                  <div className="text-sm">
                    <span className="text-gray-500">Phone:</span> {supplier.phone}
                  </div>
                  {supplier.payment_terms && (
                    <div className="text-sm">
                      <span className="text-gray-500">Payment Terms:</span> {supplier.payment_terms}
                    </div>
                  )}
                </div>
                
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    <Edit className="h-3 w-3 mr-1" />
                    Edit
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    <Plus className="h-3 w-3 mr-1" />
                    New PO
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Create PO Modal Placeholder */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <CardTitle>Create Purchase Order</CardTitle>
              <CardDescription>Create a new purchase order for inventory</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <select className="px-3 py-2 border border-gray-300 rounded-md">
                    <option value="">Select Supplier</option>
                    {suppliers.map(supplier => (
                      <option key={supplier.id} value={supplier.id}>{supplier.name}</option>
                    ))}
                  </select>
                  <Input label="Expected Delivery Date" type="date" />
                </div>
                <Input label="Notes" placeholder="Any special instructions" />
                
                <div className="border-t pt-4">
                  <h4 className="font-medium mb-3">Items</h4>
                  <div className="space-y-2">
                    <div className="grid grid-cols-5 gap-2 text-sm font-medium text-gray-600">
                      <span>Product</span>
                      <span>Quantity</span>
                      <span>Unit Cost</span>
                      <span>Total</span>
                      <span>Action</span>
                    </div>
                    <div className="grid grid-cols-5 gap-2">
                      <select className="px-2 py-1 border border-gray-300 rounded text-sm">
                        <option>Select Product</option>
                      </select>
                      <Input type="number" placeholder="0" />
                      <Input type="number" placeholder="0.00" />
                      <div className="py-1 text-sm">$0.00</div>
                      <Button variant="outline" size="sm">Remove</Button>
                    </div>
                  </div>
                  <Button variant="outline" size="sm" className="mt-2">
                    <Plus className="h-3 w-3 mr-1" />
                    Add Item
                  </Button>
                </div>
              </div>
              <div className="flex justify-end space-x-2 mt-6">
                <Button variant="outline" onClick={() => setShowCreateModal(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setShowCreateModal(false)}>
                  Create Purchase Order
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
