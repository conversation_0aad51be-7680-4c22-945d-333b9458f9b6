'use client';

import { useState } from 'react';
import { useAuth, usePermissions } from '@/lib/auth-context';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { Button } from '@/components/ui/button';
import {
  ShoppingCart,
  BarChart3,
  Package,
  Users,
  Settings,
  Menu,
  X,
  LogOut,
  Home,
  ChefHat,
  MapPin,
  TrendingUp,
  UserCircle,
  Warehouse,
  Factory,
  ShoppingBag,
  Truck,
  ClipboardList,
} from 'lucide-react';

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  permission?: () => boolean;
}

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user, signOut } = useAuth();
  const permissions = usePermissions();

  const navigation: NavigationItem[] = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: Home,
    },
    {
      name: 'Point of Sale',
      href: '/dashboard/pos',
      icon: ShoppingCart,
      permission: permissions.canAccessPOS,
    },
    {
      name: 'Orders',
      href: '/dashboard/orders',
      icon: ClipboardList,
      permission: permissions.canManageOrders,
    },
    {
      name: 'Kitchen Display',
      href: '/dashboard/kitchen',
      icon: ChefHat,
      permission: permissions.canManageKitchen,
    },
    {
      name: 'Products',
      href: '/dashboard/products',
      icon: Package,
      permission: permissions.canManageProducts,
    },
    {
      name: 'Inventory',
      href: '/dashboard/inventory',
      icon: Warehouse,
      permission: permissions.canManageInventory,
    },
    {
      name: 'Manufacturing',
      href: '/dashboard/manufacturing',
      icon: Factory,
      permission: permissions.canManageProducts,
    },
    {
      name: 'Purchasing',
      href: '/dashboard/purchasing',
      icon: ShoppingBag,
      permission: permissions.canManageInventory,
    },
    {
      name: 'Delivery Tracking',
      href: '/dashboard/delivery',
      icon: Truck,
      permission: permissions.canManageOrders,
    },
    {
      name: 'Customers',
      href: '/dashboard/customers',
      icon: Users,
      permission: permissions.canManageOrders,
    },
    {
      name: 'Delivery Tracking',
      href: '/dashboard/delivery',
      icon: MapPin,
      permission: permissions.canManageDelivery,
    },
    {
      name: 'Manufacturing',
      href: '/dashboard/manufacturing',
      icon: TrendingUp,
      permission: permissions.canManageProducts,
    },
    {
      name: 'Analytics',
      href: '/dashboard/analytics',
      icon: BarChart3,
      permission: permissions.canViewReports,
    },
    {
      name: 'Users',
      href: '/dashboard/users',
      icon: Users,
      permission: permissions.canManageUsers,
    },
    {
      name: 'Settings',
      href: '/dashboard/settings',
      icon: Settings,
      permission: permissions.canManageUsers,
    },
  ];

  const filteredNavigation = navigation.filter(
    item => !item.permission || item.permission()
  );

  const handleSignOut = async () => {
    await signOut();
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        {/* Mobile sidebar */}
        <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
          <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
          <div className="fixed inset-y-0 left-0 flex w-64 flex-col bg-white">
            <div className="flex h-16 items-center justify-between px-4 border-b">
              <div className="flex items-center">
                <ShoppingCart className="h-8 w-8 text-blue-600 mr-2" />
                <span className="text-xl font-bold text-gray-900">POS Pro</span>
              </div>
              <button
                onClick={() => setSidebarOpen(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            <nav className="flex-1 px-4 py-4 space-y-1">
              {filteredNavigation.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900"
                  onClick={() => setSidebarOpen(false)}
                >
                  <item.icon className="mr-3 h-5 w-5" />
                  {item.name}
                </a>
              ))}
            </nav>
          </div>
        </div>

        {/* Desktop sidebar */}
        <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
          <div className="flex flex-col flex-grow bg-white border-r border-gray-200">
            <div className="flex h-16 items-center px-4 border-b">
              <ShoppingCart className="h-8 w-8 text-blue-600 mr-2" />
              <span className="text-xl font-bold text-gray-900">POS Pro</span>
            </div>
            <nav className="flex-1 px-4 py-4 space-y-1">
              {filteredNavigation.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900"
                >
                  <item.icon className="mr-3 h-5 w-5" />
                  {item.name}
                </a>
              ))}
            </nav>
          </div>
        </div>

        {/* Main content */}
        <div className="lg:pl-64">
          {/* Top navigation */}
          <div className="sticky top-0 z-40 bg-white border-b border-gray-200">
            <div className="flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8">
              <button
                onClick={() => setSidebarOpen(true)}
                className="text-gray-500 hover:text-gray-600 lg:hidden"
              >
                <Menu className="h-6 w-6" />
              </button>

              <div className="flex items-center space-x-4">
                {/* User info */}
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    <UserCircle className="h-8 w-8 text-gray-400" />
                    <div className="hidden sm:block">
                      <div className="text-sm font-medium text-gray-900">{user?.name}</div>
                      <div className="text-xs text-gray-500 capitalize">{user?.role.replace('_', ' ')}</div>
                    </div>
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleSignOut}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    Sign Out
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Page content */}
          <main className="flex-1">
            <div className="py-6">
              <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                {children}
              </div>
            </div>
          </main>
        </div>
      </div>
    </ProtectedRoute>
  );
}
