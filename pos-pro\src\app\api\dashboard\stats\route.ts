import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// GET /api/dashboard/stats - Get dashboard statistics
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const branchId = searchParams.get('branch_id');
    const startDate = searchParams.get('start_date') || new Date().toISOString().split('T')[0];
    const endDate = searchParams.get('end_date') || new Date().toISOString().split('T')[0];

    const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey);

    // Build base queries with branch filter if provided
    const buildQuery = (table: string) => {
      let query = supabase.from(table);
      if (branchId) {
        query = query.eq('branch_id', branchId);
      }
      return query;
    };

    // Get daily sales
    const { data: dailySales, error: salesError } = await buildQuery('orders')
      .select('total_amount')
      .eq('status', 'delivered')
      .gte('created_at', `${startDate}T00:00:00`)
      .lte('created_at', `${endDate}T23:59:59`);

    if (salesError) {
      return NextResponse.json(
        { error: salesError.message },
        { status: 400 }
      );
    }

    const dailySalesAmount = dailySales?.reduce((sum, order) => sum + order.total_amount, 0) || 0;

    // Get daily orders count
    const { count: dailyOrdersCount } = await buildQuery('orders')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', `${startDate}T00:00:00`)
      .lte('created_at', `${endDate}T23:59:59`);

    // Get pending orders count
    const { count: pendingOrdersCount } = await buildQuery('orders')
      .select('*', { count: 'exact', head: true })
      .in('status', ['pending', 'confirmed', 'preparing']);

    // Get low stock items count
    let lowStockQuery = supabase
      .from('low_stock_items')
      .select('*', { count: 'exact', head: true });
    
    if (branchId) {
      lowStockQuery = lowStockQuery.eq('branch_id', branchId);
    }

    const { count: lowStockCount } = await lowStockQuery;

    // Get active tables count (for dine-in branches)
    const { count: activeTablesCount } = await buildQuery('tables')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'occupied');

    // Calculate sales growth (compare with previous period)
    const previousStartDate = new Date(startDate);
    previousStartDate.setDate(previousStartDate.getDate() - 1);
    const previousEndDate = new Date(endDate);
    previousEndDate.setDate(previousEndDate.getDate() - 1);

    const { data: previousSales } = await buildQuery('orders')
      .select('total_amount')
      .eq('status', 'delivered')
      .gte('created_at', previousStartDate.toISOString().split('T')[0] + 'T00:00:00')
      .lte('created_at', previousEndDate.toISOString().split('T')[0] + 'T23:59:59');

    const previousSalesAmount = previousSales?.reduce((sum, order) => sum + order.total_amount, 0) || 0;
    const salesGrowth = previousSalesAmount > 0 
      ? ((dailySalesAmount - previousSalesAmount) / previousSalesAmount) * 100 
      : 0;

    // Get sales by order type
    const { data: salesByType } = await buildQuery('orders')
      .select('order_type, total_amount')
      .eq('status', 'delivered')
      .gte('created_at', `${startDate}T00:00:00`)
      .lte('created_at', `${endDate}T23:59:59`);

    const salesByOrderType = salesByType?.reduce((acc, order) => {
      acc[order.order_type] = (acc[order.order_type] || 0) + order.total_amount;
      return acc;
    }, {} as Record<string, number>) || {};

    // Get top selling products
    const { data: topProducts } = await supabase
      .from('order_items')
      .select(`
        product_id,
        quantity,
        total_price,
        products (
          name,
          sku
        ),
        orders!inner (
          status,
          branch_id,
          created_at
        )
      `)
      .eq('orders.status', 'delivered')
      .gte('orders.created_at', `${startDate}T00:00:00`)
      .lte('orders.created_at', `${endDate}T23:59:59`)
      .then(({ data, error }) => {
        if (error) return { data: null, error };
        
        // Filter by branch if specified
        const filteredData = branchId 
          ? data?.filter(item => item.orders?.branch_id === branchId)
          : data;

        // Group by product and calculate totals
        const productStats = filteredData?.reduce((acc, item) => {
          const productId = item.product_id;
          if (!acc[productId]) {
            acc[productId] = {
              product_id: productId,
              name: item.products?.name || 'Unknown',
              sku: item.products?.sku || '',
              total_quantity: 0,
              total_revenue: 0,
            };
          }
          acc[productId].total_quantity += item.quantity;
          acc[productId].total_revenue += item.total_price;
          return acc;
        }, {} as Record<string, any>) || {};

        // Convert to array and sort by quantity
        return {
          data: Object.values(productStats)
            .sort((a: any, b: any) => b.total_quantity - a.total_quantity)
            .slice(0, 10),
          error: null
        };
      });

    if (topProducts.error) {
      return NextResponse.json(
        { error: topProducts.error.message },
        { status: 400 }
      );
    }

    // Get hourly sales for the day
    const { data: hourlySales } = await buildQuery('orders')
      .select('created_at, total_amount')
      .eq('status', 'delivered')
      .gte('created_at', `${startDate}T00:00:00`)
      .lte('created_at', `${startDate}T23:59:59`);

    const hourlyData = Array.from({ length: 24 }, (_, hour) => ({
      hour,
      sales: 0,
      orders: 0,
    }));

    hourlySales?.forEach(order => {
      const hour = new Date(order.created_at).getHours();
      hourlyData[hour].sales += order.total_amount;
      hourlyData[hour].orders += 1;
    });

    const stats = {
      daily_sales: dailySalesAmount,
      daily_orders: dailyOrdersCount || 0,
      pending_orders: pendingOrdersCount || 0,
      low_stock_items: lowStockCount || 0,
      active_tables: activeTablesCount || 0,
      sales_growth: salesGrowth,
      sales_by_type: salesByOrderType,
      top_products: topProducts.data,
      hourly_sales: hourlyData,
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });

  } catch (error) {
    console.error('Get dashboard stats error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
