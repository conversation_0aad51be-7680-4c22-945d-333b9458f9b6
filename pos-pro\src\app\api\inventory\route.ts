import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// GET /api/inventory - Fetch inventory with filters
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const branchId = searchParams.get('branch_id');
    const lowStock = searchParams.get('low_stock') === 'true';
    const productId = searchParams.get('product_id');

    const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey);

    // Build query
    let query = supabase
      .from('inventory')
      .select(`
        *,
        products (
          id,
          name,
          sku,
          unit,
          categories (
            id,
            name
          )
        ),
        branches (
          id,
          name
        )
      `)
      .order('last_updated', { ascending: false });

    // Apply filters
    if (branchId) {
      query = query.eq('branch_id', branchId);
    }
    if (productId) {
      query = query.eq('product_id', productId);
    }

    const { data: inventory, error } = await query;

    if (error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    // Filter low stock items if requested
    let filteredInventory = inventory;
    if (lowStock) {
      filteredInventory = inventory?.filter(item => item.quantity <= item.reorder_level) || [];
    }

    return NextResponse.json({
      success: true,
      data: filteredInventory,
    });

  } catch (error) {
    console.error('Get inventory error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/inventory - Update inventory levels
export async function POST(request: NextRequest) {
  try {
    const inventoryData = await request.json();
    const {
      product_id,
      branch_id,
      quantity_change,
      movement_type, // 'in', 'out', 'adjustment'
      reference_id,
      reference_type,
      notes,
      created_by,
    } = inventoryData;

    // Validation
    if (!product_id || !branch_id || quantity_change === undefined || !movement_type || !created_by) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey);

    // Get current inventory
    const { data: currentInventory, error: inventoryError } = await supabase
      .from('inventory')
      .select('*')
      .eq('product_id', product_id)
      .eq('branch_id', branch_id)
      .single();

    if (inventoryError && inventoryError.code !== 'PGRST116') {
      return NextResponse.json(
        { error: inventoryError.message },
        { status: 400 }
      );
    }

    let newQuantity: number;
    let updatedInventory;

    if (currentInventory) {
      // Update existing inventory
      newQuantity = currentInventory.quantity + quantity_change;
      
      if (newQuantity < 0) {
        return NextResponse.json(
          { error: 'Insufficient inventory' },
          { status: 400 }
        );
      }

      const { data, error: updateError } = await supabase
        .from('inventory')
        .update({
          quantity: newQuantity,
          last_updated: new Date().toISOString(),
        })
        .eq('product_id', product_id)
        .eq('branch_id', branch_id)
        .select(`
          *,
          products (
            id,
            name,
            sku,
            unit
          ),
          branches (
            id,
            name
          )
        `)
        .single();

      if (updateError) {
        return NextResponse.json(
          { error: updateError.message },
          { status: 400 }
        );
      }

      updatedInventory = data;
    } else {
      // Create new inventory record
      if (quantity_change < 0) {
        return NextResponse.json(
          { error: 'Cannot create inventory with negative quantity' },
          { status: 400 }
        );
      }

      const { data, error: createError } = await supabase
        .from('inventory')
        .insert({
          product_id,
          branch_id,
          quantity: quantity_change,
          reorder_level: 10, // Default reorder level
          max_stock_level: 100, // Default max stock level
        })
        .select(`
          *,
          products (
            id,
            name,
            sku,
            unit
          ),
          branches (
            id,
            name
          )
        `)
        .single();

      if (createError) {
        return NextResponse.json(
          { error: createError.message },
          { status: 400 }
        );
      }

      updatedInventory = data;
    }

    // Create inventory movement record
    const { error: movementError } = await supabase
      .from('inventory_movements')
      .insert({
        product_id,
        branch_id,
        movement_type,
        quantity: quantity_change,
        reference_id: reference_id || null,
        reference_type: reference_type || null,
        notes: notes || null,
        created_by,
      });

    if (movementError) {
      console.error('Failed to create inventory movement:', movementError);
      // Don't fail the request if movement logging fails
    }

    return NextResponse.json({
      success: true,
      data: updatedInventory,
      message: 'Inventory updated successfully',
    });

  } catch (error) {
    console.error('Update inventory error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/inventory - Set inventory levels and thresholds
export async function PUT(request: NextRequest) {
  try {
    const inventoryData = await request.json();
    const {
      product_id,
      branch_id,
      quantity,
      reorder_level,
      max_stock_level,
    } = inventoryData;

    // Validation
    if (!product_id || !branch_id || quantity === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey);

    // Upsert inventory record
    const { data: inventory, error } = await supabase
      .from('inventory')
      .upsert({
        product_id,
        branch_id,
        quantity,
        reorder_level: reorder_level || 10,
        max_stock_level: max_stock_level || 100,
        last_updated: new Date().toISOString(),
      })
      .select(`
        *,
        products (
          id,
          name,
          sku,
          unit
        ),
        branches (
          id,
          name
        )
      `)
      .single();

    if (error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      data: inventory,
      message: 'Inventory levels updated successfully',
    });

  } catch (error) {
    console.error('Set inventory error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
