import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase';
import { generateOrderNumber } from '@/utils';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// GET /api/orders - Fetch orders with pagination and filters
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const branchId = searchParams.get('branch_id');
    const orderType = searchParams.get('order_type');
    const startDate = searchParams.get('start_date');
    const endDate = searchParams.get('end_date');

    const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey);

    // Build query
    let query = supabase
      .from('orders')
      .select(`
        *,
        customers (
          id,
          name,
          phone,
          email
        ),
        branches (
          id,
          name
        ),
        users!orders_cashier_id_fkey (
          id,
          name
        ),
        sales_rep:users!orders_sales_rep_id_fkey (
          id,
          name
        ),
        tables (
          id,
          table_number
        ),
        order_items (
          id,
          quantity,
          unit_price,
          total_price,
          notes,
          products (
            id,
            name,
            sku
          )
        )
      `)
      .order('created_at', { ascending: false });

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }
    if (branchId) {
      query = query.eq('branch_id', branchId);
    }
    if (orderType) {
      query = query.eq('order_type', orderType);
    }
    if (startDate) {
      query = query.gte('created_at', startDate);
    }
    if (endDate) {
      query = query.lte('created_at', endDate);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    const { data: orders, error, count } = await query;

    if (error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    // Get total count for pagination
    const { count: totalCount } = await supabase
      .from('orders')
      .select('*', { count: 'exact', head: true });

    return NextResponse.json({
      success: true,
      data: orders,
      pagination: {
        page,
        limit,
        total: totalCount || 0,
        totalPages: Math.ceil((totalCount || 0) / limit),
      },
    });

  } catch (error) {
    console.error('Get orders error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/orders - Create new order
export async function POST(request: NextRequest) {
  try {
    const orderData = await request.json();
    const {
      customer_id,
      branch_id,
      cashier_id,
      sales_rep_id,
      table_id,
      order_type,
      items,
      notes,
      delivery_address,
      delivery_location,
      payment_method = 'cash',
    } = orderData;

    // Validation
    if (!branch_id || !cashier_id || !order_type || !items || items.length === 0) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey);

    // Generate order number
    const orderNumber = generateOrderNumber();

    // Calculate totals
    let subtotal = 0;
    const processedItems = [];

    for (const item of items) {
      // Get product details
      const { data: product, error: productError } = await supabase
        .from('products')
        .select('id, name, price')
        .eq('id', item.product_id)
        .single();

      if (productError || !product) {
        return NextResponse.json(
          { error: `Product not found: ${item.product_id}` },
          { status: 400 }
        );
      }

      const itemTotal = product.price * item.quantity;
      subtotal += itemTotal;

      processedItems.push({
        product_id: item.product_id,
        quantity: item.quantity,
        unit_price: product.price,
        total_price: itemTotal,
        notes: item.notes || null,
      });
    }

    // Calculate tax (10% for example)
    const taxRate = 0.10;
    const taxAmount = subtotal * taxRate;
    const totalAmount = subtotal + taxAmount;

    // Create order
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .insert({
        order_number: orderNumber,
        customer_id: customer_id || null,
        branch_id,
        cashier_id,
        sales_rep_id: sales_rep_id || null,
        table_id: table_id || null,
        order_type,
        subtotal,
        tax_amount: taxAmount,
        total_amount: totalAmount,
        payment_method,
        notes: notes || null,
        delivery_address: delivery_address || null,
        delivery_latitude: delivery_location?.latitude || null,
        delivery_longitude: delivery_location?.longitude || null,
      })
      .select()
      .single();

    if (orderError) {
      return NextResponse.json(
        { error: orderError.message },
        { status: 400 }
      );
    }

    // Create order items
    const orderItemsWithOrderId = processedItems.map(item => ({
      ...item,
      order_id: order.id,
    }));

    const { error: itemsError } = await supabase
      .from('order_items')
      .insert(orderItemsWithOrderId);

    if (itemsError) {
      // Rollback order if items creation fails
      await supabase.from('orders').delete().eq('id', order.id);
      return NextResponse.json(
        { error: itemsError.message },
        { status: 400 }
      );
    }

    // Fetch complete order with relations
    const { data: completeOrder } = await supabase
      .from('orders')
      .select(`
        *,
        customers (
          id,
          name,
          phone
        ),
        order_items (
          id,
          quantity,
          unit_price,
          total_price,
          notes,
          products (
            id,
            name,
            sku
          )
        )
      `)
      .eq('id', order.id)
      .single();

    return NextResponse.json({
      success: true,
      data: completeOrder,
      message: 'Order created successfully',
    });

  } catch (error) {
    console.error('Create order error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
