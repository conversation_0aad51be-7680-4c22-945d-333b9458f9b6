import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// GET /api/products - Fetch products with pagination and filters
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const categoryId = searchParams.get('category_id');
    const search = searchParams.get('search');
    const isActive = searchParams.get('is_active');
    const isManufactured = searchParams.get('is_manufactured');

    const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey);

    // Build query
    let query = supabase
      .from('products')
      .select(`
        *,
        categories (
          id,
          name
        ),
        recipes (
          id,
          name,
          preparation_time,
          cost_per_unit
        )
      `)
      .order('name', { ascending: true });

    // Apply filters
    if (categoryId) {
      query = query.eq('category_id', categoryId);
    }
    if (isActive !== null) {
      query = query.eq('is_active', isActive === 'true');
    }
    if (isManufactured !== null) {
      query = query.eq('is_manufactured', isManufactured === 'true');
    }
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%,sku.ilike.%${search}%`);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    const { data: products, error } = await query;

    if (error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('products')
      .select('*', { count: 'exact', head: true });

    // Apply same filters for count
    if (categoryId) {
      countQuery = countQuery.eq('category_id', categoryId);
    }
    if (isActive !== null) {
      countQuery = countQuery.eq('is_active', isActive === 'true');
    }
    if (isManufactured !== null) {
      countQuery = countQuery.eq('is_manufactured', isManufactured === 'true');
    }
    if (search) {
      countQuery = countQuery.or(`name.ilike.%${search}%,description.ilike.%${search}%,sku.ilike.%${search}%`);
    }

    const { count: totalCount } = await countQuery;

    return NextResponse.json({
      success: true,
      data: products,
      pagination: {
        page,
        limit,
        total: totalCount || 0,
        totalPages: Math.ceil((totalCount || 0) / limit),
      },
    });

  } catch (error) {
    console.error('Get products error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/products - Create new product
export async function POST(request: NextRequest) {
  try {
    const productData = await request.json();
    const {
      name,
      description,
      category_id,
      sku,
      barcode,
      price,
      cost,
      unit = 'piece',
      is_manufactured = false,
      image_url,
    } = productData;

    // Validation
    if (!name || !category_id || !sku || price === undefined || cost === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields: name, category_id, sku, price, cost' },
        { status: 400 }
      );
    }

    const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey);

    // Check if SKU already exists
    const { data: existingProduct } = await supabase
      .from('products')
      .select('id')
      .eq('sku', sku)
      .single();

    if (existingProduct) {
      return NextResponse.json(
        { error: 'Product with this SKU already exists' },
        { status: 400 }
      );
    }

    // Create product
    const { data: product, error: productError } = await supabase
      .from('products')
      .insert({
        name,
        description: description || null,
        category_id,
        sku,
        barcode: barcode || null,
        price: parseFloat(price),
        cost: parseFloat(cost),
        unit,
        is_manufactured,
        image_url: image_url || null,
      })
      .select(`
        *,
        categories (
          id,
          name
        )
      `)
      .single();

    if (productError) {
      return NextResponse.json(
        { error: productError.message },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      data: product,
      message: 'Product created successfully',
    });

  } catch (error) {
    console.error('Create product error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
