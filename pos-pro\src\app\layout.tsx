import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { AuthProvider } from "@/lib/auth-context";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "POS Pro - Restaurant Point of Sale System",
  description: "Comprehensive Point of Sale system for restaurants and food service businesses with inventory management, delivery tracking, and analytics.",
  keywords: ["POS", "restaurant", "point of sale", "inventory", "delivery", "food service"],
  authors: [{ name: "POS Pro Team" }],
  viewport: "width=device-width, initial-scale=1",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.variable} font-sans antialiased`}>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
