'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { createSupabaseBrowserClient } from '@/lib/supabase';
import { User as AppUser } from '@/types';

interface AuthContextType {
  user: AppUser | null;
  session: Session | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AppUser | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const supabase = createSupabaseBrowserClient();

  // Fetch user profile from our users table
  const fetchUserProfile = async (authUser: User): Promise<AppUser | null> => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select(`
          id,
          email,
          name,
          role,
          branch_id,
          territory_id,
          phone,
          avatar_url,
          is_active,
          branches:branch_id (
            id,
            name,
            address
          ),
          territories:territory_id (
            id,
            name
          )
        `)
        .eq('id', authUser.id)
        .eq('is_active', true)
        .single();

      if (error || !data) {
        console.error('Error fetching user profile:', error);
        return null;
      }

      return {
        id: data.id,
        email: data.email,
        name: data.name,
        role: data.role as AppUser['role'],
        branch_id: data.branch_id,
        territory_id: data.territory_id,
        phone: data.phone,
        avatar_url: data.avatar_url,
        is_active: data.is_active,
        created_at: '', // Not needed for context
        updated_at: '', // Not needed for context
      };
    } catch (error) {
      console.error('Error in fetchUserProfile:', error);
      return null;
    }
  };

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const { data: { session: initialSession } } = await supabase.auth.getSession();
        
        if (initialSession?.user) {
          const userProfile = await fetchUserProfile(initialSession.user);
          setUser(userProfile);
          setSession(initialSession);
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email);
        
        if (session?.user) {
          const userProfile = await fetchUserProfile(session.user);
          setUser(userProfile);
          setSession(session);
        } else {
          setUser(null);
          setSession(null);
        }
        
        setLoading(false);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        return { success: false, error: error.message };
      }

      if (data.user) {
        const userProfile = await fetchUserProfile(data.user);
        if (!userProfile) {
          await supabase.auth.signOut();
          return { success: false, error: 'User profile not found or inactive' };
        }
        setUser(userProfile);
        setSession(data.session);
      }

      return { success: true };
    } catch (error) {
      console.error('Sign in error:', error);
      return { success: false, error: 'An unexpected error occurred' };
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      await supabase.auth.signOut();
      setUser(null);
      setSession(null);
    } catch (error) {
      console.error('Sign out error:', error);
    } finally {
      setLoading(false);
    }
  };

  const refreshUser = async () => {
    if (session?.user) {
      const userProfile = await fetchUserProfile(session.user);
      setUser(userProfile);
    }
  };

  const value = {
    user,
    session,
    loading,
    signIn,
    signOut,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Permission checking hooks
export function usePermissions() {
  const { user } = useAuth();

  const hasRole = (roles: AppUser['role'] | AppUser['role'][]) => {
    if (!user) return false;
    const roleArray = Array.isArray(roles) ? roles : [roles];
    return roleArray.includes(user.role);
  };

  const canManageProducts = () => hasRole(['admin', 'manager']);
  const canManageOrders = () => hasRole(['admin', 'manager', 'cashier']);
  const canManageInventory = () => hasRole(['admin', 'manager', 'warehouse_staff']);
  const canManageUsers = () => hasRole(['admin', 'manager']);
  const canViewReports = () => hasRole(['admin', 'manager']);
  const canManageKitchen = () => hasRole(['admin', 'manager', 'kitchen_staff']);
  const canManageDelivery = () => hasRole(['admin', 'manager', 'sales_rep']);
  const canAccessPOS = () => hasRole(['admin', 'manager', 'cashier', 'sales_rep']);

  return {
    hasRole,
    canManageProducts,
    canManageOrders,
    canManageInventory,
    canManageUsers,
    canViewReports,
    canManageKitchen,
    canManageDelivery,
    canAccessPOS,
  };
}
