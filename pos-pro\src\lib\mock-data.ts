// Mock data for development when Supa<PERSON> is not configured
import { Product, Order, Customer, DashboardStats } from '@/types';

export const mockProducts: Product[] = [
  {
    id: 'prod-1',
    name: 'Espresso',
    description: 'Strong coffee shot',
    category_id: 'cat-1',
    sku: 'BEV-ESP-001',
    barcode: '1234567890',
    price: 2.50,
    cost: 0.75,
    unit: 'cup',
    is_manufactured: true,
    recipe_id: 'recipe-1',
    image_url: null,
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'prod-2',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    description: '<PERSON><PERSON>ress<PERSON> with steamed milk',
    category_id: 'cat-1',
    sku: 'BEV-CAP-001',
    barcode: '1234567891',
    price: 4.50,
    cost: 1.25,
    unit: 'cup',
    is_manufactured: true,
    recipe_id: 'recipe-2',
    image_url: null,
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'prod-3',
    name: '<PERSON><PERSON>',
    description: 'Espresso with steamed milk and foam',
    category_id: 'cat-1',
    sku: 'BEV-LAT-001',
    barcode: '1234567892',
    price: 5.00,
    cost: 1.50,
    unit: 'cup',
    is_manufactured: true,
    recipe_id: 'recipe-3',
    image_url: null,
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'prod-4',
    name: 'Caesar Salad',
    description: 'Fresh romaine with caesar dressing',
    category_id: 'cat-2',
    sku: 'APP-CS-001',
    barcode: '1234567893',
    price: 8.50,
    cost: 3.00,
    unit: 'plate',
    is_manufactured: true,
    recipe_id: 'recipe-4',
    image_url: null,
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'prod-5',
    name: 'Grilled Chicken',
    description: 'Seasoned grilled chicken breast',
    category_id: 'cat-3',
    sku: 'MAIN-GC-001',
    barcode: '1234567894',
    price: 15.00,
    cost: 6.00,
    unit: 'plate',
    is_manufactured: true,
    recipe_id: 'recipe-5',
    image_url: null,
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'prod-6',
    name: 'Chocolate Cake',
    description: 'Rich chocolate layer cake',
    category_id: 'cat-4',
    sku: 'DES-CC-001',
    barcode: '1234567895',
    price: 6.50,
    cost: 2.50,
    unit: 'slice',
    is_manufactured: true,
    recipe_id: 'recipe-6',
    image_url: null,
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
];

export const mockCustomers: Customer[] = [
  {
    id: 'cust-1',
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '******-0150',
    address: '123 Customer St, Downtown',
    location: { latitude: 40.7127, longitude: -74.005 },
    loyalty_points: 150,
    rating: 4.5,
    notes: 'Regular customer, prefers oat milk',
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'cust-2',
    name: 'Jane Smith',
    email: '<EMAIL>',
    phone: '******-0151',
    address: '456 Client Ave, Uptown',
    location: { latitude: 40.7483, longitude: -73.9856 },
    loyalty_points: 75,
    rating: 4.2,
    notes: null,
    is_active: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
];

export const mockOrders: Order[] = [
  {
    id: 'order-1',
    order_number: 'ORD-001-2024',
    customer_id: 'cust-1',
    branch_id: 'demo-branch-id',
    cashier_id: 'demo-cashier-id',
    sales_rep_id: null,
    table_id: null,
    order_type: 'takeaway',
    status: 'delivered',
    subtotal: 12.00,
    tax_amount: 1.20,
    discount_amount: 0,
    total_amount: 13.20,
    payment_method: 'card',
    payment_status: 'paid',
    notes: null,
    delivery_address: null,
    delivery_location: null,
    estimated_delivery_time: null,
    created_at: '2024-01-15T10:30:00Z',
    updated_at: '2024-01-15T10:45:00Z',
  },
  {
    id: 'order-2',
    order_number: 'ORD-002-2024',
    customer_id: 'cust-2',
    branch_id: 'demo-branch-id',
    cashier_id: 'demo-cashier-id',
    sales_rep_id: null,
    table_id: null,
    order_type: 'dine_in',
    status: 'preparing',
    subtotal: 23.50,
    tax_amount: 2.35,
    discount_amount: 0,
    total_amount: 25.85,
    payment_method: 'cash',
    payment_status: 'paid',
    notes: 'Extra sauce on the side',
    delivery_address: null,
    delivery_location: null,
    estimated_delivery_time: null,
    created_at: '2024-01-15T11:15:00Z',
    updated_at: '2024-01-15T11:20:00Z',
  },
  {
    id: 'order-3',
    order_number: 'ORD-003-2024',
    customer_id: null,
    branch_id: 'demo-branch-id',
    cashier_id: 'demo-cashier-id',
    sales_rep_id: null,
    table_id: null,
    order_type: 'takeaway',
    status: 'pending',
    subtotal: 7.00,
    tax_amount: 0.70,
    discount_amount: 0,
    total_amount: 7.70,
    payment_method: 'digital_wallet',
    payment_status: 'pending',
    notes: null,
    delivery_address: null,
    delivery_location: null,
    estimated_delivery_time: null,
    created_at: '2024-01-15T12:00:00Z',
    updated_at: '2024-01-15T12:00:00Z',
  },
];

export const mockDashboardStats: DashboardStats = {
  daily_sales: 1250.75,
  daily_orders: 45,
  pending_orders: 3,
  low_stock_items: 2,
  active_tables: 8,
  sales_growth: 12.5,
  sales_by_type: {
    dine_in: 650.25,
    takeaway: 400.50,
    delivery: 200.00,
    field_sales: 0,
  },
  top_products: [
    {
      product_id: 'prod-2',
      name: 'Cappuccino',
      sku: 'BEV-CAP-001',
      total_quantity: 25,
      total_revenue: 112.50,
    },
    {
      product_id: 'prod-3',
      name: 'Latte',
      sku: 'BEV-LAT-001',
      total_quantity: 20,
      total_revenue: 100.00,
    },
    {
      product_id: 'prod-1',
      name: 'Espresso',
      sku: 'BEV-ESP-001',
      total_quantity: 18,
      total_revenue: 45.00,
    },
  ],
  hourly_sales: Array.from({ length: 24 }, (_, hour) => ({
    hour,
    sales: Math.random() * 100,
    orders: Math.floor(Math.random() * 10),
  })),
};

// Mock API responses
export const createMockApiResponse = <T>(data: T, success: boolean = true, message?: string) => ({
  success,
  data,
  message: message || (success ? 'Success' : 'Error'),
});

export const createMockPaginatedResponse = <T>(
  data: T[],
  page: number = 1,
  limit: number = 20
) => ({
  success: true,
  data,
  pagination: {
    page,
    limit,
    total: data.length,
    totalPages: Math.ceil(data.length / limit),
  },
});
