import { createClient } from '@supabase/supabase-js';
import { createBrowserClient } from '@supabase/ssr';

// Get environment variables with fallbacks for development
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://demo.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'demo_anon_key';

// Check if we're in a browser environment and have valid Supabase credentials
const isValidSupabaseConfig = () => {
  return supabaseUrl !== 'https://demo.supabase.co' && supabaseAnonKey !== 'demo_anon_key';
};

// Client-side Supabase client with error handling
export const supabase = (() => {
  try {
    if (isValidSupabaseConfig()) {
      return createClient(supabaseUrl, supabaseAnonKey);
    } else {
      // Return a mock client for development when Supabase is not configured
      console.warn('Supabase not configured. Using mock client for development.');
      return null;
    }
  } catch (error) {
    console.error('Error creating Supabase client:', error);
    return null;
  }
})();

// Browser client for SSR with error handling
export function createSupabaseBrowserClient() {
  try {
    if (isValidSupabaseConfig()) {
      return createBrowserClient(supabaseUrl, supabaseAnonKey);
    } else {
      console.warn('Supabase not configured. Using mock client for development.');
      return null;
    }
  } catch (error) {
    console.error('Error creating Supabase browser client:', error);
    return null;
  }
}

// Database types (will be generated from Supabase)
export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          name: string;
          role: string;
          branch_id: string | null;
          territory_id: string | null;
          phone: string | null;
          avatar_url: string | null;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          email: string;
          name: string;
          role: string;
          branch_id?: string | null;
          territory_id?: string | null;
          phone?: string | null;
          avatar_url?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          name?: string;
          role?: string;
          branch_id?: string | null;
          territory_id?: string | null;
          phone?: string | null;
          avatar_url?: string | null;
          is_active?: boolean;
          updated_at?: string;
        };
      };
      branches: {
        Row: {
          id: string;
          name: string;
          address: string;
          phone: string;
          email: string;
          latitude: number;
          longitude: number;
          delivery_radius: number;
          is_active: boolean;
          manager_id: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          address: string;
          phone: string;
          email: string;
          latitude: number;
          longitude: number;
          delivery_radius?: number;
          is_active?: boolean;
          manager_id: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          address?: string;
          phone?: string;
          email?: string;
          latitude?: number;
          longitude?: number;
          delivery_radius?: number;
          is_active?: boolean;
          manager_id?: string;
          updated_at?: string;
        };
      };
      customers: {
        Row: {
          id: string;
          name: string;
          email: string | null;
          phone: string;
          address: string;
          latitude: number | null;
          longitude: number | null;
          loyalty_points: number;
          rating: number;
          notes: string | null;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          email?: string | null;
          phone: string;
          address: string;
          latitude?: number | null;
          longitude?: number | null;
          loyalty_points?: number;
          rating?: number;
          notes?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          email?: string | null;
          phone?: string;
          address?: string;
          latitude?: number | null;
          longitude?: number | null;
          loyalty_points?: number;
          rating?: number;
          notes?: string | null;
          is_active?: boolean;
          updated_at?: string;
        };
      };
      products: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          category_id: string;
          sku: string;
          barcode: string | null;
          price: number;
          cost: number;
          unit: string;
          is_manufactured: boolean;
          recipe_id: string | null;
          image_url: string | null;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          category_id: string;
          sku: string;
          barcode?: string | null;
          price: number;
          cost: number;
          unit: string;
          is_manufactured?: boolean;
          recipe_id?: string | null;
          image_url?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          category_id?: string;
          sku?: string;
          barcode?: string | null;
          price?: number;
          cost?: number;
          unit?: string;
          is_manufactured?: boolean;
          recipe_id?: string | null;
          image_url?: string | null;
          is_active?: boolean;
          updated_at?: string;
        };
      };
      orders: {
        Row: {
          id: string;
          order_number: string;
          customer_id: string | null;
          branch_id: string;
          cashier_id: string;
          sales_rep_id: string | null;
          table_id: string | null;
          order_type: string;
          status: string;
          subtotal: number;
          tax_amount: number;
          discount_amount: number;
          total_amount: number;
          payment_method: string;
          payment_status: string;
          notes: string | null;
          delivery_address: string | null;
          delivery_latitude: number | null;
          delivery_longitude: number | null;
          estimated_delivery_time: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          order_number: string;
          customer_id?: string | null;
          branch_id: string;
          cashier_id: string;
          sales_rep_id?: string | null;
          table_id?: string | null;
          order_type: string;
          status?: string;
          subtotal: number;
          tax_amount: number;
          discount_amount: number;
          total_amount: number;
          payment_method: string;
          payment_status?: string;
          notes?: string | null;
          delivery_address?: string | null;
          delivery_latitude?: number | null;
          delivery_longitude?: number | null;
          estimated_delivery_time?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          order_number?: string;
          customer_id?: string | null;
          branch_id?: string;
          cashier_id?: string;
          sales_rep_id?: string | null;
          table_id?: string | null;
          order_type?: string;
          status?: string;
          subtotal?: number;
          tax_amount?: number;
          discount_amount?: number;
          total_amount?: number;
          payment_method?: string;
          payment_status?: string;
          notes?: string | null;
          delivery_address?: string | null;
          delivery_latitude?: number | null;
          delivery_longitude?: number | null;
          estimated_delivery_time?: string | null;
          updated_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      user_role: 'admin' | 'manager' | 'cashier' | 'kitchen_staff' | 'sales_rep' | 'warehouse_staff';
      order_type: 'dine_in' | 'takeaway' | 'delivery' | 'field_sales';
      order_status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'delivered' | 'cancelled';
      payment_method: 'cash' | 'card' | 'digital_wallet' | 'credit';
      payment_status: 'pending' | 'paid' | 'partial' | 'refunded';
    };
  };
};
