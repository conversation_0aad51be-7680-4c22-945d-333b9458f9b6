-- Create indexes for better performance

-- Users indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_branch_id ON users(branch_id);
CREATE INDEX idx_users_territory_id ON users(territory_id);
CREATE INDEX idx_users_is_active ON users(is_active);

-- Branches indexes
CREATE INDEX idx_branches_location ON branches USING GIST(location);
CREATE INDEX idx_branches_manager_id ON branches(manager_id);
CREATE INDEX idx_branches_is_active ON branches(is_active);

-- Categories indexes
CREATE INDEX idx_categories_parent_id ON categories(parent_id);
CREATE INDEX idx_categories_is_active ON categories(is_active);

-- Products indexes
CREATE INDEX idx_products_category_id ON products(category_id);
CREATE INDEX idx_products_sku ON products(sku);
CREATE INDEX idx_products_barcode ON products(barcode);
CREATE INDEX idx_products_is_manufactured ON products(is_manufactured);
CREATE INDEX idx_products_is_active ON products(is_active);
CREATE INDEX idx_products_name_search ON products USING GIN(to_tsvector('english', name || ' ' || COALESCE(description, '')));

-- Customers indexes
CREATE INDEX idx_customers_phone ON customers(phone);
CREATE INDEX idx_customers_email ON customers(email);
CREATE INDEX idx_customers_location ON customers USING GIST(location);
CREATE INDEX idx_customers_is_active ON customers(is_active);
CREATE INDEX idx_customers_name_search ON customers USING GIN(to_tsvector('english', name));

-- Suppliers indexes
CREATE INDEX idx_suppliers_email ON suppliers(email);
CREATE INDEX idx_suppliers_phone ON suppliers(phone);
CREATE INDEX idx_suppliers_location ON suppliers USING GIST(location);
CREATE INDEX idx_suppliers_is_active ON suppliers(is_active);

-- Territories indexes
CREATE INDEX idx_territories_boundaries ON territories USING GIST(boundaries);
CREATE INDEX idx_territories_sales_rep_id ON territories(sales_rep_id);
CREATE INDEX idx_territories_is_active ON territories(is_active);

-- Tables indexes
CREATE INDEX idx_tables_branch_id ON tables(branch_id);
CREATE INDEX idx_tables_status ON tables(status);
CREATE INDEX idx_tables_waiter_id ON tables(waiter_id);
CREATE INDEX idx_tables_current_order_id ON tables(current_order_id);

-- Orders indexes
CREATE INDEX idx_orders_order_number ON orders(order_number);
CREATE INDEX idx_orders_customer_id ON orders(customer_id);
CREATE INDEX idx_orders_branch_id ON orders(branch_id);
CREATE INDEX idx_orders_cashier_id ON orders(cashier_id);
CREATE INDEX idx_orders_sales_rep_id ON orders(sales_rep_id);
CREATE INDEX idx_orders_table_id ON orders(table_id);
CREATE INDEX idx_orders_order_type ON orders(order_type);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_payment_status ON orders(payment_status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_orders_delivery_location ON orders USING GIST(delivery_location);

-- Order items indexes
CREATE INDEX idx_order_items_order_id ON order_items(order_id);
CREATE INDEX idx_order_items_product_id ON order_items(product_id);

-- Inventory indexes
CREATE INDEX idx_inventory_product_id ON inventory(product_id);
CREATE INDEX idx_inventory_branch_id ON inventory(branch_id);
CREATE INDEX idx_inventory_low_stock ON inventory(product_id, branch_id) WHERE quantity <= reorder_level;

-- Purchase orders indexes
CREATE INDEX idx_purchase_orders_po_number ON purchase_orders(po_number);
CREATE INDEX idx_purchase_orders_supplier_id ON purchase_orders(supplier_id);
CREATE INDEX idx_purchase_orders_branch_id ON purchase_orders(branch_id);
CREATE INDEX idx_purchase_orders_status ON purchase_orders(status);
CREATE INDEX idx_purchase_orders_created_by ON purchase_orders(created_by);
CREATE INDEX idx_purchase_orders_created_at ON purchase_orders(created_at);

-- Purchase order items indexes
CREATE INDEX idx_purchase_order_items_purchase_order_id ON purchase_order_items(purchase_order_id);
CREATE INDEX idx_purchase_order_items_product_id ON purchase_order_items(product_id);

-- Recipes indexes
CREATE INDEX idx_recipes_product_id ON recipes(product_id);
CREATE INDEX idx_recipes_is_active ON recipes(is_active);

-- Recipe ingredients indexes
CREATE INDEX idx_recipe_ingredients_recipe_id ON recipe_ingredients(recipe_id);
CREATE INDEX idx_recipe_ingredients_ingredient_id ON recipe_ingredients(ingredient_id);

-- Manufacturing orders indexes
CREATE INDEX idx_manufacturing_orders_mo_number ON manufacturing_orders(mo_number);
CREATE INDEX idx_manufacturing_orders_recipe_id ON manufacturing_orders(recipe_id);
CREATE INDEX idx_manufacturing_orders_branch_id ON manufacturing_orders(branch_id);
CREATE INDEX idx_manufacturing_orders_status ON manufacturing_orders(status);
CREATE INDEX idx_manufacturing_orders_created_by ON manufacturing_orders(created_by);

-- Delivery routes indexes
CREATE INDEX idx_delivery_routes_order_id ON delivery_routes(order_id);
CREATE INDEX idx_delivery_routes_driver_id ON delivery_routes(driver_id);
CREATE INDEX idx_delivery_routes_status ON delivery_routes(status);
CREATE INDEX idx_delivery_routes_start_location ON delivery_routes USING GIST(start_location);
CREATE INDEX idx_delivery_routes_end_location ON delivery_routes USING GIST(end_location);

-- Loyalty transactions indexes
CREATE INDEX idx_loyalty_transactions_customer_id ON loyalty_transactions(customer_id);
CREATE INDEX idx_loyalty_transactions_order_id ON loyalty_transactions(order_id);
CREATE INDEX idx_loyalty_transactions_type ON loyalty_transactions(transaction_type);
CREATE INDEX idx_loyalty_transactions_created_at ON loyalty_transactions(created_at);

-- Customer ratings indexes
CREATE INDEX idx_customer_ratings_customer_id ON customer_ratings(customer_id);
CREATE INDEX idx_customer_ratings_order_id ON customer_ratings(order_id);
CREATE INDEX idx_customer_ratings_rating ON customer_ratings(rating);

-- Inventory movements indexes
CREATE INDEX idx_inventory_movements_product_id ON inventory_movements(product_id);
CREATE INDEX idx_inventory_movements_branch_id ON inventory_movements(branch_id);
CREATE INDEX idx_inventory_movements_type ON inventory_movements(movement_type);
CREATE INDEX idx_inventory_movements_reference ON inventory_movements(reference_id, reference_type);
CREATE INDEX idx_inventory_movements_created_at ON inventory_movements(created_at);
CREATE INDEX idx_inventory_movements_created_by ON inventory_movements(created_by);

-- Create functions for triggers

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to calculate order totals
CREATE OR REPLACE FUNCTION calculate_order_totals()
RETURNS TRIGGER AS $$
BEGIN
    -- Update order totals when order items change
    UPDATE orders 
    SET subtotal = (
        SELECT COALESCE(SUM(total_price), 0) 
        FROM order_items 
        WHERE order_id = COALESCE(NEW.order_id, OLD.order_id)
    ),
    total_amount = subtotal + tax_amount - discount_amount
    WHERE id = COALESCE(NEW.order_id, OLD.order_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

-- Function to update inventory on order completion
CREATE OR REPLACE FUNCTION update_inventory_on_order()
RETURNS TRIGGER AS $$
BEGIN
    -- When order status changes to 'confirmed', reserve inventory
    IF NEW.status = 'confirmed' AND OLD.status != 'confirmed' THEN
        UPDATE inventory 
        SET reserved_quantity = reserved_quantity + oi.quantity
        FROM order_items oi
        WHERE inventory.product_id = oi.product_id 
        AND inventory.branch_id = NEW.branch_id
        AND oi.order_id = NEW.id;
    END IF;
    
    -- When order status changes to 'delivered' or 'ready', reduce actual inventory
    IF NEW.status IN ('delivered', 'ready') AND OLD.status NOT IN ('delivered', 'ready') THEN
        -- Move from reserved to actual reduction
        UPDATE inventory 
        SET quantity = quantity - oi.quantity,
            reserved_quantity = reserved_quantity - oi.quantity
        FROM order_items oi
        WHERE inventory.product_id = oi.product_id 
        AND inventory.branch_id = NEW.branch_id
        AND oi.order_id = NEW.id;
        
        -- Create inventory movement records
        INSERT INTO inventory_movements (product_id, branch_id, movement_type, quantity, reference_id, reference_type, created_by)
        SELECT oi.product_id, NEW.branch_id, 'out', -oi.quantity, NEW.id, 'order', NEW.cashier_id
        FROM order_items oi
        WHERE oi.order_id = NEW.id;
    END IF;
    
    -- When order is cancelled, release reserved inventory
    IF NEW.status = 'cancelled' AND OLD.status != 'cancelled' THEN
        UPDATE inventory 
        SET reserved_quantity = reserved_quantity - oi.quantity
        FROM order_items oi
        WHERE inventory.product_id = oi.product_id 
        AND inventory.branch_id = NEW.branch_id
        AND oi.order_id = NEW.id;
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to update customer loyalty points
CREATE OR REPLACE FUNCTION update_loyalty_points()
RETURNS TRIGGER AS $$
BEGIN
    -- Award loyalty points when order is completed (1 point per dollar spent)
    IF NEW.status = 'delivered' AND OLD.status != 'delivered' AND NEW.customer_id IS NOT NULL THEN
        -- Calculate points (1 point per dollar, rounded down)
        INSERT INTO loyalty_transactions (customer_id, order_id, points_earned, transaction_type, description)
        VALUES (NEW.customer_id, NEW.id, FLOOR(NEW.total_amount), 'earned', 'Points earned from order ' || NEW.order_number);
        
        -- Update customer total points
        UPDATE customers 
        SET loyalty_points = loyalty_points + FLOOR(NEW.total_amount)
        WHERE id = NEW.customer_id;
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to update customer rating
CREATE OR REPLACE FUNCTION update_customer_rating()
RETURNS TRIGGER AS $$
BEGIN
    -- Recalculate customer average rating
    UPDATE customers 
    SET rating = (
        SELECT COALESCE(AVG(rating), 0) 
        FROM customer_ratings 
        WHERE customer_id = NEW.customer_id
    )
    WHERE id = NEW.customer_id;
    
    RETURN NEW;
END;
$$ language 'plpgsql';
