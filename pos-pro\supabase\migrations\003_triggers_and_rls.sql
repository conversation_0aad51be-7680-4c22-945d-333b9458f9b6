-- Create triggers

-- Updated_at triggers for all tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_branches_updated_at BEFORE UPDATE ON branches FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customers_updated_at BEFORE UPDATE ON customers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_suppliers_updated_at BEFORE UPDATE ON suppliers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_territories_updated_at BEFORE UPDATE ON territories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tables_updated_at BEFORE UPDATE ON tables FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_purchase_orders_updated_at BEFORE UPDATE ON purchase_orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_recipes_updated_at BEFORE UPDATE ON recipes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_manufacturing_orders_updated_at BEFORE UPDATE ON manufacturing_orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_delivery_routes_updated_at BEFORE UPDATE ON delivery_routes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Business logic triggers
CREATE TRIGGER calculate_order_totals_trigger AFTER INSERT OR UPDATE OR DELETE ON order_items FOR EACH ROW EXECUTE FUNCTION calculate_order_totals();
CREATE TRIGGER update_inventory_on_order_trigger AFTER UPDATE ON orders FOR EACH ROW EXECUTE FUNCTION update_inventory_on_order();
CREATE TRIGGER update_loyalty_points_trigger AFTER UPDATE ON orders FOR EACH ROW EXECUTE FUNCTION update_loyalty_points();
CREATE TRIGGER update_customer_rating_trigger AFTER INSERT OR UPDATE ON customer_ratings FOR EACH ROW EXECUTE FUNCTION update_customer_rating();

-- Enable Row Level Security (RLS)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE branches ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE suppliers ENABLE ROW LEVEL SECURITY;
ALTER TABLE territories ENABLE ROW LEVEL SECURITY;
ALTER TABLE tables ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory ENABLE ROW LEVEL SECURITY;
ALTER TABLE purchase_orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE purchase_order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE recipes ENABLE ROW LEVEL SECURITY;
ALTER TABLE recipe_ingredients ENABLE ROW LEVEL SECURITY;
ALTER TABLE manufacturing_orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE delivery_routes ENABLE ROW LEVEL SECURITY;
ALTER TABLE loyalty_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_ratings ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory_movements ENABLE ROW LEVEL SECURITY;

-- Create RLS policies

-- Users policies
CREATE POLICY "Users can view their own profile" ON users FOR SELECT USING (auth.uid()::text = id::text);
CREATE POLICY "Admins can view all users" ON users FOR SELECT USING (
    EXISTS (SELECT 1 FROM users WHERE id::text = auth.uid()::text AND role = 'admin')
);
CREATE POLICY "Managers can view users in their branch" ON users FOR SELECT USING (
    EXISTS (SELECT 1 FROM users u WHERE u.id::text = auth.uid()::text AND u.role IN ('admin', 'manager') AND u.branch_id = branch_id)
);

-- Branches policies
CREATE POLICY "Users can view their branch" ON branches FOR SELECT USING (
    EXISTS (SELECT 1 FROM users WHERE id::text = auth.uid()::text AND branch_id = branches.id)
);
CREATE POLICY "Admins can view all branches" ON branches FOR SELECT USING (
    EXISTS (SELECT 1 FROM users WHERE id::text = auth.uid()::text AND role = 'admin')
);

-- Categories policies
CREATE POLICY "All authenticated users can view categories" ON categories FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Admins and managers can manage categories" ON categories FOR ALL USING (
    EXISTS (SELECT 1 FROM users WHERE id::text = auth.uid()::text AND role IN ('admin', 'manager'))
);

-- Products policies
CREATE POLICY "All authenticated users can view active products" ON products FOR SELECT USING (
    auth.role() = 'authenticated' AND is_active = true
);
CREATE POLICY "Admins and managers can manage products" ON products FOR ALL USING (
    EXISTS (SELECT 1 FROM users WHERE id::text = auth.uid()::text AND role IN ('admin', 'manager'))
);

-- Customers policies
CREATE POLICY "Users can view customers in their branch territory" ON customers FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM users u 
        WHERE u.id::text = auth.uid()::text 
        AND (
            u.role = 'admin' 
            OR (u.role = 'sales_rep' AND ST_Contains(
                (SELECT boundaries FROM territories WHERE sales_rep_id = u.id), 
                customers.location
            ))
            OR u.role IN ('manager', 'cashier')
        )
    )
);

-- Orders policies
CREATE POLICY "Users can view orders from their branch" ON orders FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM users u 
        WHERE u.id::text = auth.uid()::text 
        AND (u.role = 'admin' OR u.branch_id = orders.branch_id)
    )
);
CREATE POLICY "Sales reps can view their own orders" ON orders FOR SELECT USING (
    sales_rep_id::text = auth.uid()::text
);
CREATE POLICY "Cashiers can create orders in their branch" ON orders FOR INSERT WITH CHECK (
    EXISTS (
        SELECT 1 FROM users u 
        WHERE u.id::text = auth.uid()::text 
        AND u.branch_id = orders.branch_id
        AND u.role IN ('admin', 'manager', 'cashier', 'sales_rep')
    )
);

-- Order items policies
CREATE POLICY "Users can view order items for accessible orders" ON order_items FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM orders o
        JOIN users u ON u.id::text = auth.uid()::text
        WHERE o.id = order_items.order_id
        AND (u.role = 'admin' OR u.branch_id = o.branch_id OR o.sales_rep_id = u.id)
    )
);

-- Inventory policies
CREATE POLICY "Users can view inventory for their branch" ON inventory FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM users u 
        WHERE u.id::text = auth.uid()::text 
        AND (u.role = 'admin' OR u.branch_id = inventory.branch_id)
    )
);
CREATE POLICY "Warehouse staff can manage inventory" ON inventory FOR ALL USING (
    EXISTS (
        SELECT 1 FROM users u 
        WHERE u.id::text = auth.uid()::text 
        AND u.role IN ('admin', 'manager', 'warehouse_staff')
        AND (u.role = 'admin' OR u.branch_id = inventory.branch_id)
    )
);

-- Purchase orders policies
CREATE POLICY "Users can view purchase orders for their branch" ON purchase_orders FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM users u 
        WHERE u.id::text = auth.uid()::text 
        AND (u.role = 'admin' OR u.branch_id = purchase_orders.branch_id)
    )
);
CREATE POLICY "Managers can create purchase orders" ON purchase_orders FOR INSERT WITH CHECK (
    EXISTS (
        SELECT 1 FROM users u 
        WHERE u.id::text = auth.uid()::text 
        AND u.role IN ('admin', 'manager')
        AND (u.role = 'admin' OR u.branch_id = purchase_orders.branch_id)
    )
);

-- Tables policies
CREATE POLICY "Users can view tables in their branch" ON tables FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM users u 
        WHERE u.id::text = auth.uid()::text 
        AND (u.role = 'admin' OR u.branch_id = tables.branch_id)
    )
);

-- Recipes policies
CREATE POLICY "All authenticated users can view active recipes" ON recipes FOR SELECT USING (
    auth.role() = 'authenticated' AND is_active = true
);
CREATE POLICY "Kitchen staff and managers can manage recipes" ON recipes FOR ALL USING (
    EXISTS (SELECT 1 FROM users WHERE id::text = auth.uid()::text AND role IN ('admin', 'manager', 'kitchen_staff'))
);

-- Manufacturing orders policies
CREATE POLICY "Users can view manufacturing orders for their branch" ON manufacturing_orders FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM users u 
        WHERE u.id::text = auth.uid()::text 
        AND (u.role = 'admin' OR u.branch_id = manufacturing_orders.branch_id)
    )
);

-- Delivery routes policies
CREATE POLICY "Drivers can view their own routes" ON delivery_routes FOR SELECT USING (
    driver_id::text = auth.uid()::text
);
CREATE POLICY "Managers can view all routes in their branch" ON delivery_routes FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM users u
        JOIN orders o ON o.id = delivery_routes.order_id
        WHERE u.id::text = auth.uid()::text 
        AND (u.role = 'admin' OR u.branch_id = o.branch_id)
    )
);

-- Loyalty transactions policies
CREATE POLICY "Customers can view their own loyalty transactions" ON loyalty_transactions FOR SELECT USING (
    customer_id IN (
        SELECT id FROM customers 
        WHERE auth.jwt() ->> 'customer_id' = id::text
    )
);
CREATE POLICY "Staff can view loyalty transactions" ON loyalty_transactions FOR SELECT USING (
    EXISTS (SELECT 1 FROM users WHERE id::text = auth.uid()::text AND role IN ('admin', 'manager', 'cashier'))
);

-- Customer ratings policies
CREATE POLICY "Customers can view and create their own ratings" ON customer_ratings FOR ALL USING (
    customer_id IN (
        SELECT id FROM customers 
        WHERE auth.jwt() ->> 'customer_id' = id::text
    )
);
CREATE POLICY "Staff can view all ratings" ON customer_ratings FOR SELECT USING (
    EXISTS (SELECT 1 FROM users WHERE id::text = auth.uid()::text AND role IN ('admin', 'manager', 'cashier'))
);

-- Inventory movements policies
CREATE POLICY "Users can view inventory movements for their branch" ON inventory_movements FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM users u 
        WHERE u.id::text = auth.uid()::text 
        AND (u.role = 'admin' OR u.branch_id = inventory_movements.branch_id)
    )
);

-- Create views for common queries

-- Low stock items view
CREATE VIEW low_stock_items AS
SELECT 
    p.id,
    p.name,
    p.sku,
    i.quantity,
    i.reorder_level,
    i.branch_id,
    b.name as branch_name
FROM inventory i
JOIN products p ON p.id = i.product_id
JOIN branches b ON b.id = i.branch_id
WHERE i.quantity <= i.reorder_level
AND p.is_active = true;

-- Daily sales summary view
CREATE VIEW daily_sales_summary AS
SELECT 
    DATE(o.created_at) as sale_date,
    o.branch_id,
    b.name as branch_name,
    COUNT(*) as total_orders,
    SUM(o.total_amount) as total_sales,
    AVG(o.total_amount) as average_order_value,
    COUNT(CASE WHEN o.order_type = 'dine_in' THEN 1 END) as dine_in_orders,
    COUNT(CASE WHEN o.order_type = 'takeaway' THEN 1 END) as takeaway_orders,
    COUNT(CASE WHEN o.order_type = 'delivery' THEN 1 END) as delivery_orders,
    COUNT(CASE WHEN o.order_type = 'field_sales' THEN 1 END) as field_sales_orders
FROM orders o
JOIN branches b ON b.id = o.branch_id
WHERE o.status = 'delivered'
GROUP BY DATE(o.created_at), o.branch_id, b.name;

-- Product sales performance view
CREATE VIEW product_sales_performance AS
SELECT 
    p.id,
    p.name,
    p.sku,
    c.name as category_name,
    SUM(oi.quantity) as total_quantity_sold,
    SUM(oi.total_price) as total_revenue,
    COUNT(DISTINCT oi.order_id) as orders_count,
    AVG(oi.unit_price) as average_price
FROM order_items oi
JOIN products p ON p.id = oi.product_id
JOIN categories c ON c.id = p.category_id
JOIN orders o ON o.id = oi.order_id
WHERE o.status = 'delivered'
GROUP BY p.id, p.name, p.sku, c.name;
