-- Seed data for POS Pro system

-- Insert sample branches
INSERT INTO branches (id, name, address, phone, email, location, delivery_radius, manager_id) VALUES
('550e8400-e29b-41d4-a716-*********001', 'Downtown Branch', '123 Main St, Downtown', '******-0101', '<EMAIL>', ST_GeogFromText('POINT(-74.006 40.7128)'), 5000, NULL),
('550e8400-e29b-41d4-a716-*********002', 'Uptown Branch', '456 Oak Ave, Uptown', '******-0102', '<EMAIL>', ST_GeogFromText('POINT(-73.9857 40.7484)'), 3000, NULL),
('550e8400-e29b-41d4-a716-*********003', 'Westside Branch', '789 Pine Rd, Westside', '******-0103', '<EMAIL>', ST_GeogFromText('POINT(-74.0059 40.7589)'), 4000, NULL);

-- Insert sample users
INSERT INTO users (id, email, name, role, branch_id, phone) VALUES
('550e8400-e29b-41d4-a716-*********010', '<EMAIL>', 'System Administrator', 'admin', NULL, '******-0110'),
('550e8400-e29b-41d4-a716-*********011', '<EMAIL>', 'John Manager', 'manager', '550e8400-e29b-41d4-a716-*********001', '******-0111'),
('550e8400-e29b-41d4-a716-*********012', '<EMAIL>', 'Jane Manager', 'manager', '550e8400-e29b-41d4-a716-*********002', '******-0112'),
('550e8400-e29b-41d4-a716-*********013', '<EMAIL>', 'Bob Cashier', 'cashier', '550e8400-e29b-41d4-a716-*********001', '******-0113'),
('550e8400-e29b-41d4-a716-*********014', '<EMAIL>', 'Alice Cashier', 'cashier', '550e8400-e29b-41d4-a716-*********002', '******-0114'),
('550e8400-e29b-41d4-a716-*********015', '<EMAIL>', 'Chef Mike', 'kitchen_staff', '550e8400-e29b-41d4-a716-*********001', '******-0115'),
('550e8400-e29b-41d4-a716-*********016', '<EMAIL>', 'Tom Sales', 'sales_rep', '550e8400-e29b-41d4-a716-*********001', '******-0116'),
('550e8400-e29b-41d4-a716-*********017', '<EMAIL>', 'Sam Warehouse', 'warehouse_staff', '550e8400-e29b-41d4-a716-*********001', '******-0117');

-- Update branch managers
UPDATE branches SET manager_id = '550e8400-e29b-41d4-a716-*********011' WHERE id = '550e8400-e29b-41d4-a716-*********001';
UPDATE branches SET manager_id = '550e8400-e29b-41d4-a716-*********012' WHERE id = '550e8400-e29b-41d4-a716-*********002';

-- Insert sample territories
INSERT INTO territories (id, name, description, boundaries, sales_rep_id) VALUES
('550e8400-e29b-41d4-a716-*********020', 'Downtown Territory', 'Downtown and surrounding areas', 
 ST_GeogFromText('POLYGON((-74.02 40.70, -73.99 40.70, -73.99 40.73, -74.02 40.73, -74.02 40.70))'), 
 '550e8400-e29b-41d4-a716-*********016');

-- Update sales rep territory
UPDATE users SET territory_id = '550e8400-e29b-41d4-a716-*********020' WHERE id = '550e8400-e29b-41d4-a716-*********016';

-- Insert sample categories
INSERT INTO categories (id, name, description) VALUES
('550e8400-e29b-41d4-a716-*********030', 'Beverages', 'All types of drinks'),
('550e8400-e29b-41d4-a716-*********031', 'Main Courses', 'Primary dishes and meals'),
('550e8400-e29b-41d4-a716-*********032', 'Appetizers', 'Starters and small plates'),
('550e8400-e29b-41d4-a716-*********033', 'Desserts', 'Sweet treats and desserts'),
('550e8400-e29b-41d4-a716-*********034', 'Ingredients', 'Raw materials and ingredients'),
('550e8400-e29b-41d4-a716-*********035', 'Hot Beverages', 'Coffee, tea, and hot drinks');

-- Insert subcategories
INSERT INTO categories (id, name, description, parent_id) VALUES
('550e8400-e29b-41d4-a716-*********036', 'Coffee', 'Coffee-based beverages', '550e8400-e29b-41d4-a716-*********035'),
('550e8400-e29b-41d4-a716-*********037', 'Tea', 'Tea-based beverages', '550e8400-e29b-41d4-a716-*********035');

-- Insert sample products
INSERT INTO products (id, name, description, category_id, sku, price, cost, unit, is_manufactured) VALUES
('550e8400-e29b-41d4-a716-*********040', 'Espresso', 'Strong coffee shot', '550e8400-e29b-41d4-a716-*********036', 'BEV-ESP-001', 2.50, 0.75, 'cup', true),
('550e8400-e29b-41d4-a716-*********041', 'Cappuccino', 'Espresso with steamed milk', '550e8400-e29b-41d4-a716-*********036', 'BEV-CAP-001', 4.50, 1.25, 'cup', true),
('550e8400-e29b-41d4-a716-*********042', 'Latte', 'Espresso with steamed milk and foam', '550e8400-e29b-41d4-a716-*********036', 'BEV-LAT-001', 5.00, 1.50, 'cup', true),
('550e8400-e29b-41d4-a716-*********043', 'Green Tea', 'Fresh green tea', '550e8400-e29b-41d4-a716-*********037', 'BEV-GT-001', 3.00, 0.50, 'cup', false),
('550e8400-e29b-41d4-a716-*********044', 'Caesar Salad', 'Fresh romaine with caesar dressing', '550e8400-e29b-41d4-a716-*********032', 'APP-CS-001', 8.50, 3.00, 'plate', true),
('550e8400-e29b-41d4-a716-*********045', 'Grilled Chicken', 'Seasoned grilled chicken breast', '550e8400-e29b-41d4-a716-*********031', 'MAIN-GC-001', 15.00, 6.00, 'plate', true),
('550e8400-e29b-41d4-a716-*********046', 'Chocolate Cake', 'Rich chocolate layer cake', '550e8400-e29b-41d4-a716-*********033', 'DES-CC-001', 6.50, 2.50, 'slice', true),
('550e8400-e29b-41d4-a716-*********047', 'Coffee Beans', 'Premium arabica coffee beans', '550e8400-e29b-41d4-a716-*********034', 'ING-CB-001', 12.00, 8.00, 'lb', false),
('550e8400-e29b-41d4-a716-*********048', 'Milk', 'Fresh whole milk', '550e8400-e29b-41d4-a716-*********034', 'ING-MLK-001', 3.50, 2.00, 'gallon', false),
('550e8400-e29b-41d4-a716-*********049', 'Chicken Breast', 'Fresh chicken breast', '550e8400-e29b-41d4-a716-*********034', 'ING-CHB-001', 8.00, 5.00, 'lb', false);

-- Insert sample customers
INSERT INTO customers (id, name, email, phone, address, location, loyalty_points) VALUES
('550e8400-e29b-41d4-a716-*********050', 'John Doe', '<EMAIL>', '******-0150', '123 Customer St, Downtown', ST_GeogFromText('POINT(-74.005 40.7127)'), 150),
('550e8400-e29b-41d4-a716-*********051', 'Jane Smith', '<EMAIL>', '******-0151', '456 Client Ave, Uptown', ST_GeogFromText('POINT(-73.9856 40.7483)'), 75),
('550e8400-e29b-41d4-a716-*********052', 'Bob Johnson', '<EMAIL>', '******-0152', '789 Buyer Blvd, Westside', ST_GeogFromText('POINT(-74.0058 40.7588)'), 200),
('550e8400-e29b-41d4-a716-*********053', 'Alice Brown', '<EMAIL>', '******-0153', '321 Patron Place, Downtown', ST_GeogFromText('POINT(-74.007 40.7129)'), 50);

-- Insert sample suppliers
INSERT INTO suppliers (id, name, contact_person, email, phone, address, location, rating) VALUES
('550e8400-e29b-41d4-a716-*********060', 'Coffee Supreme', 'Mike Coffee', '<EMAIL>', '******-0160', '100 Bean St, Coffee District', ST_GeogFromText('POINT(-74.010 40.7100)'), 4.5),
('550e8400-e29b-41d4-a716-*********061', 'Fresh Dairy Co', 'Sarah Milk', '<EMAIL>', '******-0161', '200 Dairy Rd, Farm District', ST_GeogFromText('POINT(-74.020 40.7200)'), 4.2),
('550e8400-e29b-41d4-a716-*********062', 'Premium Meats', 'Tom Butcher', '<EMAIL>', '******-0162', '300 Meat Ave, Industrial Zone', ST_GeogFromText('POINT(-74.030 40.7300)'), 4.8);

-- Insert sample tables for branches
INSERT INTO tables (id, branch_id, table_number, capacity, location_x, location_y) VALUES
('550e8400-e29b-41d4-a716-*********070', '550e8400-e29b-41d4-a716-*********001', 'T01', 4, 100, 100),
('550e8400-e29b-41d4-a716-*********071', '550e8400-e29b-41d4-a716-*********001', 'T02', 2, 200, 100),
('550e8400-e29b-41d4-a716-*********072', '550e8400-e29b-41d4-a716-*********001', 'T03', 6, 300, 100),
('550e8400-e29b-41d4-a716-*********073', '550e8400-e29b-41d4-a716-*********001', 'T04', 4, 100, 200),
('550e8400-e29b-41d4-a716-*********074', '550e8400-e29b-41d4-a716-*********002', 'T01', 4, 100, 100),
('550e8400-e29b-41d4-a716-*********075', '550e8400-e29b-41d4-a716-*********002', 'T02', 2, 200, 100),
('550e8400-e29b-41d4-a716-*********076', '550e8400-e29b-41d4-a716-*********002', 'T03', 8, 300, 100);

-- Insert sample inventory
INSERT INTO inventory (product_id, branch_id, quantity, reorder_level, max_stock_level) VALUES
('550e8400-e29b-41d4-a716-*********047', '550e8400-e29b-41d4-a716-*********001', 50, 10, 100),
('550e8400-e29b-41d4-a716-*********048', '550e8400-e29b-41d4-a716-*********001', 20, 5, 50),
('550e8400-e29b-41d4-a716-*********049', '550e8400-e29b-41d4-a716-*********001', 30, 8, 60),
('550e8400-e29b-41d4-a716-*********047', '550e8400-e29b-41d4-a716-*********002', 45, 10, 100),
('550e8400-e29b-41d4-a716-*********048', '550e8400-e29b-41d4-a716-*********002', 15, 5, 50),
('550e8400-e29b-41d4-a716-*********049', '550e8400-e29b-41d4-a716-*********002', 25, 8, 60);

-- Insert sample recipes
INSERT INTO recipes (id, product_id, name, description, yield_quantity, preparation_time, instructions, cost_per_unit) VALUES
('550e8400-e29b-41d4-a716-*********080', '550e8400-e29b-41d4-a716-*********040', 'Espresso Recipe', 'Perfect espresso shot', 1, 2, '1. Grind 18g coffee beans\n2. Tamp evenly\n3. Extract for 25-30 seconds', 0.75),
('550e8400-e29b-41d4-a716-*********081', '550e8400-e29b-41d4-a716-*********041', 'Cappuccino Recipe', 'Classic cappuccino', 1, 4, '1. Prepare espresso shot\n2. Steam milk to 150°F\n3. Pour steamed milk over espresso\n4. Top with foam', 1.25),
('550e8400-e29b-41d4-a716-*********082', '550e8400-e29b-41d4-a716-*********044', 'Caesar Salad Recipe', 'Fresh caesar salad', 1, 8, '1. Chop romaine lettuce\n2. Prepare caesar dressing\n3. Toss lettuce with dressing\n4. Add croutons and parmesan', 3.00);

-- Insert recipe ingredients
INSERT INTO recipe_ingredients (recipe_id, ingredient_id, quantity, unit, cost) VALUES
('550e8400-e29b-41d4-a716-*********080', '550e8400-e29b-41d4-a716-*********047', 0.04, 'lb', 0.48),
('550e8400-e29b-41d4-a716-*********081', '550e8400-e29b-41d4-a716-*********047', 0.04, 'lb', 0.48),
('550e8400-e29b-41d4-a716-*********081', '550e8400-e29b-41d4-a716-*********048', 0.02, 'gallon', 0.07);

-- Update products with recipe references
UPDATE products SET recipe_id = '550e8400-e29b-41d4-a716-*********080' WHERE id = '550e8400-e29b-41d4-a716-*********040';
UPDATE products SET recipe_id = '550e8400-e29b-41d4-a716-*********081' WHERE id = '550e8400-e29b-41d4-a716-*********041';
UPDATE products SET recipe_id = '550e8400-e29b-41d4-a716-*********082' WHERE id = '550e8400-e29b-41d4-a716-*********044';

-- Insert sample orders
INSERT INTO orders (id, order_number, customer_id, branch_id, cashier_id, order_type, subtotal, tax_amount, total_amount, payment_method, status) VALUES
('550e8400-e29b-41d4-a716-*********090', 'ORD-001-2024', '550e8400-e29b-41d4-a716-*********050', '550e8400-e29b-41d4-a716-*********001', '550e8400-e29b-41d4-a716-*********013', 'dine_in', 12.00, 1.20, 13.20, 'card', 'delivered'),
('550e8400-e29b-41d4-a716-*********091', 'ORD-002-2024', '550e8400-e29b-41d4-a716-*********051', '550e8400-e29b-41d4-a716-*********002', '550e8400-e29b-41d4-a716-*********014', 'takeaway', 9.50, 0.95, 10.45, 'cash', 'delivered'),
('550e8400-e29b-41d4-a716-*********092', 'ORD-003-2024', '550e8400-e29b-41d4-a716-*********052', '550e8400-e29b-41d4-a716-*********001', '550e8400-e29b-41d4-a716-*********013', 'delivery', 18.50, 1.85, 20.35, 'digital_wallet', 'pending');

-- Insert order items
INSERT INTO order_items (order_id, product_id, quantity, unit_price, total_price) VALUES
('550e8400-e29b-41d4-a716-*********090', '550e8400-e29b-41d4-a716-*********041', 2, 4.50, 9.00),
('550e8400-e29b-41d4-a716-*********090', '550e8400-e29b-41d4-a716-*********043', 1, 3.00, 3.00),
('550e8400-e29b-41d4-a716-*********091', '550e8400-e29b-41d4-a716-*********040', 2, 2.50, 5.00),
('550e8400-e29b-41d4-a716-*********091', '550e8400-e29b-41d4-a716-*********044', 1, 8.50, 8.50),
('550e8400-e29b-41d4-a716-*********092', '550e8400-e29b-41d4-a716-*********045', 1, 15.00, 15.00),
('550e8400-e29b-41d4-a716-*********092', '550e8400-e29b-41d4-a716-*********046', 1, 6.50, 6.50);

-- Fix order totals (triggers should handle this, but ensuring consistency)
UPDATE orders SET subtotal = 12.00, total_amount = 13.20 WHERE id = '550e8400-e29b-41d4-a716-*********090';
UPDATE orders SET subtotal = 13.50, total_amount = 14.45 WHERE id = '550e8400-e29b-41d4-a716-*********091';
UPDATE orders SET subtotal = 21.50, total_amount = 23.35 WHERE id = '550e8400-e29b-41d4-a716-*********092';
